{"name": "vue-admin-template", "version": "4.4.0", "description": "A vue admin template with Element UI & axios & iconfont & permission control & lint", "author": "Pan <<EMAIL>>", "scripts": {"dev": "node --max_old_space_size=4096 node_modules/@vue/cli-service/bin/vue-cli-service.js serve", "build:prod": "node --max_old_space_size=4096 node_modules/@vue/cli-service/bin/vue-cli-service.js build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "lint": "eslint --ext .js,.vue src", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit"}, "dependencies": {"@inksyun/utils": "1.3.1", "@inksyun/webcomp": "1.2.30", "@jiaminghi/data-view": "2.10.0", "@wangeditor/editor": "5.1.23", "@wangeditor/editor-for-vue": "1.0.2", "axios": "0.22.0", "core-js": "3.6.5", "crypto-js": "4.0.0", "dayjs": "1.8.16", "driver.js": "0.9.8", "echarts": "4.9.0", "element-ui": "2.15.6", "file-saver": "2.0.5", "jexcel": "4.6.1", "js-cookie": "2.2.0", "js-pinyin": "0.1.9", "mammoth": "1.4.19", "normalize.css": "7.0.0", "nprogress": "0.2.0", "number-precision": "1.5.1", "path-to-regexp": "2.4.0", "qrcodejs2": "0.0.2", "screenfull": "4.2.0", "v-charts": "1.19.0", "v-distpicker": "1.3.3", "vue": "2.6.10", "vue-axios": "3.5.2", "vue-count-to": "1.0.13", "vue-easytable": "2.27.1", "vue-i18n": "8.2.1", "vue-json-editor": "1.4.3", "vue-router": "3.0.6", "vue-super-flow": "1.4.0", "vuedraggable": "2.24.3", "vuex": "3.1.0", "xlsx": "0.17.5"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.4", "@vue/cli-plugin-eslint": "4.4.4", "@vue/cli-service": "4.4.4", "autoprefixer": "9.5.1", "babel-eslint": "10.1.0", "babel-jest": "23.6.0", "babel-plugin-dynamic-import-node": "2.3.3", "connect": "3.6.6", "eslint": "6.7.2", "eslint-plugin-vue": "6.2.2", "html-webpack-plugin": "3.2.0", "lrz": "4.9.41", "mockjs": "1.0.1-beta3", "runjs": "4.3.2", "sass": "1.26.8", "sass-loader": "8.0.2", "script-ext-html-webpack-plugin": "2.1.3", "script-loader": "0.7.2", "serve-static": "1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.2", "vue-template-compiler": "2.6.10"}, "browserslist": ["> 1%", "last 2 versions"], "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "jest": {"config": "./jest.config.js"}, "license": "MIT"}