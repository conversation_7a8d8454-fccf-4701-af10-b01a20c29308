import store from "@/store";
import { Base64 } from "js-base64";
let wsConnection = {
  $ws: null,
  lockReturn: false,
  timeout: 60 * 1000 * 5,
  timeoutObj: null,
  timeoutNum: null,
  serverTimeoutObj: null,
  //初始化webSocket长连接
  initWebSocket: function (wsurl) {
    store.commit("webSocketMsg/getWsurl", wsurl);
    this.$ws = new WebSocket(wsurl); //写入地址 这里的地址可以在initWebSocket方法加入参数
    this.$ws.onopen = this.wsOpen;
    this.$ws.onclose = this.wsClose;
    this.$ws.onmessage = this.wsMsg;
    this.$ws.onerror = this.wsError;
  },

  //打开websocket
  wsOpen: function (e) {
    // console.log(e.currentTarget.url)
    // const mqttsec = store.state.app.config.mqttsec;
    // const [username, password] = Base64.decode(mqttsec).split(";");
    // const connectPacket = JSON.stringify({username,password});
    // wsConnection.wsSend(connectPacket);
    //开始websocket心跳
    wsConnection.startWsHeartbeat();
    store.commit("webSocketMsg/getState", "连接成功");
  },
  wsClose: function (e) {
    // console.log(e, 'ws close，连接关闭')
    store.commit("webSocketMsg/getState", "连接关闭");
  },
  wsMsg: function (msg) {
    //每次接收到服务端消息后 重置websocket心跳
    wsConnection.resetHeartbeat();
    // console.log('获取消息',msg)
    //服务端发送来的消息存到vuex
    store.commit("webSocketMsg/getWsMsg", msg.data);
  },
  wsError: function (err) {
    store.commit("webSocketMsg/getState", "连接错误");
    wsConnection.reconnect();
  },
  wsSend: function (e) {
    console.log('wsSend',e)
    this.$ws.send(e);
  },
  //重启websocket
  reconnect: function () {
    let _this = this;
    if (_this.lockReturn) {
      return;
    }
    _this.lockReturn = true;
    _this.timeoutNum && clearTimeout(_this.timeoutNum);
    _this.timeoutNum = setTimeout(function () {
      _this.initWebSocket();
      _this.lockReturn = false;
    }, 3000);
  },
  // 开始websocket心跳
  startWsHeartbeat: function () {
    let _this = this;
    _this.timeoutObj && clearTimeout(_this.timeoutObj);
    _this.serverTimeoutObj && clearTimeout(_this.serverTimeoutObj);
    _this.timeoutObj = setInterval(function () {
      //判断websocket当前状态
      if (_this.$ws.readyState != 1) {
        _this.reconnect();
      }
    }, _this.timeout);
  },
  //重置websocket心跳
  resetHeartbeat: function () {
    let _this = this;
    clearTimeout(_this.timeoutObj);
    clearTimeout(_this.serverTimeoutObj);
    _this.startWsHeartbeat();
  },
};

//抛出websocket对象
export default wsConnection;
