import NP from "number-precision";
// 定义全局方法 main.js
//  Vue.prototype.funcName = function (){}
//调用  this.funcName();
export function transData(a, idStr, pidStr, chindrenStr) {
  var r = [],
    hash = {},
    id = idStr,
    pid = pidStr,
    children = chindrenStr,
    i = 0,
    j = 0,
    len = a.length;
  for (; i < len; i++) {
    hash[a[i][id]] = a[i];
  }
  for (; j < len; j++) {
    var aVal = a[j],
      hashVP = hash[aVal[pid]];
    if (hashVP) {
      !hashVP[children] && (hashVP[children] = []);
      hashVP[children].push(aVal);
    } else {
      r.push(aVal);
    }
  }
  return r;
}
// 格式化日期
export function dateFormats(dataStr) {
  if (!dataStr) {
    return;
  }
  var dt = new Date(dataStr);
  var y = dt.getFullYear();
  var m = (dt.getMonth() + 1).toString().padStart(2, "0");
  var d = dt.getDate().toString().padStart(2, "0");
  var hh = dt.getHours().toString().padStart(2, "0");
  var mm = dt.getMinutes().toString().padStart(2, "0");
  var ss = dt.getSeconds().toString().padStart(2, "0");
  return `${y}-${m}-${d} ${hh}:${mm}:${ss}`;
}
// 获取日期 从1月1日开始
export function getDate() {
  let curDate = new Date();
  let y = curDate.getFullYear();
  let m = (curDate.getMonth() + 1).toString().padStart(2, "0");
  var d = curDate.getDate().toString().padStart(2, "0");
  // var StartDate =`${y}-`+"01-01 00:00:00"
  if((curDate.getMonth() + 1)>6){
    // 当前月份在7-12
    var StartDate =`${y}-`+"01-01 00:00:00"
  }else{
      // 当前月份在1-6
      var StartDate =`${y-1}-`+"06-01 00:00:00"
  }
  var EndDate = `${y}-${m}-${d} ${23}:${59}:${59}`
  return [StartDate, EndDate];
}
// 时间处理 转${23}:${59}:${59}
export function filterDate(val) {
  var dt = new Date(val);
  var y = dt.getFullYear();
  var m = (dt.getMonth() + 1).toString().padStart(2, "0");
  var d = dt.getDate().toString().padStart(2, "0");
  return `${y}-${m}-${d} ${23}:${59}:${59}`;
}
// 时间处理 转00:00:00
export function filterStartDate(val) {
  var dt = new Date(val);
  var y = dt.getFullYear();
  var m = (dt.getMonth() + 1).toString().padStart(2, "0");
  var d = dt.getDate().toString().padStart(2, "0");
  return `${y}-${m}-${d}`+" 00:00:00";
}
// 时间比较 timeComparison
export function timeComparison(val, nextval) {
  var firstVal = new Date(val).getTime();
  var lastVal = new Date(nextval).getTime();
  if (firstVal >= lastVal) {
    return true
  } else {
    return false
  }
  // return `${y}-${m}-${d} ${23}:${59}:${59}`;
}
// 时间-快捷选择
export function pickerOptions() {
  var option = {
    shortcuts: [{
      text: '最近一周',
      onClick(picker) {
        const end = new Date();
        const start = new Date();
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
        picker.$emit('pick', [start, end]);
      }
    }, {
      text: '最近一个月',
      onClick(picker) {
        const end = new Date();
        const start = new Date();
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
        picker.$emit('pick', [start, end]);
      }
    }, {
      text: '最近三个月',
      onClick(picker) {
        const end = new Date();
        const start = new Date();
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
        picker.$emit('pick', [start, end]);
      }
    },
    {
      text: '本年至今',
      onClick(picker) {
        const end = new Date();
        const start = new Date(new Date().getFullYear(), 0);
        picker.$emit('pick', [start, end]);
      }
    }]
  }
  return option
}
// 本周 7天
export function setWeekDate() {
  var date = new Date();
  var today = date.getDay();
  var stepSunDay = -today + 1;
  if (today == 0) {
    stepSunDay = -7;
  }
  var stepMonday = 7 - today;
  var time = date.getTime();
  var monday = new Date(time + stepSunDay * 24 * 3600 * 1000);
  var sunday = new Date(time + stepMonday * 24 * 3600 * 1000);
  var StartDate = dateFormats(monday); // 日期变换
  var EndDate = dateFormats(sunday); // 日期变换
  return [StartDate, EndDate];
}
// 本月第一天,至今
export function setMonthDate() {
  let curDate = new Date();
  let y = curDate.getFullYear();
  let m = curDate.getMonth() + 1;
  if (m > 12) {
    m = 1;
    y++;
  }
  let monthLastDay = new Date(y, m, 0).getDate();
  var StartDate = y + "-" + (m < 10 ? "0" + m : m) + "-" + "01 00:00:00";
  var EndDate = y + "-" + (m < 10 ? "0" + m : m) + "-" + (monthLastDay < 10 ? "0" + monthLastDay : monthLastDay) + ' 23:59:59';
  return [StartDate, EndDate];
}
// 获取当前年的日期
export function setYearDate() {
  let curDate = new Date();
  let y = curDate.getFullYear();
  var StartDate = y + "-01-01 00:00:00";
  var EndDate = `${y}-${12}-${31} ${23}:${59}:${59}`
  return [StartDate, EndDate];
}
// 获取当前月份日期
export function getMonthDate() {
  let curDate = new Date();
  let y = curDate.getFullYear();
  let m = (curDate.getMonth() + 1).toString().padStart(2, "0");
  var d = curDate.getDate().toString().padStart(2, "0");
  let monthLastDay = new Date(y, m, 0).getDate().toString().padStart(2, "0"); //获取本月最后一天
  var StartDate = y + '-' + m + "-01 00:00:00";
  var EndDate = `${y}-${m}-${monthLastDay} ${23}:${59}:${59}`
  return [StartDate, EndDate];
}
// 获取上一个月份日期
export function getPrevMonthDate() {
  var nowdays = new Date();
  var year = nowdays.getFullYear();
  var month = nowdays.getMonth();
  if(month==0){
      month = 12;
      year = year-1;
  }
  if(month<10){
      month = '0'+month;
  }
  var myDate = new Date(year,month,0);

  var StartDate = year+'-'+month+'-01 00:00:00'; //上个月第一天
  var EndDate = year+'-'+month+'-'+myDate.getDate()+' 23:59:59';
  return [StartDate, EndDate];
}

// 获取两个日期之间的间隔日期
export function getBetweenDate(startTime, endTime) {
  let start = new Date(startTime)
  let end = new Date(endTime)
  const resultTime = []
  // 当 开始时间小于结束时间的时候进入循环
  while (start <= end) {
    let getDay = start.getDate()
    // 月份需要加 1
    let getMonth = start.getMonth() + 1
    let getYear = start.getFullYear()
    /**
     * 拼接时间格式
     * (getMonth >= 10 ? `${getMonth}` : `0${getMonth}`) 自动给 小于10的时间前面补0
     */
    let setTime =
      `${getYear}-` +
      (getMonth >= 10 ? `${getMonth}` : `0${getMonth}`) +
      '-' +
      (getDay >= 10 ? `${getDay}` : `0${getDay}`)

    resultTime.push(setTime)
    /**
     * 重新设置开始时间
     * 使用 setFullYear() 方法会自动将时间累加，返回的是时间戳格式
     * 使用 new Date() 将时间重新设置为标准时间
     * getMonth - 1 将月份时间重新还原
     */
    start = new Date(start.setFullYear(getYear, getMonth - 1, getDay + 1))
  }
  return resultTime
}
// 格式化时间json
export function handleData(data) {
  let newArr = [];
  let n = 0;
  let yearMonths = "";
  let first = data[0].split("-");
  // yearMonths = first[0] + "年" + first[1] + "月";
  newArr[n] = [data[0]];
  // newArr[n].yearMonths = yearMonths; 
  // 转数组
  for (let i = 1; i < data.length; i++) {
    let current = data[i].split("-");
    let before = data[i - 1].split("-");
    // yearMonths = current[0] + "年" + current[1] + "月";
    if (current[0] === before[0] && current[1] === before[1]) {
      newArr[n].push(data[i]);
    } else {
      n++;
      newArr[n] = [];
      // newArr[n].yearMonths=yearMonths;
      newArr[n].push(data[i]);
    }
  }
  // 再次转json
  // console.log('newArr', newArr)
  if (newArr.length == 0) {
    return [];
  }
  var dateformat = [];
  for (var i = 0; i < newArr.length; i++) {
    var Item = newArr[i][0]
    let year = Item.split("-")[0];
    let month = Item.split("-")[1];
    var obj = {
      yearMonths: year + "-" + month,
      item: [],
    };
    for (var a = 0; a < newArr[i].length; a++) {
      let month = newArr[i][a].split("-")[1];
      let day = newArr[i][a].split("-")[2];

      var itemobj = {
        itemcode: 'd' + month + day,
        itemname: day,
        isweekend: 0
      };
      var isweek = new Date(newArr[i][a]).getDay()
      if (isweek == 0 || isweek == 6) {
        itemobj.isweekend = 1
      }
      obj.item.push(itemobj);
    }
    dateformat.push(obj);
  }
  return dateformat
}

//===========================================
// 进度条
export function schedule(finishqty, quantity) {
  finishqty = parseFloat(finishqty);
  quantity = parseFloat(quantity);
  if (isNaN(finishqty) || isNaN(quantity)) {
    return 0;
  }
  return quantity <= 0
    ? 0
    : Math.round((finishqty / quantity) * 10000) / 100.0;
}
export function prformat(percentage) {
  return `${percentage}`;
}
// 合计行计算
export function getSummariesTotal(param, Arr,position) {
  const { columns, data } = param;
  if (!position) {
    position = 1
  }
  var arr = Arr;
  const sums = [];
  columns.forEach((column, index) => {
    if (index === position) {
      sums[index] = "合计";
      return;
    }
    var bor = false;
    if (
      arr.length > 0 &&
      arr.find((item) => item == column.property) != undefined
    ) {
      bor = true;
    }
    const values = data.map((item) => Number(item[column.property]));
    if (!values.every((value) => isNaN(value)) && bor) {
      sums[index] = values.reduce((prev, curr) => {
        const value = Number(curr);
        if (!isNaN(value)) {
          return NP.plus(Number(prev), Number(curr));
        } else {
          return Number(prev);
        }
      }, 0);
    } else {
      sums[index] = "";
    }
  });
  return sums;
}

/**
* 乘法运算，避免数据相乘小数点后产生多位数和计算精度损失。
*
* @param num1被乘数 | num2乘数
*/
export function numMulti(num1, num2) {
  num1 = num1 || 0
  num2 = num2 || 0

  let baseNum = 0
  try {
    baseNum += num1.toString().split('.')[1].length
  } catch (e) {
  }
  try {
    baseNum += num2.toString().split('.')[1].length
  } catch (e) {
  }
  return Number(num1.toString().replace('.', '')) * Number(num2.toString().replace('.', '')) / Math.pow(10, baseNum)
}
//格式化货币
// const formatMoney = (money) => {
//   return money.replace(new RegExp(`(?!^)(?=(\\d{3})+${money.includes('.') ? '\\.' : '$'})`, 'g'), ',')  
// }
export function filitsort(val, datasheet) {
  var goods = ['goodsuid', 'goodsname', 'goodsspec', 'goodsunit', 'partid', 'goodsid']
  var group = ['groupuid', 'groupname', 'groupid',]
  if (goods.indexOf(val.prop)) {
    return 'Mat_Goods.' + val.prop
  }
  if (group.indexOf(val.prop)) {
    return 'App_Workgroup.' + val.prop
  }

  return datasheet + '.' + val.prop
}

/**
* 表格排序
* val 需要排序的字段
* tableForm 表格列内容
*/
export function tablesort(val, tableForm) {
  var index = tableForm.item.findIndex(
    (item) => item.itemcode == val.prop
  );
  if (index > -1) {
    return tableForm.item[index].datasheet ? tableForm.item[index].datasheet : ''
  } else {
    return ''
  }
}
//isTrue 0获取范围值 1时间值
export function filterDateRange(val,isTrue) {
  var dateRangeMap = {
   '逾期': [
     getDate()[0],
     filterDate(new Date().setDate(new Date().getDate() - 1)),
   ],
   '今天': [filterStartDate(new Date()), filterDate(new Date())],
   '明天': [
     filterStartDate(
       new Date(new Date().setDate(new Date().getDate() + 1))
     ),
     filterDate(new Date(new Date().setDate(new Date().getDate() + 1))),
   ],
   '后天': [
     filterStartDate(
       new Date(new Date().setDate(new Date().getDate() + 2))
     ),
     filterDate(new Date(new Date().setDate(new Date().getDate() + 2))),
   ],
 };
 var dateMap={
  '逾期': new Date().setDate(new Date().getDate() - 1),
   '今天': new Date(),
   '明天': new Date(new Date().setDate(new Date().getDate() + 1)),
   '后天': new Date(new Date().setDate(new Date().getDate() + 2)),
 }
 if(isTrue){
   return dateFormats(dateMap[val]);
 }
 return dateRangeMap[val];
}
