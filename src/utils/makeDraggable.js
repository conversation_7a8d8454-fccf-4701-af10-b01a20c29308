async function makeDraggable(row, element, container = document.body,executeFun) {
    let isDragging = false;
    let offsetX, offsetY;
    let longPressTimer;
    let createdDiv = null;
    let isSuccess = false;
  
    longPressTimer = setTimeout(() => {
        isDragging = true;
      

        offsetX = element.clientX - element.offsetLeft;
        offsetY = element.clientY - element.offsetTop;
  
        createdDiv = document.createElement("div");
        createdDiv.className = "draggable-text";
        createdDiv.textContent = row.refno || "单据编码";
        createdDiv.style.position = "fixed";
        createdDiv.style.padding = "10px";
        createdDiv.style.border = "1px solid #333";
        createdDiv.style.background = "#FFF";
        createdDiv.style.zIndex = "9999";
        container.appendChild(createdDiv);

        // 设置 Div 初始位置
        createdDiv.style.left = `${element.clientX}px`;
        createdDiv.style.top = `${element.clientY}px`;
        element.preventDefault();
      }, 500);
    
    // const mousemoveHandler=()=>{
       
    // }  
      document.addEventListener("mousemove", (e) => {
        if (isDragging) {
            e.preventDefault();
            document.body.classList.add("no-select"); // 开始拖拽时禁用文本选择
            container.classList.add("select-area");
            if (createdDiv) {
              createdDiv.style.left = `${e.clientX}px`;
              createdDiv.style.top = `${e.clientY}px`;
            }
          }
      });
      const mouseupHandler = () => {
        isDragging = false;
        document.body.classList.remove("no-select"); // 结束拖拽时恢复文本选择
        container.classList.remove("select-area");
        // 清除定时器
        clearTimeout(longPressTimer);
        if (createdDiv) {
           
          const containerRect = container.getBoundingClientRect();

          const elementRect = createdDiv.getBoundingClientRect();
  
          isSuccess= (elementRect.left >= containerRect.left &&
              elementRect.right <= containerRect.right &&
              elementRect.top >= containerRect.top &&
              elementRect.bottom <= containerRect.bottom
          ) 
          container.removeChild(createdDiv);
          createdDiv = null;
          // isSuccess = true;
        }
        // document.removeEventListener("mousemove", mousemoveHandler);
        document.removeEventListener("mouseup", mouseupHandler);
        if (isSuccess) {
            console.log('拖拽成功')
            executeFun()
        } else {
          console.log('拖拽未成功')
        }
        return
      };
      document.addEventListener("mouseup", mouseupHandler);
  }

  export default makeDraggable;