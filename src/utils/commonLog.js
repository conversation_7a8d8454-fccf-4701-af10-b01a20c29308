import store from "@/store";
import Vue from "vue";
function buryingPointAjax(data) {
  const xhrURL =
    store.state.app.config.env == "dev"
      ? "http://dev.inksyun.com:30471/S21M07B1/tracking" //本地
      : "http://regapi.inksyun.com/S21M07B1/tracking"; //公网
  return new Promise((resolve, reject) => {
    // 创建ajax请求
    const xhr = new XMLHttpRequest();
    xhr.open("post", xhrURL, true);
    xhr.setRequestHeader("Content-Type", "application/json;charset=UTF-8");
    // 发送数据
    xhr.send(data);
  });
}
/**
 * SetTrackingLog 埋点事件
 * code 事件编码
 * desc 事件说明
 * v-TrackingLog="{ code: 'login_submit' }"
 * this.$SetTrackingLog('login_fail', res.data.msg,'/SaUser/login')
 */
function SetTrackingLog(code, desc, pagepath) {
  var saTrackinglogPojo = {
    eventcode: code,
    pagepath: pagepath ? pagepath : "",
    description: desc ? desc : "",
    app: store.state.app.config.title,
    env: store.state.app.config.env,
    // userinfo: "",
  };
  if (!!store.getters.userinfo) {
    saTrackinglogPojo = Object.assign(
      saTrackinglogPojo,
      store.getters.userinfo
    );
    if (store.getters.userinfo.tenantinfo) {
      saTrackinglogPojo.company = store.getters.userinfo.tenantinfo.company;
    }
    //saTrackinglogPojo.userinfo=store.getters.userinfo
  }
  // console.log("saTrackinglogPojo", saTrackinglogPojo);
  buryingPointAjax(JSON.stringify(saTrackinglogPojo));
}
Vue.prototype.$SetTrackingLog = SetTrackingLog;

//通用按钮埋点指令
Vue.directive("TrackingLog", {
  inserted(el, binding, vnode) {
    //按钮点击执行事件
    const handleClick = (el, binding,) => {
      //修饰符
      const idFlag = binding.modifiers.idFlag ? 1 : 0;
      //绑定值
      const data = binding.value;
      //埋点外部传参对象
      // const dynamicInfo = { idFlag, ...data }
      // CapolLog.pointAdd(dynamicInfo,el)
      var isdesc = el.dataset.desc;
      SetTrackingLog(
        data.code,
        !!data.desc ? data.desc : !!isdesc ? isdesc : ""
      );
    };
    const wrappedClickEvent = function (event) {
      handleClick(el, binding, event);
    };
    el.addEventListener("click", wrappedClickEvent);
    el.handleClick = handleClick;
  },
  unbind(el, binding) {
    const handleClick = el.handleClick;
    el.removeEventListener("click", handleClick);
    delete el.handleClick;
  },
});
