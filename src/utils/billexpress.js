import request from "@/utils/request";
import inksutils from '@inksyun/utils';
import Vue from 'vue'
let exprData = [];//自定义公式数据
let modulecode = ""; //记录当前的code
let countfiles = [
    "quantity",
    "price",
    "amount",
    "taxprice",
    "taxamount",
    "itemtaxrate"]
let tgcolumnVal = "";
/**
 * 获取后台自定义公式
 * @param {*} code 
 */
async function getFormula(code) {
    await request.get('/system/SYSM07B15/getListByCode?key=' + code).then(res => {
        if (res.data.code == 200) {
            exprData = res.data.data
        } else {
            exprData = []
        }
    })
}
/**
 * 解析自定义公式exprtemp 数据
 * @param {*} exprtemp 模板字符
 * @param {*} row 单据行数据
 * @returns 
 */
function transExprTemp(exprtemp, row) {
    let keywords = exprtemp.match(/{[^}{]*?}/g);
    keywords = keywords.map((item) => {
        item = item.replace("{", "");
        item = item.replace("}", "");
        return item;
    });
    // 公式
    var objStr = exprtemp;
    // console.log(keywords,objStr)
    for (var i = 0; i < keywords.length; i++) {
        objStr = objStr.replace(
            "${" + keywords[i] + "}",
            "row['" + keywords[i] + "']"
        );
    }
    return objStr
}
// 判断 countfiles是否包含目标值tgcolumn
function setTgcolumnVal(val) {
    var colname = val ? val.toLowerCase() : ""
    return colname
}
/**
 * 自定义计算公式模板
 * @param {*} code 自定义公式modulecode
 * @param {*} row 单据行数据
 * @param {*} colname 修改的字段
 * @returns 返回row
 */
export async function btnExpress(code, row, colname) {
    // 根据code判断是否需要获取后台自定义公式数据
    if (modulecode != code) {
        modulecode = code
        await getFormula(code)
    }
    console.log('exprData',exprData)
    tgcolumnVal = ''; //初始化目标值
    // 判断exprData是否有值
    // console.log('exprData', exprData,row, !!exprData.length)
    if (exprData.length) {
        var copyRow = Object.assign({}, row)
        for (var i = 0; i < exprData.length; i++) {
            var Item = exprData[i];
            var orgcolumns = Item.orgcolumns.split(',');
            // console.log(orgcolumns)
            // 源字段是否包含colname
            if (orgcolumns.includes(colname)) {
                //    目标字段=模板计算后的值

                if (Item.returntype == 1) {
                    // 字符串
                    // copyRow[Item.tgcolumn] = eval(transExprTemp(Item.exprtemp))
                    Vue.set(copyRow, Item.tgcolumn, eval(transExprTemp(Item.exprtemp)))
                } else {
                    // 整数
                    Vue.set(copyRow, Item.tgcolumn, inksutils.count.fomatFloat(eval(transExprTemp(Item.exprtemp)), Item.decnum))
                    // 判断是否包含通用金额计算公式的字段
                    if (countfiles.includes(Item.tgcolumn.toLowerCase())) {
                        tgcolumnVal = setTgcolumnVal(Item.tgcolumn)
                    }
                }

            }
        }
        return copyRow
    } else {
        return row
    }
}
export function getTgcolumnVal() {
    return tgcolumnVal
}