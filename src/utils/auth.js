import Cookies from 'js-cookie'
import request from "@/utils/request";
import store from '@/store'
import { MessageBox } from 'element-ui';
import { set } from 'nprogress';
const TokenKey = 'inks_token' // 令牌key
const RefreshKey = 'inks_refresh_token' //刷新令牌
const rtKey = 'rtkey'
const domain=window.location.hostname
console.log('domain',domain)
import router from '@/router'

export function getToken() {
  return Cookies.get(TokenKey,{domain:domain})
}

export function setToken(token) {
  return Cookies.set(TokenKey, token, {
    expires: 5,
    domain:domain
  })
}

export function removeToken() {
  Cookies.remove(TokenKey,{domain:domain})
  Cookies.remove(Refresh<PERSON>ey,{domain:domain})
  Cookies.remove(rtKey,{domain:domain})
}

//刷新token
export function getRefreshToken() {
  return Cookies.get(RefreshKey,{domain:domain})
}
export function setRefreshToken(token) {
  return Cookies.set(<PERSON>f<PERSON><PERSON><PERSON>, token, {
    expires: 5,
    domain:domain
  })
}

//获取trkey
export function getrtKey() {
  return Cookies.get(rtKey,{domain:domain})
}

export function setrtKey(rtkey) {
  return Cookies.set(rtKey, rtkey, {
    expires: 5,
    domain:domain
  })
}

// 刷新菜单
export function readnav() {
  let baseurl = "/system/SYSM02B2/getMenuWebListBySelf";
  if (store.state.app.config.deffncode) {
    baseurl += "?fncode=" + store.state.app.config.deffncode;
  }
  request
    .get(baseurl)
    .then((response) => {
      if (response.data.code == 200) {
        var navjson = response.data.data;
        localStorage.setItem("navjson", JSON.stringify(navjson));
        store.dispatch("app/setnavdata", navjson);
        this.$mainRouter.push({ path: "/" });
      }
    })
    .catch((error) => {
      console.log(error);
    });
}
//判断当前登录的用户userid 密码是否为弱密码
export function weekPasswordCheck(){
  request.post('/system/SYSM07B17/checkWeakPassword?type=0').then(res=>{
    console.log('checkWeakPassword',res)
      if(res.data.code==200){
        MessageBox.alert('当前密码强度较弱，请尽快修改密码！', '提示', {
          confirmButtonText: '确定',
          type: 'warning'
        })
      }
  }).catch((error) => {
    console.log(error||"弱密码校验失败");
  });
}
// 判断传入密码是否为弱密码
export function isWeakPassword(val){
  return new Promise(async(resolve, reject) => {
    var params = {password:val}
    await request.post('/system/SYSM07B17/isWeakPassword',JSON.stringify(params)).then(res=>{
      resolve(res.data.data)
    }).catch((error) => {
      console.log(error||"弱密码校验失败");
      reject(false)
    });
  })
}
