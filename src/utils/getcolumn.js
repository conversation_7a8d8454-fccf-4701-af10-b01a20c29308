import request from "@/utils/request";
import store from "@/store";
import inksutils from "@inksyun/utils";
var apiCache = inksutils.apiCache.default;
/**
 * 获取SPU属性
 * @param {boolean} useDomian 是否使用域名
 * @returns {Promise<Array>} SPU数据
 */
async function getListByShow(useDomian) {
  try {
    var spuCode = "spu" + useDomian ? "_" + useDomian : "";
    const res = await apiCache.request(
      spuCode,
      async () =>
        await request.get(
          `/goods/D91M01S2/getListByShow?ud=${useDomian ? useDomian : ""}`
        )
    );
    return res.data.code === 200 ? res.data.data : [];
  } catch (error) {
    console.error("获取SPU属性失败:", error);
    return [];
  }
}

/**
 * 获取货品自定义内容
 * @returns {Promise<Array>} 货品自定义内容
 */
async function getGoodsCustomList() {
  try {
    const goodsCode = "GoodsCustom";
    const res = await apiCache.request(
      goodsCode,
      async () => await request.get("/goods/D91M01S4/getListByShow")
    );
    return res.data.code === 200
      ? res.data.data.map((item) => ({
          itemcode: item.custkey,
          itemname: item.custname,
          minwidth: "80",
          displaymark: item.showmark ? 1 : 0,
          overflow: 1,
          editmark: 0,
        }))
      : [];
  } catch (error) {
    console.error("获取货品自定义内容失败:", error);
    return [];
  }
}

/**
 * 获取核算分组
 * @returns {Promise<Array>} 核算分组数据
 */
async function getCostGroupList() {
  try {
    const costCode = "CostGroup";
    const queryparams = {
      pagenum: 1,
      pagesize: 1000,
      ordertype: 1,
      searchtype: 1,
    };
    const res = await apiCache.request(
      costCode,
      async () =>
        await request.post(
          "/sale/D01M02S1/getGroupPageList",
          JSON.stringify(queryparams)
        )
    );
    return res.data.code === 200
      ? res.data.data.list.map((item) => ({
          partgroupid: item.id,
          attrkey: item.grpkey,
          attrname: item.grpname,
        }))
      : [];
  } catch (error) {
    console.error("获取核算分组失败:", error);
    return [];
  }
}

/**
 * 对比并合并列数据
 * @param {Object} dgdata 请求数据
 * @param {Object} orgdata 本地数据
 * @returns {Object} 合并后的数据
 */
function mergeColumnData(dgdata, orgdata) {
  const newArr = { item: [] };
  const orgItems = JSON.parse(JSON.stringify(orgdata.item));

  dgdata.item.forEach((n) => {
    const index = orgItems.findIndex((a) => a.itemcode === n.itemcode);
    if (index !== -1) {
      Object.assign(orgItems[index], {
        minwidth: n.minwidth,
        fixed: n.fixed,
        displaymark: n.displaymark,
      });
      newArr.item.push(orgItems[index]);
      orgItems.splice(index, 1);
    }
  });

  newArr.item.push(...orgItems);
  return newArr;
}

/**
 * 获取列数据
 * @param {string} code 表单代码
 * @param {Object} tableForm 表格表单数据
 * @param {boolean} showspu 是否显示SPU
 * @param {boolean} showgoods 是否显示货品
 * @param {boolean} showcost 是否显示核算分组
 * @param {boolean} useDomian 是否使用域名
 * @returns {Promise<Object>} 列数据
 */
export async function getColumn(
  code,
  tableForm,
  showspu,
  showgoods,
  showcost,
  useDomian
) {
  const userinfo = store.state.user.userinfo || {};
  const permissions = Array.isArray(userinfo.permissions)
    ? userinfo.permissions
    : [];

  const hasCostPermission =
    userinfo.isadmin || permissions.includes("Bus_OrderCost.Cost");

  const [customList, goodsCustomList, costgroupList] = await Promise.all([
    showspu ? getListByShow(useDomian) : [],
    showgoods ? getGoodsCustomList() : [],
    showcost && hasCostPermission ? getCostGroupList() : [],
  ]);

  const colList = { ...tableForm };

  if (showcost && hasCostPermission) {
    costgroupList.forEach((item) => {
      if (!colList.item.some((col) => col.itemcode === item.attrkey)) {
        colList.item.push({
          itemcode: item.attrkey,
          itemname: item.attrname,
          minwidth: "80",
          displaymark: 1,
          overflow: 1,
          editmark: 1,
        });
      }
    });
  }

  if (showspu) {
    customList.forEach((item) => {
      if (!colList.item.some((col) => col.itemcode === item.attrkey)) {
        const newItem = {
          itemcode: item.attrkey,
          itemname: item.attrname,
          minwidth: "80",
          displaymark: item.listshow,
          overflow: 1,
          editmark: 1,
        };
        if (code === "D01M03P1Item") {
          colList.item.splice(-4, 0, newItem);
        } else {
          colList.item.push(newItem);
        }
      }
    });
  }

  if (showgoods) {
    goodsCustomList.forEach((item) => {
      if (!colList.item.some((col) => col.itemcode === item.itemcode)) {
        colList.item.push(item);
      }
    });
  }

  try {
    const res = await request.get(
      `/system/SYSM07B9/getBillEntityByCode?code=${code}`
    );
    if (res.data.code === 200 && res.data.data) {
      console.log("1111");
      const mergedData = mergeColumnData(res.data.data, colList);
      return {
        customList,
        colList: {
          ...mergedData,
          id: res.data.data.id,
          formcode: res.data.data.formcode,
        },
        costgroupList: showcost && hasCostPermission ? costgroupList : [],
        partGroup: showcost && hasCostPermission ? costgroupList : [],
      };
    }
  } catch (error) {
    console.error("获取列数据失败:", error);
  }
  console.log("222", colList);
  return {
    customList,
    colList,
    costgroupList: showcost && hasCostPermission ? costgroupList : [],
    partGroup: showcost && hasCostPermission ? costgroupList : [],
  };
}

/**
 * 初始化列数据
 * @param {string} formcode 表单代码 D04M01M1(自定义表单code)
 * @param {string} subcode 部件code Th/List/Item
 * @param {Object} tableForm 表格表单数据
 * @param {boolean} showspu 是否显示SPU
 * @param {boolean} showgoods 是否显示货品
 * @param {boolean} showcost 是否显示核算分组
 * @param {boolean} useDomian 是否使用域名
 * @returns {Promise<Object>} 初始化后的列数据
 */
export async function initColumns(
  formcode,
  subcode,
  tableForm,
  showspu,
  showgoods,
  showcost,
  useDomian
) {
  debugger;
  const userinfo = store.state.user.userinfo || {};
  const permissions = Array.isArray(userinfo.permissions)
    ? userinfo.permissions
    : [];

  const hasCostPermission =
    userinfo.isadmin || permissions.includes("Bus_OrderCost.Cost");
  const code = formcode + subcode; //D04M01M1Th
  let colList = JSON.parse(JSON.stringify(tableForm)); //防止污染
  const [customList, goodsCustomList, costgroupList] = await Promise.all([
    showspu ? getListByShow(useDomian) : [],
    showgoods ? getGoodsCustomList() : [],
    showcost && hasCostPermission ? getCostGroupList() : [],
  ]);
  let specList = [];
  if (showcost && hasCostPermission) {
    costgroupList.forEach((item) => {
      if (!colList.item.some((col) => col.itemcode === item.attrkey)) {
        const newItem = {
          itemcode: item.attrkey,
          itemname: item.attrname,
          minwidth: "80",
          displaymark: 1,
          overflow: 1,
          editmark: 1,
        };
        colList.item.push(newItem);
        specList.push(newItem);
      }
    });
  }

  if (showspu) {
    customList.forEach((item) => {
      if (!colList.item.some((col) => col.itemcode === item.attrkey)) {
        const newItem = {
          itemcode: item.attrkey,
          itemname: item.attrname,
          minwidth: "80",
          displaymark: item.listshow,
          overflow: 1,
          editmark: 1,
        };
        colList.item.push(newItem);
        specList.push(newItem);
      }
    });
  }

  if (showgoods) {
    goodsCustomList.forEach((item) => {
      if (!colList.item.some((col) => col.itemcode === item.itemcode)) {
        colList.item.push(item);
        specList.push(item);
      }
    });
  }
  const columnData = await apiCache.request(code, async () => {
    debugger;
    try {
      let paramObj = {
        formcode: formcode,
        subcode: subcode,
        item: tableForm.item,
        sensitivecode: tableForm.sensitivecode || "",
      };
      if (specList.length) {
        paramObj.spec = specList;
      }
      const res = await request.post(
        "/system/SYSM07B9/initColumns",
        JSON.stringify(paramObj)
      );
      if (res.data.code === 200 && res.data.data) {
        res.data.data.formcode = code;
        return res.data.data;
      }
    } catch (error) {
      console.error("初始化列数据失败:", error);
    }
  });
  return {
    customList,
    colList: columnData,
    costgroupList: showcost && hasCostPermission ? costgroupList : [],
    partGroup: showcost && hasCostPermission ? costgroupList : [],
  };
}
