import Vue from 'vue'
import {numMulti} from '@/utils/tools'
//将整数部分逢三一断 12345600 过滤为 12，345，600
Vue.filter('NumberFormat', function(value) {
    if (!value) {
      return '0'
    }
    const intPartFormat = value.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,') // 将整数部分逢三一断
    return intPartFormat
  })
  //将数据格式化为金额  123456 过滤为 123,456.00
  Vue.filter('MoneyFormat', function(number, decimals, symbol) {
    if (!decimals) {
      decimals = 2
    }
    if (!symbol) {
      symbol = ''
    }
    const decPoint = '.'
    const thousandsSep = ','
    number = (number + '').replace(/[^0-9+-Ee.]/g, '')
    const n = !isFinite(+number) ? 0 : +number
    const prec = !isFinite(+decimals) ? 0 : Math.abs(decimals)
    const sep = (typeof thousandsSep === 'undefined') ? ',' : thousandsSep
    const dec = (typeof decPoint === 'undefined') ? '.' : decPoint
    let s = ''
    const toFixedFix = function(n, prec) {
      const k = Math.pow(10, prec)
      return '' + numMulti (n, k) / k
    }
    s = (prec ? toFixedFix(n, prec) : '' + Math.round(n)).split('.')
    const re = /(-?\d+)(\d{3})/
    while (re.test(s[0])) {
      s[0] = s[0].replace(re, '$1' + sep + '$2')
    }
    if ((s[1] || '').length < prec) {
      s[1] = s[1] || ''
      s[1] += new Array(prec - s[1].length + 1).join('0')
    }
    var Val=symbol + s.join(dec).replace(/\.00$/, "")
    if(Val==0){Val=''}
    return Val
  })
  // 时间过滤
  Vue.filter('dateFormats', function (value) {
    if (!value) {
      return;
    }
    var dt = new Date(value);
    var y = dt.getFullYear();
    var m = (dt.getMonth() + 1).toString().padStart(2, "0");
    var d = dt.getDate().toString().padStart(2, "0");
    var hh = dt.getHours().toString().padStart(2, "0");
    var mm = dt.getMinutes().toString().padStart(2, "0");
    var ss = dt.getSeconds().toString().padStart(2, "0");
    return `${y}-${m}-${d} ${hh}:${mm}:${ss}`;
  })
  Vue.filter('dateFormat', function (value) {
    if (!value) {
      return;
    }
    var dt = new Date(value);
    var y = dt.getFullYear();
    var m = (dt.getMonth() + 1).toString().padStart(2, "0");
    var d = dt.getDate().toString().padStart(2, "0");
    return `${y}-${m}-${d}`;
  })
  Vue.filter('days', function (value) {
    if (!value) {
      return;
    }
    var dt = new Date(value);
    var m = (dt.getMonth() + 1).toString().padStart(2, "0");
    var d = dt.getDate().toString().padStart(2, "0");
    return `${m}/${d}`;
  })
  