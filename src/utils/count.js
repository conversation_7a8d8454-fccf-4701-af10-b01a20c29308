import store from '@/store'
// 四舍五入 保留位数n
export function fomatFloat(value, n) {
    var f = Math.round(value * Math.pow(10, n)) / Math.pow(10, n);
    var s = f.toString();
    var rs = s.indexOf(".");
    if (rs < 0) {
        s += ".";
    }
    for (var i = s.length - s.indexOf("."); i <= n; i++) {
        s += "0";
    }
    return parseFloat(s);
}
export function amountFloat(data, n) {
    if (!n) {
        n = 2
    }
    var obj = {
        billamount: 0,
        billtaxamount: 0,
        billtaxtotal: 0,
    }
    try {
        data.forEach(item => {
            fomatFloat(obj.billamount += Number(item.amount), n)
            fomatFloat(obj.billtaxamount += Number(item.taxamount), n)
        });
        obj.billtaxtotal = fomatFloat(obj.billtaxamount - obj.billamount, n)
    } catch {
        // this.$message.warning('计算错误，请重新保存')
        return
    }

    return obj;
}
/**
 * 核价计算
 * 2023-06-08 hu
 * @param {*} row 
 * @param {*} colname 
 * @returns 
 */
export function countPriceInput(row, colname) {
    // digit 位数
    var configs = store.getters.userinfo.configs
    var qtyDigit = !!configs["system.dec.quantity"] ? JSON.parse(configs["system.dec.quantity"]) : 4; //数量
    var priceDigit = !!configs["system.dec.price"] ? JSON.parse(configs["system.dec.price"]) : 4; //单价
    var amountDigit = !!configs["system.dec.amount"] ? JSON.parse(configs["system.dec.amount"]) : 2; //金额
    var taxpriceDigit = !!configs["system.dec.taxprice"] ? JSON.parse(configs["system.dec.taxprice"]) : 4; //未税单价
    var taxamountDigit = !!configs["system.dec.taxamount"] ? JSON.parse(configs["system.dec.taxamount"]) : 2; //未税金额
    // 当内容为undefined
    for (var key in row) {
        if (row[key] == undefined) {
            if (key == 'taxamount' || key == 'amount' || key == 'price' || key == 'taxprice' || key == 'itemtaxrate') {
                row[key] = 0
            }
        }
    }
    try {
        switch (colname) {
            case "rebate":
                row.price = fomatFloat((row.stdprice * row.rebate * row.rebatesec), priceDigit)
                row.amount = fomatFloat(row.price * row.quantity, amountDigit)
                // 含税单价=未税单价*（1+税率）
                row.taxprice = fomatFloat((row.price * (1 + row.itemtaxrate * 0.01)), taxpriceDigit)
                //含税金额  = 含税金额 * 1+ 税
                row.taxamount = fomatFloat((row.taxprice * row.quantity), taxamountDigit)
                // 税额 = 含税金额 -未税金额
                row.taxtotal = fomatFloat((row.taxamount - row.amount), taxamountDigit)
                break;
            case "rebatesec":
                row.price = fomatFloat((row.stdprice * row.rebate * row.rebatesec), priceDigit)
                row.amount = fomatFloat(row.price * row.quantity, amountDigit)
                // 含税单价=未税单价*（1+税率）
                row.taxprice = fomatFloat((row.price * (1 + row.itemtaxrate * 0.01)), taxpriceDigit)
                //含税金额  = 含税金额 * 1+ 税
                row.taxamount = fomatFloat((row.taxprice * row.quantity), taxamountDigit)
                // 税额 = 含税金额 -未税金额
                row.taxtotal = fomatFloat((row.taxamount - row.amount), taxamountDigit)
                break;
            case "stdprice":
                row.stdprice = fomatFloat(row.stdprice, priceDigit)
                row.stdamount = fomatFloat((row.stdprice * row.quantity), amountDigit)
                row.price = fomatFloat((row.stdprice * row.rebate * row.rebatesec), priceDigit)
                // 含税单价=未税单价*税率
                row.taxprice = fomatFloat((row.price * (1 + row.itemtaxrate * 0.01)), taxpriceDigit)
                // 未税金额=未税单价*数量
                row.amount = fomatFloat((row.price * row.quantity), amountDigit)
                // 含税金额=含税单价*数量
                row.taxamount = fomatFloat((row.taxprice * row.quantity), taxamountDigit)
                //税额=含税金额-未税金额
                row.taxtotal = fomatFloat((row.taxamount - row.amount), taxamountDigit)
                break;
            case "stdamount":
                row.stdamount = fomatFloat(row.stdamount, amountDigit)
                row.stdprice = fomatFloat((row.stdamount / row.quantity), priceDigit)

                row.price = fomatFloat((row.stdprice * row.rebate * row.rebatesec), priceDigit)
                row.amount = fomatFloat(row.price * row.quantity, amountDigit)
                // 含税单价=未税单价*（1+税率）
                row.taxprice = fomatFloat((row.price * (1 + row.itemtaxrate * 0.01)), taxpriceDigit)
                //含税金额  = 含税金额 * 1+ 税
                row.taxamount = fomatFloat((row.taxprice * row.quantity), taxamountDigit)
                // 税额 = 含税金额 -未税金额
                row.taxtotal = fomatFloat((row.taxamount - row.amount), taxamountDigit)
                break;
            case "amount":
                row.amount = fomatFloat(row.amount, amountDigit)

                row.price = fomatFloat((row.amount / row.quantity), priceDigit)
                
                row.stdprice = fomatFloat((row.price / (row.rebate * row.rebatesec)), priceDigit)
                row.stdamount = fomatFloat(row.stdprice*row.quantity, amountDigit)
                // 含税单价=未税单价*（1+税率）
                row.taxprice = fomatFloat((row.price * (1 + row.itemtaxrate * 0.01)), taxpriceDigit)
                //含税金额  = 含税金额 * 1+ 税
                row.taxamount = fomatFloat((row.taxprice * row.quantity), taxamountDigit)
                // 税额 = 含税金额 -未税金额
                row.taxtotal = fomatFloat((row.taxamount - row.amount), taxamountDigit)
                break;
            case "taxamount":
                row.taxamount = fomatFloat(row.taxamount, taxamountDigit)
                // 含税单价=含税金额/数量
                row.taxprice = fomatFloat((row.taxamount / row.quantity), taxpriceDigit)
                // 单价=未税单价/税率
                row.price = fomatFloat((row.taxprice / (1 + row.itemtaxrate * 0.01)), priceDigit)
                // 金额=单价*数量
                row.amount = fomatFloat((row.price * row.quantity), amountDigit)
                // 税额=含税金额-未税金额
                row.taxtotal = fomatFloat((row.taxamount - row.amount), taxamountDigit)
                row.stdprice = fomatFloat((row.price / (row.rebate * row.rebatesec)), priceDigit)
                row.stdamount = fomatFloat(row.stdprice*row.quantity, amountDigit)
                break;
            case "taxprice":
                row.taxprice = fomatFloat(row.taxprice, taxpriceDigit)
                // 单价
                row.price = fomatFloat((row.taxprice / (1 + row.itemtaxrate * 0.01)), priceDigit)
                // 金额= 单价*数量 
                row.amount = fomatFloat((row.price * row.quantity), amountDigit)
                // 含税金额=含税单价*数量
                row.taxamount = fomatFloat((row.taxprice * row.quantity), taxamountDigit)
                //税额=含税金额-未税金额
                row.taxtotal = fomatFloat((row.taxamount - row.amount), taxamountDigit)
                row.stdprice = fomatFloat((row.price / (row.rebate * row.rebatesec)), priceDigit)
                row.stdamount = fomatFloat(row.stdprice*row.quantity, amountDigit)
                break;
            case "price":
                row.price = fomatFloat(row.price, priceDigit)
                // 含税单价=未税单价*税率
                row.taxprice = fomatFloat((row.price * (1 + row.itemtaxrate * 0.01)), taxpriceDigit)
                // 未税金额=未税单价*数量
                row.amount = fomatFloat((row.price * row.quantity), amountDigit)
                // 含税金额=含税单价*数量
                row.taxamount = fomatFloat((row.taxprice * row.quantity), taxamountDigit)
                //税额=含税金额-未税金额
                row.taxtotal = fomatFloat((row.taxamount - row.amount), taxamountDigit)
                row.stdprice = fomatFloat((row.price / (row.rebate * row.rebatesec)), priceDigit)
                row.stdamount = fomatFloat(row.stdprice*row.quantity, amountDigit)
                break;
            case "quantity":
                //数量保留位数
                row.quantity = fomatFloat(row.quantity, qtyDigit)
               
                row.stdamount = fomatFloat(row.stdprice*row.quantity, amountDigit)
                //未税金额
                row.amount = fomatFloat((row.price * row.quantity), amountDigit)
                // 含税金额=含税单价*数量
                row.taxamount = fomatFloat((row.taxprice * row.quantity), taxamountDigit)
                //税额=含税金额-未税金额
                row.taxtotal = fomatFloat((row.taxamount - row.amount), taxamountDigit)
                break;
            case "itemtaxrate":
                row.itemtaxrate = fomatFloat(row.itemtaxrate, 0)
                // 含税单价=未税单价*税率
                row.taxprice = fomatFloat((row.price * (1 + row.itemtaxrate * 0.01)), taxpriceDigit)

                // 含税金额=含税单价*数量
                row.taxamount = fomatFloat((row.taxprice * row.quantity), taxamountDigit)
                //税额=含税金额-未税金额
                row.taxtotal = fomatFloat((row.taxamount - row.amount), taxamountDigit)
                break;
            default:
                break;
        }
    } catch (error) {
        row.price = 0;
        row.taxprice = 0;
        row.amount = 0;
        row.taxamount = 0;
        row.taxtotal = 0;
        this.$message.warning("计算异常：" + error);
    }
    return row;
}
// formdata 正向赋值计算
export function getParam(saveparam, paramdata, formdata) {
    for (var i = 0; i < saveparam.params.length; i++) {
        var aItem = saveparam.params[i];
        paramdata[aItem] = formdata[aItem];
    }
    // item 内容添加
    if (!!formdata.item) {
        formdata.item.forEach((n) => {
            var obj = {};
            for (var key in n) {
                var keyIndex = saveparam.paramsItem.findIndex((m) => m == key);
                if (keyIndex != -1) {
                    obj[key] = n[key];
                }
            }
            paramdata.item.push(obj);
        });
    }
    // mat 内容添加
    if (!!formdata.mat) {
        formdata.mat.forEach((n) => {
            var obj = {};
            for (var key in n) {
                var keyIndex = saveparam.paramsMat.findIndex((m) => m == key);
                if (keyIndex != -1) {
                    obj[key] = n[key];
                }
            }
            paramdata.mat.push(obj);
        });
    }
    if (!!formdata.cash) {
        formdata.cash.forEach((n) => {
            var obj = {};
            for (var key in n) {
                var keyIndex = saveparam.paramsCash.findIndex((m) => m == key);
                if (keyIndex != -1) {
                    obj[key] = n[key];
                }
            }
            paramdata.cash.push(obj);
        });
    }
    if (!!formdata.draw) {
        formdata.draw.forEach((n) => {
            var obj = {};
            for (var key in n) {
                var keyIndex = saveparam.paramsDraw.findIndex((m) => m == key);
                if (keyIndex != -1) {
                    obj[key] = n[key];
                }
            }
            paramdata.draw.push(obj);
        });
    }
    return paramdata
}