import store from '@/store';
import request from "@/utils/request";
import { getRefreshToken, getToken, setToken, setrtKey } from "@/utils/auth";
import Cookies from "js-cookie";
let promise = null;
export async function refreshToken() {
    request.defaults.baseURL = Cookies.get("baseApi",{domain:window.location.hostname});
    console.log("Refreshing token...");
    if (promise) {
        return promise;
    }
    promise = new Promise(async (resolve, reject) => {
        try {
            const resp = await request.post('/auth/tokenRtKet', {
                refreshtoken: getRefreshToken(),
                tenantid: JSON.parse(window.localStorage.getItem("getInfo")).tenantid,
                _isRefresh: true,
                fncode: store.state.app.config.deffncode,
            });

            if (resp.data.code === 200) {
                setToken(resp.data.data.access_token);
                setrtKey(resp.data.data.rtkey);
                localStorage.setItem("getInfo", JSON.stringify(resp.data.data.loginuser));
                store.commit('user/SET_USERINFO', resp.data.data.loginuser);
                store.commit('user/SET_TOKEN', getToken());
                resolve(true);
            } else {
                resolve(false);
            }
        } catch (error) {
            resolve(false);
        } finally {
            promise = null;
        }
    });

    return promise;
}

export function isRefreshRequest(config) {
    return config && config._isRefresh === true;
}