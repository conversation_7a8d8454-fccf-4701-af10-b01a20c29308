import request from "@/utils/request";
export function batchDelete(baseurl, datalst, field) {
    let promiseList = [];
    field = field ? field : 'id'
    for (let item of datalst) {
        let promise = new Promise((resolve, reject) => {
            request
                .get(baseurl + `?key=${item[field]}`)
                .then((res) => {
                    if (res.data.code == 200) {
                        if (res.data.data == 0) {
                            reject(res.data.msg || "删除失败");
                        } else {
                            resolve("删除成功");
                        }
                    } else {
                        reject(res.data.msg || "删除失败");
                    }
                })
                .catch((er) => {
                    reject(er || "删除失败");
                });
        });
        promiseList.push(promise);
    }
    return promiseList
}
