import axios from 'axios';
import { MessageBox, Message, Notification } from 'element-ui';
import store from '@/store';
import { getToken, setToken, removeToken } from '@/utils/auth';
import Cookies from 'js-cookie';
import router from '@/router';
import { refreshToken, isRefreshRequest } from '@/utils/refreshToken';

const service = axios.create({
  baseURL: Cookies.get('baseApi',{domain:window.location.hostname}),
  timeout: 10 * 1000, // 10000 request timeout
});

service.interceptors.request.use(
  config => {
    if (getToken() != undefined || store.getters.token) {
      config.headers['Authorization'] = getToken();
    }
    config.headers['Content-Type'] = 'application/json;charset=UTF-8';
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

service.interceptors.response.use(
  async response => {
    console.log('请求拦截success', response)
    const code = response.status;
    if (code < 200 || code > 300) {
      Notification.error({
        title: '错误',
        message: response.data.msg || '请求出错',
      });
      return Promise.reject('error');
    } else {
      if (response.data.code === 403) {
        Notification.error({
          title: '请求已被拒绝',
          message: response.data.msg || '拒绝访问',
          duration: 3500,
        });
        return Promise.reject(response.data.msg || '拒绝访问');
      } else if (response.data.code === 401 && !isRefreshRequest(response.config)) {
        try {
          const isSuccess = await refreshToken();
          if (isSuccess) {
            response.config.headers.Authorization = `${getToken()}`;
            response.config._isRefresh = true; // 添加标志，避免无限递归
            return service.request(response.config);
          } else {
            Notification.error({
              title: '登录已到期，请重新登录',
              message: response.data.msg || '请重新登录',
              duration: 3500,
            });
            removeToken();
            // router.push({ path: '/login' });
            return Promise.reject('登录已到期');
          }
        } catch (error) {
          Notification.error({
            title: '刷新 token 失败',
            message: '请重新登录',
            duration: 3500,
          });
          removeToken();
          // router.push({ path: '/login' });
          return Promise.reject('刷新 token 失败');
        }
      }
      return response;
    }
  },
  error => {
    if (error.message === 'Network Error') {
      Notification.error({
        title: '网络错误',
        message: '未能连接服务器...',
      });
    } else if (error.response) {
      const code = error.response.status;
      if (code === 404) {
        Notification.error({
          title: '请求404',
          message: '请求错误，服务器接口404...',
        });
      } else if (code === 500) {
        Notification.error({
          title: '服务器错误',
          message: '后台服务器错误，请重试...',
        });
      } else if (code === 503) {
        Notification.error({
          title: '服务器维护',
          message: '服务器维护中，请稍后再试...',
        });
      }
    } else if (error.message.includes('timeout')) {
      Notification.error({
        title: '请求错误',
        message: '请求超时...',
      });
    }
    return Promise.reject(error);
  }
);

export default service;
