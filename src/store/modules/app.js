import Cookies from 'js-cookie'

const state = {
  sidebar: {
    opened: Cookies.get('sidebarStatus',{domain:window.location.hostname}) ? !!+Cookies.get('sidebarStatus',{domain:window.location.hostname}) : true,
    withoutAnimation: false
  },
  device: 'desktop',
  platform: '', // 是否为钉钉设备  Eric
  navdata: '', // 用于保存导航 Eric
  roledata: '',
  web_socket_msg: "",
  config: {
    baseURL: Cookies.get('baseApi',{domain:window.location.hostname}) ? Cookies.get('baseApi',{domain:window.location.hostname}) : '', // Eric
    title: '应凯订单管理系统', // 用于动态定义系统名
    guid: '', // 用于动态加载菜单
    model: Cookies.get('sysmodel',{domain:window.location.hostname}) == 'cluster' ? '组织' : '账套'
  },
  navweb: '', // PC导航菜单数据
  // BASE_API: Cookies.get('baseApi') ? Cookies.get('baseApi') : '', //Eric
  // apptitle:'订单管理系统',  // 用于动态定义系统名
  // appguid:'',  // 用于动态加载菜单
}

const mutations = {
  TOGGLE_SIDEBAR: state => {
    state.sidebar.opened = !state.sidebar.opened
    state.sidebar.withoutAnimation = false
    if (state.sidebar.opened) {
      Cookies.set('sidebarStatus', 1,{domain:window.location.hostname})
    } else {
      Cookies.set('sidebarStatus', 0,{domain:window.location.hostname})
    }
  },
  CLOSE_SIDEBAR: (state, withoutAnimation) => {
    Cookies.set('sidebarStatus', 0,{domain:window.location.hostname})
    state.sidebar.opened = false
    state.sidebar.withoutAnimation = withoutAnimation
  },
  TOGGLE_DEVICE: (state, device) => {
    state.device = device
  },
  SET_PLAT_FORM(state, data) {
    state.platform = data // Eric
  },
  SET_NAV_DATA(state, data) {
    state.navdata = data // nav
  },
  SET_ROLE_DATA(state, data) {
    state.roledata = data // nav
  },
  SET_CONFIG(state, data) {
    state.config = data;
    if (data.model == 'cluster') {
      state.config.model = '组织'
    } else {
      state.config.model = '账套'
    }
    Cookies.set('baseApi', data.baseURL,{domain:window.location.hostname});
    Cookies.set('sysmodel', data.model,{domain:window.location.hostname});
  },
  SET_NAV_WEB(state, data) {
    state.navweb = data // web
  },
  // SET_BASE_API(state, data) {
  //   state.BASE_API = data   //Eric
  //   Cookies.set('baseApi', data)
  // },
  // SET_APP_TITLE(state, data) {
  //   state.apptitle = data   //Eric
  // },
  // SET_APP_GUID(state, data) {
  //   state.appguid = data   //Eric
  // }
}

const actions = {
  toggleSideBar({ commit }) {
    commit('TOGGLE_SIDEBAR')
  },
  closeSideBar({ commit }, { withoutAnimation }) {
    commit('CLOSE_SIDEBAR', withoutAnimation)
  },
  toggleDevice({ commit }, device) {
    commit('TOGGLE_DEVICE', device)
  },

  setPlatForm({ commit }, data) {
    commit('SET_PLAT_FORM', data)
  },
  setnavdata({ commit }, data) {
    commit('SET_NAV_DATA', data)
  },
  setconfig({ commit }, data) {
    commit('SET_CONFIG', data)
  },
  setnavweb({ commit }, data) {
    commit('SET_NAV_WEB', data)
  },
  // setBaseApi({ commit }, data) {
  //   commit('SET_BASE_API', data)
  // },
  // setapptitle({ commit }, data) {
  //   commit('SET_APP_TITLE', data)
  // },
  // setappguid({ commit }, data) {
  //   commit('SET_APP_GUID', data)
  // }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
