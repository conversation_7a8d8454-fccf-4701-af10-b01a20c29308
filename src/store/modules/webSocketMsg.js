
const getDefaultState = () => {
  return {
    webSocketMsg:{},
    webSocketState:"连接关闭",
    wsurl:"ws://127.0.0.1:18800"
  }
}
const state = getDefaultState()
const mutations = {
    getWsMsg:(state, webSocketMsg) => {
      state.webSocketMsg=webSocketMsg
  },
  getState:(state, webSocketState)=>{
    state.webSocketState=webSocketState
  },
  getWsurl:(state, newUrl)=>{
    state.wsurl = newUrl;
  }
}

const actions = {

}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

