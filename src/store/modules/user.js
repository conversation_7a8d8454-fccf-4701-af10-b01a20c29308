import { login, logout, getInfo } from '@/api/user'
import { getToken, setToken, removeToken } from '@/utils/auth'
import { resetRouter } from '@/router'

const getDefaultState = () => {
  return {
    token: getToken(),
    name: '',
    avatar: '',
    userinfo: [],
    tenantinfo:{},
    serviceOrder:'',//用于保存服务工单点击刷新触发顶部工具栏刷新的变量  zhang
  }
}

const state = getDefaultState()

const mutations = {
  RESET_STATE: (state) => {
    Object.assign(state, getDefaultState())
  },
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_NAME: (state, name) => {
    state.name = name
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar
  },
  SET_USERINFO: (state, userinfo) => {
    state.userinfo = userinfo
  },
  SET_SERVICEORDER: (state, serviceOrder) => {
    state.serviceOrder = serviceOrder
  },
  SET_TENANTINFO: (state, tenantinfo) => {
    state.tenantinfo = tenantinfo
  },
}

const actions = {
  // user login
  login({ commit }, userInfo) {
    const { UserName, Password } = userInfo
    return new Promise((resolve, reject) => {
      login({ UserName: UserName.trim(), Password: Password }).then(response => {
        const { data } = response
        if (data.code == 200) {
          commit('SET_TOKEN', data.data.access_token)
          setToken(data.data.access_token)
        }
        resolve(data)
      }).catch(error => {
        reject(error)
      })
    })
  },
  // get user info
  getInfo({ commit, state }, userInfo) {
    return new Promise((resolve, reject) => {
      commit('SET_TOKEN', userInfo.token)
      setToken(userInfo.token)
      commit('SET_NAME', userInfo.realname)
      commit('SET_USERINFO', userInfo)
      if (userInfo.avatar) {
        commit('SET_AVATAR', userInfo.avatar)
      } else {
        commit('SET_AVATAR', 'https://oss.aliyuncs.com/aliyun_id_photo_bucket/default_handsome.jpg')
      }
    })
  },
  //get tenant info
  gettenantInfo({ commit, state }, tenantinfo ){
    commit('SET_TENANTINFO', tenantinfo)
  },

  // user logout
  logout({ commit, state }) {
    return new Promise((resolve, reject) => {
      logout().then((response) => {
        removeToken()
        resetRouter()
        window.localStorage.clear()
        commit('RESET_STATE')
        resolve()
      }).catch(error => {
        removeToken()
        window.localStorage.clear()
        reject(error)
      })
    })
  },

  // remove token
  resetToken({ commit }) {
    return new Promise(resolve => {
      removeToken() // must remove  token  first
      commit('RESET_STATE')
      resolve()
    })
  },
  //服务单改变 2021/12/29 zhang
  serviceChange({ commit, state }, service){
    commit('SET_SERVICEORDER', service)
  },
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

