
const getDefaultState = () => {
  return {
    modulecode:"",
    scenename:"",
    jsondata:[],   
  }
}
const state = getDefaultState()
const mutations = {
  setSearchData(state, data){
    state.modulecode=data.modulecode;
    state.jsondata=data.jsondata;
  },
}

const actions = {
  // setsearchdata({ commit }, data) {
  //   commit('SET_SEARCH_DATA', data)
  // }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

