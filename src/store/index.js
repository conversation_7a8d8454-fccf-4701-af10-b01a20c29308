import Vue from 'vue'
import Vuex from 'vuex'
import getters from './getters'
import app from './modules/app'
import settings from './modules/settings'
import user from './modules/user'
import tagsView from './modules/tagsView' // zhang 窗口标签栏用
import permission from './modules/permission' // zhang 窗口标签栏用
import webSocketMsg from './modules/webSocketMsg'
import advancedSearch from './modules/advancedSearch'

Vue.use(Vuex)

const store = new Vuex.Store({
  modules: {
    app,
    settings,
    user,
    tagsView,
    permission,
    webSocketMsg,
    advancedSearch
  },
  getters
})

export default store
