import './public-path'
import Vue from 'vue'
import VueRouter from 'vue-router'
import Cookies from 'js-cookie'
import axios from 'axios'
import store from '@/store'
import App from './App'
import request from '@/utils/request'
import { setRefreshToken, setToken } from './utils/auth'
import routes from '@/router'
import { windowDirective, openMicroWindow } from '@/directives/window'
import i18n from '@/i18n'
import locale from 'element-ui/lib/locale'
import enelelang from 'element-ui/lib/locale/lang/en'
import zhelelang from 'element-ui/lib/locale/lang/zh-CN'

// 注册 v-window 指令
Vue.directive('window', windowDirective)

let instance = null
let sonRouter = null
const wujieEnv = window.__POWERED_BY_WUJIE__
Vue.prototype.$ismicroapp = wujieEnv

Vue.prototype.$microClose = function(timeout = 0, updateData = true) {
  setTimeout(() => {
    if (wujieEnv) {
      const { close } = window.$wujie.props
      // 状态为弹窗的场景下执行关闭弹窗
      if (close) {
        if (updateData) {
          this.$microBindData()
        }
        console.log('v-window 弹窗环境 关闭弹窗')
        close()
      }
    }
  }, timeout)
}
Vue.prototype.$microBindData = function() {
  if (wujieEnv) {
    const { bindData, lstp } = window.$wujie.props
    if (bindData) {
      if (lstp.orgidx) {
        bindData(lstp.orgidx)
      } else {
        bindData()
      }
      console.log('v-window 弹窗环境 更新父列表')
    }
  }
}

const getMicroEntity = async(code, type) => {
  return new Promise((resolve, reject) => {
    request
      .get(`/system/SYSM07B20/getEntityByCode?key=${code}&type=${type}`)
      .then((response) => {
        if (response.data.code === 200) {
          resolve(response.data.data)
        } else {
          reject(response.data.msg)
        }
      })
  })
}
const getMicroEntrySer = (code) => {
  let codeMap = sessionStorage.getItem('inks-microcode')
  if (!codeMap) {
    console.error(`microcode对应数据不存在, 请检查`)
    return ''
  } else {
    codeMap = JSON.parse(codeMap)
  }
  const entry = codeMap.find((item) => item.key === code)
  if (entry) {
    const app = store.state['app']
    const config = app?.config || {}
    let localMark = false
    if (config.localmark && config.localmark === 1) {
      localMark = true
    }
    const splitEntry = entry.value.split(';')
    if (localMark) {
      return splitEntry[0]
    } else {
      return splitEntry[1]
    }
  }
  console.error(`${code}对应地址不存在, 请检查`)
  return ''
}

const openMicroMixin = Vue.mixin({
  methods: {
    $openMicroWindow(params) {
      openMicroWindow(params, this)
    },
    $getMicroEntrySer(code) {
      return getMicroEntrySer(code)
    },
    $getMicroEntity(key, type) {
      return getMicroEntity(key, type)
    }
  }
})

if (wujieEnv) {
  // 重新cookie api
  // 子应用的domain hostname还是主应用的, 通过重写代理的方式写进主应用
  const cookieProxy = () => {
    const getMainCookie = window.$wujie.props.getMainCookie
    Cookies.set = function(name, value, options) {
      getMainCookie().set(name, value, options)
    }
    Cookies.get = function(name, options) {
      return getMainCookie().get(name, options)
    }
    Cookies.remove = function(name, options) {
      getMainCookie().remove(name, options)
    }
  }
  cookieProxy()

  const bus = window.$wujie.bus
  bus.$on('updateLanguage', (language) => {
    i18n.locale = language
    console.log('切换语言', language)
    if (language === 'en') {
      locale.use(enelelang)
    } else {
      locale.use(zhelelang)
    }
  })
}

class InitApp {
  constructor(microBase = '/') {
    this.base = microBase
    this.routes = routes
    this.baseKey = this.base.replaceAll('/', '')
    this.lifestate = 0
    this.errorListener()
    this.createRouter()
    this.init()
  }
  errorListener() {
    const key = this.baseKey
    window.addEventListener('error', function(event) {
      // 检查是否是子应用的资源
      if (event.target && (['LINK', 'SCRIPT', 'IMG'].includes(event.target.tagName))) {
        if (wujieEnv) {
          this.lifestate = 2
          const bus = window.$wujie.bus
          bus.$emit('pong', {
            key: key,
            state: this.lifestate
          })
        }
      }
    }, true)
  }
  applyConfig(config) {
    if (config.env !== 'dev' && process.env.NODE_ENV !== 'development') {
      window.console.log = () => {}
    }
    const baseURL = config.baseURL || window.location.origin + '/'
    Cookies.set('baseApi', baseURL, {
      expires: 5,
      domain: window.location.hostname
    })
    store.dispatch('app/setconfig', config)
    request.defaults.baseURL = baseURL
  }
  createRouter() {
    if (!sonRouter) {
      if (wujieEnv) {
        const microRoutes = []
        const baseKey = this.baseKey
        this.routes.forEach(route => {
          microRoutes.push({
            path: route.path,
            name: route.name,
            key: baseKey,
            microPath: `/${baseKey}${route.path}`
          })
          route.path = `/${baseKey}${route.path}`
        })
        window.$wujie.bus.$emit(`${baseKey}:routes`, {
          form: baseKey,
          routes: microRoutes
        })
      }
      sonRouter = new VueRouter({
        base: '/',
        mode: 'history',
        routes: this.routes
      })
    }
  }
  createApp() {
    instance = new Vue({
      router: sonRouter,
      store,
      i18n,
      mixins: [openMicroMixin],
      render: (h) => h(App)
    }).$mount('#app')
    console.log('当前路由', sonRouter)
  }
  init() {
    if (wujieEnv) {
      const createNewRouter = () => {
        if (!sonRouter) {
          this.createRouter()
        }
      }

      // 代理主应用的路由
      const mainRouterProxy = () => {
        const getMainRouter = window.$wujie.props.getMainRouter
        if (getMainRouter) {
          Vue.prototype.$mainRouter = getMainRouter()
        }
      }

      // 更新用户信息
      const updateUserInfo = () => {
        const getUserInfo = window.$wujie.props.getUserInfo
        if (getUserInfo) {
          const userInfo = getUserInfo()
          store.commit('user/SET_USERINFO', userInfo)
          localStorage.setItem('getInfo', JSON.stringify(userInfo))
          setToken(userInfo.token)
        }
      }

      // 更新token
      const updateRefreshToken = () => {
        const getRefreshToken = window.$wujie.props.getRefreshToken
        if (getRefreshToken) {
          setRefreshToken(getRefreshToken())
        }
      }

      // 更新租户信息
      const updateTenantInfo = () => {
        const getTenantinfo = window.$wujie.props.getTenantinfo
        if (getTenantinfo) {
          store.commit('user/SET_TENANTINFO', getTenantinfo())
        }
      }

      // 初始化microMap对应域名
      const initMicroCode = () => {
        if (!sessionStorage.getItem('inks-microcode')) {
          request.get('/system/SYSM06B1/getMicroAppMapList').then((res) => {
            if (res.data.code === 200) {
              sessionStorage.setItem('inks-microcode', JSON.stringify(res.data.data))
            }
          })
        }
      }

      if (window.$wujie.props.initMicro) {
        createNewRouter()
      }

      window.__WUJIE_MOUNT = async() => {
        if (window.$wujie.props.ping) {
          this.pong()
        }
        if (window.$wujie.props.initMicro) {
          return
        }
        if (window.$wujie.props.elMessage) {
          Vue.prototype.$message = window.$wujie.props.elMessage
        }
        console.log(`init ${window.__WUJIE.id} 子应用`)
        createNewRouter()
        mainRouterProxy()
        updateUserInfo()
        updateRefreshToken()
        updateTenantInfo()
        await this.syncLanguage()
        console.log('window.$wujie.props.getAppConfig()', window.$wujie.props.getAppConfig())
        this.applyConfig(window.$wujie.props.getAppConfig())
        initMicroCode()
        this.createApp()
      }
      window.__WUJIE_UNMOUNT = () => {
        if (instance) {
          instance.$destroy()
          instance.$el.innerHTML = ''
          instance = null
        }
        if (sonRouter) {
          sonRouter = null
        }
      }
    } else {
      axios
        .get('./appconfig.json')
        .then(({ data }) => {
          // 应用配置
          this.applyConfig(data)
          this.createApp()
        })
        .catch((err) => {
          console.error('获取 appconfig.json 失败', err)
          this.createApp()
        })
    }
  }
  pong() {
    const bus = window.$wujie.bus
    if (this.lifestate !== 2) {
      setTimeout(() => {
        this.lifestate = 1
        bus.$emit('pong', {
          key: this.baseKey,
          state: this.lifestate
        })
      }, 2000)
    }
  }
  async syncLanguage() {
    const { getLanguage, vWindow, syncMicroLanguage } = window.$wujie.props
    const language = getLanguage()
    const page = vWindow.page
    const { pageCode } = page
    return new Promise((resolve) => {
      syncMicroLanguage(pageCode).then(message => {
        i18n.setLocaleMessage('zh-CN', message.zh)
        i18n.setLocaleMessage('en', message.en)
        i18n.locale = language || 'zh-CN'
        if (language === 'en') {
          locale.use(enelelang)
        } else {
          locale.use(zhelelang)
        }
        resolve()
      })
    })
  }
}
export {
  getMicroEntrySer,
  getMicroEntity
}
export default InitApp
