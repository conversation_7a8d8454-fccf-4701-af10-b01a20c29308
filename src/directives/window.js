const parseKey = (value, that) => {
  let splitValue = value.slice(1, -1)
  splitValue = splitValue.split('.')
  let temp = that
  splitValue.forEach(v => {
    temp = temp[v]
  })
  return temp
}

const openMicroWindow = (value, that) => {
  value = JSON.parse(JSON.stringify(value))
  const { page } = value
  if (page.microMode) {
    const { params } = value
    // 绑定默认的bindData
    if (that.bindData) {
      params.bindData = that.bindData
    }
    // 绑定默认的callback方法
    if (that.microcallback) {
      params.microcallback = that.microcallback
    }
    // 属性驱动设计
    Object.keys(page).forEach(item => {
      const value = page[item]
      if (typeof value === 'string') {
        if (value.startsWith('{') && value.endsWith('}')) {
          page[item] = parseKey(value, that)
        }
      }
    })
    Object.keys(params).forEach(item => {
      const value = params[item]
      if (typeof value === 'string') {
        if (value.startsWith('{') && value.endsWith('}')) {
          params[item] = parseKey(value, that)
        }
      }
    })
    const lstp = params.lstp
    Object.keys(lstp).forEach(item => {
      const value = lstp[item]
      if (typeof value === 'string') {
        if (value.startsWith('{') && value.endsWith('}')) {
          lstp[item] = parseKey(value, that)
        }
      }
    })
    // 派发事件
    console.log('v-window指令参数', value)
    window.$wujie.props.opensub(value)
  } else {
    // 执行传统的click事件
    const clickHandle = page.clickHandle
    if (typeof clickHandle === 'string') {
      if (that[clickHandle]) {
        that[clickHandle]()
      }
    } else if (typeof clickHandle === 'function') {
      clickHandle()
    }
  }

  // 关闭Dropdown
  if (that.$refs && that.$refs['EditGroupBtns']) {
    const { $children } = that.$refs['EditGroupBtns']
    $children.forEach(child => {
      if (child.popperElm) {
        child.hide()
      }
    })
  }
}

const windowDirective = {
  // 当指令第一次绑定到元素时调用
  bind(el, binding, vnode) {
    const that = vnode.context
    // 存储点击事件处理函数，以便后续解绑
    el._windowClickHandler = function() {
      openMicroWindow(binding.value, that)
    }
    // 添加点击事件监听器
    el.addEventListener('click', el._windowClickHandler)
  },

  // 当绑定值更新时调用
  update(el, binding, vnode) {
    // 同样可以获取组件实例
    const that = vnode.context

    // 如果值发生变化，更新处理函数
    if (binding.value !== binding.oldValue) {
      el._windowClickHandler = function() {
        openMicroWindow(binding.value, that)
      }
    }
  },

  // 当指令与元素解绑时调用
  unbind(el) {
    // 移除事件监听器
    if (el._windowClickHandler) {
      el.removeEventListener('click', el._windowClickHandler)
      delete el._windowClickHandler
    }
  }
}

export {
  openMicroWindow,
  windowDirective
}
