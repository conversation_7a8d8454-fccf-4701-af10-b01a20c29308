const omsRoutes = [
  {
    path: '/M01B1',
    name: 'E08M01B1',
    component: () => import('@/views/modules/E08/E08M01B1/index.vue'),
    meta: { title: '主页', icon: 'table' }
  },
  {
    path: '/M01B2',
    name: 'E08M01B2',
    component: () => import('@/views/modules/E08/E08M01B2/index.vue'),
    meta: { title: '清单', icon: 'table' }
  },
  {
    path: '/M01B3',
    name: 'E08M01B3',
    component: () => import('@/views/modules/E08/E08M01B3/index.vue'),
    meta: { title: '报价详情', icon: 'table' }
  },
  {
    path: '/M01B4',
    name: 'E08M01B4',
    component: () => import('@/views/modules/E08/E08M01B4/index.vue'),
    meta: { title: '报价金额', icon: 'table' }
  },
  {
    path: '/M01B5',
    name: 'E08M01B5',
    component: () => import('@/views/modules/E08/E08M01B5/index.vue'),
    meta: { title: '报价进度', icon: 'table' }
  },
  {
    path: '/M01B6',
    name: 'E08M01B6',
    component: () => import('@/views/modules/E08/E08M01B6/index.vue'),
    meta: { title: '金额详细', icon: 'table' }
  },
  {
    path: '/M14B1',
    name: 'E08M14B1',
    component: () => import('@/views/modules/E08/E08M01B7/index.vue'),
    meta: { title: '标准BOM', icon: 'table' }
  },
  {
    path: '/M13S1',
    name: 'E08M13S1',
    component: () => import('@/views/modules/E08/E08M01B8/index.vue'),
    meta: { title: '物料信息', icon: 'table' }
  }
]
export default omsRoutes
