<template>
  <div class="cost-analysis">
    <!-- 产品标题 -->
    <div class="product-title">
      <h1>按摩仪(FL10121)V2</h1>
    </div>

    <!-- 成本卡片区域 -->
    <div class="cost-cards">
      <!-- 总成本卡片 -->
      <div class="cost-card total-cost" @click="() => amountVisible = true">
        <div class="cost-circle">
          <div class="cost-amount">231¥</div>
          <div class="cost-label">报价金额</div>
        </div>
      </div>

      <!-- 各项成本卡片 -->
      <div class="cost-card">
        <div class="cost-header">
          <span class="cost-type">塑料成本</span>
        </div>
        <div class="cost-value">17¥</div>
      </div>

      <div class="cost-card">
        <div class="cost-header">
          <span class="cost-type">金属成本</span>
        </div>
        <div class="cost-value">32¥</div>
      </div>

      <div class="cost-card">
        <div class="cost-header">
          <span class="cost-type">电子成本</span>
        </div>
        <div class="cost-value">32¥</div>
      </div>

      <div class="cost-card">
        <div class="cost-header">
          <span class="cost-type">配件成本</span>
        </div>
        <div class="cost-value">32¥</div>
      </div>
    </div>

    <!-- 标签页 -->
    <div class="tabs-section">
      <el-tabs v-model="activeTab" class="cost-tabs">
        <el-tab-pane label="塑料" name="plastic" />
        <el-tab-pane label="金属" name="metal" />
        <el-tab-pane label="电子" name="electronic" />
        <el-tab-pane label="配件" name="accessories" />
        <el-tab-pane label="包装" name="packaging" />
        <el-tab-pane label="人工" name="labor" />
      </el-tabs>

      <!-- 编辑按钮 -->
      <el-button v-if="false" type="text" class="edit-btn">编辑</el-button>
    </div>

    <!-- 详情表格 -->
    <div class="details-table">
      <ve-table
        :key="keynum"
        :columns="tableColumns"
        :table-data="tableData"
        :border-around="true"
        :border-x="true"
        :border-y="true"
        :row-style-option="rowStyleOption"
        :cell-style-option="cellStyleOption"
        :max-height="400"
      />
    </div>
    <el-dialog
      v-if="amountVisible"
      title="金额明细"
      :append-to-body="true"
      top="5vh"
      :visible.sync="amountVisible"
      width="1500px"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <AmountDetail />
    </el-dialog>
  </div>
</template>

<script>
import AmountDetail from '@/views/modules/E08/E08M01B6/index.vue'

export default {
  name: 'CostAnalysis',
  components: { AmountDetail },
  data() {
    return {
      keynum: 0,
      activeTab: 'plastic',
      amountVisible: false,
      tableColumns: [
        {
          field: 'image',
          key: 'image',
          title: '图片',
          width: 80,
          align: 'center',
          renderBodyCell: ({ row, column, rowIndex }, h) => {
            if (column.field === 'image') {
              const src = require('@/' + row[column.field])
              const html = (
                <div>
                  <el-image
                    style='width:30px;text-align:center'
                    src={src}
                    preview-src-list={[src]}
                  >
                  </el-image>

                </div>
              )
              return html
            }
          }
        },
        {
          field: 'name',
          key: 'name',
          title: '货品名称',
          width: 200,
          align: 'center'
        },
        {
          field: 'quantity',
          key: 'quantity',
          title: '数量',
          width: 100,
          align: 'center'
        },
        {
          field: 'amount',
          key: 'amount',
          title: '金额',
          width: 120,
          align: 'center'
        },
        {
          field: 'status',
          key: 'status',
          title: '状态',
          width: 100,
          align: 'center'
        },
        {
          field: 'action',
          key: 'action',
          title: '操作',
          width: 100,
          align: 'center',
          renderBodyCell: ({ row, column, rowIndex }, h) => {
            if (column.field === 'action') {
              const html = (
                <div>
                  <el-button type='text' on-click={() => {}}>
                    详情
                  </el-button>
                </div>
              )
              return html
            }
          }
        }
      ],
      tableData: [
        {
          id: 1,
          checked: false,
          image: 'assets/product/product-placeholder.png',
          name: '上盖面板',
          quantity: 1,
          amount: '0.733',
          status: '完成',
          statusType: 'success'
        }
      ],
      rowStyleOption: {
        stripe: true,
        hoverHighlight: true
      },
      cellStyleOption: {
        bodyCellClass: ({ row, column, rowIndex }) => {
          return 'custom-cell'
        }
      }
    }
  },
  created() {
    this.keynum++
  },
  watch: {
    activeTab: {
      handler() {
        this.switchTable();
      }
    }
  },
  methods: {
    handleEdit(row) {
      console.log('编辑:', row)
      this.$message.success('编辑功能')
    },
    switchTable() {
      if (this.activeTab === 'plastic') {
        this.tableData = [
          {
            id: 1,
            checked: false,
            image: 'assets/product/product-placeholder.png',
            name: '上盖面板',
            quantity: 1,
            amount: '0.733',
            status: '完成',
            statusType: 'success'
          }
        ]
      } else if (this.activeTab === 'metal') {
        this.tableData = [
          {
            id: 1,
            checked: false,
            image: 'assets/product/product-metal.png',
            name: '支架螺丝',
            quantity: 1,
            amount: '0.733',
            status: '完成',
            statusType: 'success'
          }
        ]
      } else if (this.activeTab === 'electronic') {
        this.tableData = [
          {
            id: 1,
            checked: false,
            image: 'assets/product/product-electronic.png',
            name: '电池',
            quantity: 1,
            amount: '60',
            status: '完成',
            statusType: 'success'
          }
        ]
      } else if (this.activeTab === 'accessories') {
        this.tableData = [
          {
            id: 1,
            checked: false,
            image: 'assets/product/product-accessories.png',
            name: '防震垫',
            quantity: 1,
            amount: '0.6',
            status: '完成',
            statusType: 'success'
          }
        ]
      } else if (this.activeTab === 'packaging') {
        this.tableData = [
          {
            id: 1,
            checked: false,
            image: 'assets/product/product-packaging.png',
            name: '纸盒',
            quantity: 1,
            amount: '3.5',
            status: '完成',
            statusType: 'success'
          }
        ]
      } else if (this.activeTab === 'labor') {
        this.tableData = []
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.cost-analysis {
  padding: 20px;
  background: #f5f5f5;
  height: 100%;
}

.product-title {
  margin-bottom: 30px;

  h1 {
    font-size: 24px;
    font-weight: bold;
    color: #333;
    margin: 0;
  }
}

.cost-cards {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

.cost-card {
  background: white;
  border-radius: 8px;
  padding: 5px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-width: 180px;

  &.total-cost {
    .cost-circle {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100px;
      height: 100px;
      border-radius: 50%;
      background: linear-gradient(135deg, #1890ff, #40a9ff);
      color: white;
      margin: 0 auto;

      .cost-amount {
        font-size: 18px;
        font-weight: bold;
      }

      .cost-label {
        font-size: 12px;
        margin-top: 4px;
      }
    }
  }

  .cost-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;

    .cost-type {
      font-size: 14px;
      color: #666;
    }

    .download-btn {
      color: #ff4d4f;
      padding: 0;

      i {
        font-size: 16px;
      }
    }
  }

  .cost-value {
    font-size: 24px;
    font-weight: bold;
    color: #ff4d4f;
  }
}

.tabs-section {
  background: white;
  border-radius: 8px;
  padding: 0 20px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .cost-tabs {
    flex: 1;

    :deep(.el-tabs__header) {
      margin: 0;

      .el-tabs__nav-wrap {
        &::after {
          display: none;
        }
      }

      .el-tabs__item {
        padding: 0 20px;
        height: 50px;
        line-height: 50px;
        color: #666;

        &.is-active {
          color: #1890ff;
        }
      }

      .el-tabs__active-bar {
        background-color: #1890ff;
      }
    }
  }

  .edit-btn {
    color: #666;
    padding: 0;
    margin-left: 20px;
  }
}

.details-table {
  background: white;
  border-radius: 8px;
  padding: 20px;
  height: 100%;

  :deep(.ve-table) {
    .ve-table-header-tr {
      background: #fafafa;

      th {
        font-weight: 600;
        color: #333;
        border-right: 1px solid #e8e8e8;
        padding: 12px 8px;
      }
    }

    .ve-table-body-tr {
      &:hover {
        background: #f9f9f9;
      }

      td {
        border-right: 1px solid #e8e8e8;
        padding: 12px 8px;

        &.custom-cell {
          vertical-align: middle;
        }
      }
    }
  }
  ::v-deep .ve-table-container {
    height: 400px !important;
  }
}

.product-image-cell {
  img {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 4px;
    background: #f0f0f0;
  }
}
</style>
