<template>
  <div>
    <!-- formedit部分 货品信息 -->
    <div v-if="formvisible" ref="formedit" class="formedit">
      <FormEdit
        ref="formedit"
        :idx="idx"
        :goods-cust-list="goodsCustList"
        v-on="{
          compForm: compForm,
          closeForm: closeForm,
          changeIdx: changeIdx,
          bindData: bindData,
        }"
      />
    </div>
    <div v-show="!formvisible" ref="index" class="index">
      <ListHeader
        :show-tree="treevisble"
        :table-form="tableForm"
        @btnAdd="showForm(0)"
        @btnshowGroup="treevisble = !treevisble"
        @bindData="bindData"
        @btnImport="btnImport"
        @Export="Export"
        @btnHelp="btnHelp"
        @pagePrint="$refs.tableList.pagePrint()"
        @btnPrint="$refs.tableList.btnPrint()"
        @btnSearch="$refs.tableList.search($event)"
        @advancedSearch="$refs.tableList.advancedSearch($event)"
        @allDelete="$refs.tableList.allDelete()"
        @btnExport="$refs.tableList.btnExport()"
        @bindColumn="$refs.tableList.getColumn()"
      />
      <div class="page-container">
        <el-row>
          <!-- ==============左侧物料分组========================= -->
          <el-col v-show="treevisble" :span="4">
            <div style="font-size: 14px; color: #606266">
              <div class="groupTitle">
                <span>分组</span>
                <i
                  class="el-icon-s-tools"
                  :style="{ color: treeeditable ? '#1e80ff' : '' }"
                  @click="treeEdit"
                />
              </div>
              <el-tree
                :data="groupData"
                node-key="id"
                default-expand-all
                :expand-on-click-node="false"
                style="
                  font-size: 14px;
                  height: 80vh;
                  overflow: auto;
                  width: 100%;
                "
              >
                <span slot-scope="{ node, data }" class="custom-tree-node">
                  <span
                    @click="() => handleNodeClick(data)"
                  >{{ node.label }}
                    <a
                      v-if="data.prefix"
                      href="javascript:;"
                    >[{{ data.prefix }}]</a>
                  </span>
                  <!-- 操作按键 -->
                  <span v-show="treeeditable">
                    <el-button
                      v-show="data.id !== '0'"
                      type="text"
                      size="mini"
                      style="padding-left: 5px"
                      icon="el-icon-edit"
                      @click="() => editTreeNode(data)"
                    />
                  </span>
                  <span v-show="treeeditable">
                    <el-button
                      type="text"
                      size="mini"
                      style="padding-left: 5px"
                      icon="el-icon-circle-plus-outline"
                      @click="() => addTreeChild(data)"
                    />
                  </span>
                  <span v-show="treeeditable">
                    <el-button
                      v-show="data.id !== '0'"
                      type="text"
                      size="mini"
                      style="padding-left: 5px"
                      icon="el-icon-delete-solid"
                      @click="() => delTreeNode(data)"
                    />
                  </span>
                </span>
              </el-tree>
            </div>
          </el-col>
          <!-- =========================右侧货品信息======================= -->
          <el-col
            :span="treevisble ? (showhelp ? 16 : 20) : showhelp ? 20 : 24"
          >
            <TableList
              ref="tableList"
              :formtemplate="formtemplate"
              @changeIdx="changeIdx"
              @showForm="showForm"
              @sendTableForm="sendTableForm"
            />
          </el-col>
          <el-col :span="showhelp ? 4 : 0">
            <HelpModel ref="helpmodel" :code="'D91M01B1'" />
          </el-col>
        </el-row>
      </div>
    </div>
    <!-- ====================组件========================= -->
    <!-- 新建调整货品分组对话框 -->
    <el-dialog
      v-if="gropuformvisible"
      title="货品分类"
      :append-to-body="true"
      :visible.sync="gropuformvisible"
      width="800px"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <FormAdd
        ref="formadd"
        :group-data="groupData"
        :idx="idx"
        :pid="pid"
        v-on="{
          compForm: compForm,
          closeForm: closeForm,
          bindData: bindTreeData,
        }"
      />
    </el-dialog>
    <!-- 货品导入对话框 -->
    <el-dialog
      v-if="exportvisible"
      title="货品导入"
      :visible.sync="exportvisible"
      width="60%"
      top="4vh"
    >
      <Export
        ref="exportFile"
        @closeDialog="exportvisible = false"
        @bindData="bindData"
      />
    </el-dialog>
    <!-- 批量导出 -->
    <el-dialog
      v-if="exportInfoVisble"
      title="批量导出"
      :visible.sync="exportInfoVisble"
      width="400px"
    >
      <div class="export-content">
        <div
          style="
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: space-around;
          "
        >
          <span>页码</span>
          <el-input-number
            v-model="exportInfo.PageNum"
            style="width: 60%"
            size="small"
            :min="1"
          />
        </div>
        <div
          style="
            display: flex;
            align-items: center;
            justify-content: space-around;
          "
        >
          <span>数量</span>
          <el-input-number
            v-model="exportInfo.PageSize"
            style="width: 60%"
            size="small"
            :min="1"
            :step="100"
          />
        </div>
      </div>
      <div slot="footer">
        <el-button type="primary" @click="submitExport">确 定</el-button>
        <el-button @click="exportInfoVisble = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import request from '@/utils/request'
import FormEdit from './components/formEdit.vue'
import FormAdd from './M013S1/formedit.vue' // 货品分类
import GROUP_CRUD from './M013S1/CRUD.JS' // 货品分类CRUD
import ListHeader from './components/listHeader.vue'
import Export from './components/export.vue' // 导入货品组件
import TableList from './components/tableList.vue'
import { formTemp } from './components/formTemp/index.js'
export default {
  name: 'D91M01B1',
  components: {
    ListHeader,
    FormEdit,
    FormAdd,
    Export,
    TableList
  },
  data() {
    return {
      // ===================通用data================
      microUrl: '',
      editEntity: null,
      windowParams: {
        key: 'D91M01B1-D91M01B1ED',
        page: {
          microMode: this.$ismicroapp,
          microEntry: '{microUrl}'
        },
        params: {
          pageType: 0, //  list
          openType: 0, //  modal
          lstp: {
            idx: '{idx}',
            parent: 'D91M01B1List',
            lifecode: 0
          }
        }
      },
      lst: [],
      formvisible: false,
      idx: 0,
      total: 0,
      queryParams: {
        PageNum: 1,
        PageSize: 20,
        OrderType: 1,
        SearchType: 1
      },
      // ==================自定义data==================
      // 树形图用
      treetitle: '货品分组', // 树形图大分组的名字
      gropuformvisible: false, // 显示货品分类填写表单
      treevisble: true, // 树行图显示 默认显示
      groupData: [], // 树形图数据
      treeeditable: false,
      pid: 0, // 传递给子组件父级id
      // ===========导入货品用变量==============
      // 导入窗口显示
      exportvisible: false,
      selectList: [], // 勾选选择内容
      // 批量导出
      exportInfoVisble: false,
      exportInfo: {
        PageNum: 1,
        PageSize: 100
      },
      showhelp: false,
      goodsInfo: {}, // 货品库存信息
      tableForm: {}, // 表格列数据
      formtemplate: formTemp,
      goodsCustList: []
    }
  },
  computed: {
    tableMaxHeight() {
      return window.innerHeight - 160 + 'px'
    }
  },
  created() {
    this.bindTreeData()
    this.getGoodsCust()
  },
  mounted() {
    this.bindTemp()
    this.bindData()
  },
  methods: {
    bindTemp() {
      this.$request
        .get('/system/SYSM07B13/getEntityByCode?key=D91M01B1')
        .then((res) => {
          if (res.data.code == 200) {
            if (res.data.data != null) {
              this.formtemplate = res.data.data.frmcontent
                ? JSON.parse(res.data.data.frmcontent)
                : formTemp
            }
            this.$nextTick(() => {
              this.$refs.tableList.getColumn()
            })
          } else {
            this.$alert(res.data.msg || '获取页面信息失败')
          }
        })
        .catch((er) => {
          this.$message.error('请求错误')
        })
    },
    // ==========================通用项======================================
    bindData() {
      this.$refs.tableList.bindData()
    },

    btnHelp() {
      this.showhelp = !this.showhelp
      if (this.showhelp) {
        this.$refs.helpmodel.bindData()
      }
    },
    // 获取货品分组
    bindTreeData() {
      const data = {
        PageNum: 1,
        PageSize: 10000,
        OrderType: 0,
        SearchType: 1,
        OrderBy: 'rownum'
      }
      request
        .post('/e08/E08M13S1/getPageList', JSON.stringify(data))
        .then((res) => {
          if (res.data.code == 200) {
            const resdata = res.data.data.list.map((v) => {
              return {
                id: v.id,
                pid: v.parentid,
                label: v.groupname,
                prefix: v.prefix
              }
            })
            const firstItem = [
              {
                id: '0',
                pid: -1,
                label: this.treetitle,
                prefix: false
              }
            ]
            var arr = [...resdata, ...firstItem]
            this.groupData = this.transData(arr, 'id', 'pid', 'children')
          } else {
          }
        })
        .catch((error) => {
          console.log(error)
        })
    },
    sendTableForm(data) {
      this.tableForm = data
    },
    async getGoodsCust() {
      this.goodsCustList = []
      try {
        const res = await this.$request.get('/e08/E08M13S4/getList')
        if (res.data.code === 200) {
          this.goodsCustList = res.data.data || []
        } else {
          this.$message({
            message: res.data.msg,
            type: 'error'
          })
        }
      } catch (error) {
        console.log(error)
      }
    },
    // ===================================table部分方法=================================
    Export() {
      this.exportInfoVisble = true
    },
    submitExport() {
      var queryParams = {
        PageNum: this.exportInfo.PageNum,
        PageSize: this.exportInfo.PageSize,
        OrderType: 1,
        SearchType: 1
      }
      request
        .post('/e08/E08M13B1/exportList', JSON.stringify(queryParams), {
          responseType: 'blob'
        })
        .then((res) => {
          this.$message.success('导出成功')
          const link = document.createElement('a')
          const blob = new Blob([res.data], { type: 'application/vnd.ms-excel' })
          link.style.display = 'none'
          link.href = URL.createObjectURL(blob)
          link.download = '货品信息'
          document.body.appendChild(link)
          // 模拟点击事件
          link.click()
          this.exportInfoVisble = false
        })
    },
    // ===================================通用项目======================================
    // 打开编码窗口
    showForm(res) {
      this.idx = res
      if (this.$ismicroapp) {
        if (this.editEntity && this.editEntity.formentry) {
          this.microUrl = this.editEntity.formentry
          console.log('修改microEntity为', this.editEntity.formentry)
        } else {
          const origin = this.$getMicroEntrySer('E08')
          this.microUrl = `${origin}/E08/M01B8ED`
        }
        this.$openMicroWindow(this.windowParams)
        return
      }
      this.formvisible = true
    },
    // 打开编码窗口 货品分组
    showGroupform(res) {
      this.idx = res
      this.gropuformvisible = true
    },
    // 关闭对话框功能
    closeForm() {
      this.formvisible = false
      this.gropuformvisible = false // 额外加入的货品分类
    },
    // 完成对话框功能
    compForm() {
      this.bindTreeData() // 额外加入的货品分类
      this.bindData()
      this.formvisible = false
      this.gropuformvisible = false // 额外加入的货品分类
    },
    // ===================================自定义项======================================
    transData(a, idStr, pidStr, chindrenStr) {
      var r = []
      var hash = {}
      var id = idStr
      var pid = pidStr
      var children = chindrenStr
      var i = 0
      var j = 0
      var len = a.length
      for (; i < len; i++) {
        hash[a[i][id]] = a[i]
      }
      for (; j < len; j++) {
        var aVal = a[j]
        var hashVP = hash[aVal[pid]]
        if (hashVP) {
          !hashVP[children] && (hashVP[children] = [])
          hashVP[children].push(aVal)
        } else {
          r.push(aVal)
        }
      }
      return r
    },
    // ===================================工具项======================================
    // 树形分组
    handleNodeClick(data) {
      if (data.id == 0) {
        this.$refs.tableList.search('')
      } else {
        const res = data.id
        this.$refs.tableList.searchGroupuid(res)
      }
    },
    // 树形图点击编辑按钮
    treeEdit() {
      console.log(10)
      this.treeeditable = !this.treeeditable
    },
    // 点击编辑按钮
    editTreeNode(data) {
      this.pid = data.pid
      this.showGroupform(data.id)
    },
    // 点击添加节点
    addTreeChild(data) {
      this.pid = data.id
      this.showGroupform(0)
    },
    // 树形图删除节点
    delTreeNode(data) {
      if (data.children) {
        this.$message({
          message: '警告,该节点下存在子节点不能删除',
          type: 'warning'
        })
      } else {
        this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            console.log('执行删除')
            GROUP_CRUD.delete(data.id)
              .then(() => {
                console.log('执行关闭保存')
                this.$message.success('删除成功！')
                this.bindTreeData()
              })
              .catch(() => {
                this.$message.warning('删除失败')
              })
          })
          .catch(() => {})
      }
    },
    changeIdx(val) {
      this.idx = val
    },
    // 导入窗口打开
    btnImport() {
      this.exportvisible = true
    },
    // 提交导入
    submitExPort() {
      console.log(this.$refs.exportFile)
      this.$refs.exportFile.importf()
    },

    // 货品模板导出
    modelExport() {
      request
        .get('/e08/E08M13B1/exportModel', { responseType: 'blob' })
        .then((res) => {
          console.log(res)
          const link = document.createElement('a')
          const blob = new Blob([res.data], { type: 'application/vnd.ms-excel' })
          link.style.display = 'none'
          link.href = URL.createObjectURL(blob)
          link.download = '货品信息模板.xls'
          document.body.appendChild(link)
          // 模拟点击事件
          link.click()
        })
        .catch((err) => {
          console.log(err)
        })
    }
  }
}
</script>
<style lang="scss" scoped>
@import "@/styles/mycustom.scss";
.custom-tree-node {
  span:first-child {
    max-width: 170px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: inline-block;
  }
}
//========================index table css=============================
.page-container {
  padding: 0;
  height: calc(100vh - 105px - 37px);
}
.table-container {
  height: calc(100vh - 140px);
}

.groupTitle {
  font-weight: bold;
  background: rgb(243, 244, 247) none repeat scroll 0% 0%;
  color: rgb(85, 85, 85);
  padding: 3px 15px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  box-sizing: border-box;
}
.groupTitle > i {
  float: right;
  line-height: 30px;
  cursor: pointer;
}
.wordStyle {
  min-height: 100px;
}
</style>

