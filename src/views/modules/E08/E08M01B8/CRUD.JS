import request from "@/utils/request";
const CRUD = {
    // get user info 保存
    add(formdata) {
        console.log(formdata);
        return new Promise((resolve, reject) => {

            var params = JSON.stringify(formdata)
            request
                .post("/E08M13B1/create", params)
                .then((response) => {
                    if (response.data.code == 200) {
                        resolve(response.data);
                    } else {
                        reject(response.data.msg)
                    }
                })
                .catch((error) => {
                    reject(error)
                });
        })
    },
    // 更新添加
    update(formdata) {
        return new Promise((resolve, reject) => {

            var params = JSON.stringify(formdata);

            request
                .post("/E08M13B1/update", params)
                .then((response) => {

                    if (response.data.code == 200) {
                        resolve(response.data);
                    } else {
                        reject(response.data.msg)
                    }
                })
                .catch((error) => {
                    reject(error)
                });
        })
    },
    /**
* 2023-05-09 hu
* @param that that
*  删除
*/
    delete(that) {
        request
            .get(`/E08M13B1/delete?key=${that.formdata.id}`)
            .then((res) => {
                if (res.data.code == 200) {
                    that.$message.success(res.data.msg || "删除成功");
                    that.$emit("compForm");
                } else {
                    that.$message.warning(res.data.msg || "删除失败");
                }
            })
            .catch((error) => {
                that.$message.error(error || "服务请求错误");
            });
    },
    // 识别编码是否存在
    checkGoodsUid(rowdata) {
        return new Promise((resolve, reject) => {

            var params = JSON.stringify(rowdata);
            request
                .post("/E08M13B1/getEntityBynsp", params)
                .then((response) => {

                    if (response.data.code == 200) {
                        resolve(response.data);
                    } else {
                        reject(response.data.msg)
                    }
                })
                .catch((error) => {
                    reject(error)
                });
        })
    },
    // 导入excel
    importEntity(rowdata) {
        return new Promise((resolve, reject) => {

            var params = JSON.stringify(rowdata);
            request
                .post("/E08M13B1/importEntity", params)
                .then((response) => {

                    if (response.data.code == 200) {
                        resolve(response.data);
                    } else {
                        reject(response.data.msg)
                    }
                })
                .catch((error) => {
                    reject(error)
                });
        })
    },
}

export default CRUD
