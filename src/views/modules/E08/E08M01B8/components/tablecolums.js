
export const tableList = {
  formcode: "D91M01B1List",
  item: [
    {
      itemcode: "goodsuid",
      itemname: "货品编码",
      minwidth: "100",
      defwidth: "",
      displaymark: 1,
      overflow: 1,
      fixed: 0,
      sortable: 1,
      aligntype: "center",
      datasheet: "Mat_Goods.goodsuid",
    },
    {
      itemcode: "goodsname",
      itemname: "货品名称",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
      datasheet: "Mat_Goods.goodsname",
    },
    {
      itemcode: "goodsspec",
      itemname: "货品规格",
      minwidth: "100",
      displaymark: 1,
      overflow: 1,
      sortable: 1,
      datasheet: "Mat_Goods.goodsspec",
    },
    {
      itemcode: "goodsstate",
      itemname: "货品状态",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
      datasheet: "Mat_Goods.goodsstate",
    },
    {
      itemcode: "goodsunit",
      itemname: "单位",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
      datasheet: "Mat_Goods.goodsunit",
    },
    {
      itemcode: "partid",
      itemname: "外部编码",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
      datasheet: "Mat_Goods.partid",
    },
    {
      itemcode: "safestock",
      itemname: "安全库存",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
      sortable: 1,
      datasheet: "Mat_Goods.safestock",
    },
    {
      itemcode: "ivquantity",
      itemname: "当前库存",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
      sortable: 1,
      datasheet: "Mat_Goods.ivquantity",
    },
    {
      itemcode: "barcode",
      itemname: "条形码",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
      datasheet: "Mat_Goods.barcode",
    },
    {
      itemcode: "brandname",
      itemname: "品牌",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
      datasheet: "Mat_Goods.brandname",
    },
    {
      itemcode: "lister",
      itemname: "制表",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
    },
    {
      itemcode: "packsn",
      itemname: "货品SN",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
      datasheet: "Mat_Goods.packsn",
    },
    {
      itemcode: "modifydate",
      itemname: "修改日期",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
      sortable: 1,
      datasheet: "Mat_Goods.modifydate",
    },
    {
      itemcode: "operate",
      itemname: "操作",
      minwidth: "150",
      defwidth: "150px",
      displaymark: 1,
      overflow: 1,
    },
  ],
}
