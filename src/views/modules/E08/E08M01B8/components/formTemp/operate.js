/**
 * 2023-05-19 hu
 * 
 * operateBar 操作栏下拉内容
 * processBar 过程查询 下拉内容
 * 
 * show 是否显示
 * divided 是否添加切割线
 * label 名称
 * icon 图标
 * disabled 禁用条件
 * methods 函数方法
 * param 参数
 * children 二级菜单
 */

export const operateBar = [
    {
        show: 1,
        divided: false,
        label: "删 除",
        icon: "el-icon-delete",
        disabled: "this.formstate!=1",
        methods: 'deleteForm',
        param: "",
        children: []
    },
    {
        show: 1,
        divided: true,
        label: "引入货品",
        icon: "el-icon-plus",
        disabled: "this.formstate!==0",
        methods: 'openHistoryGoods',
        param: "",
        children: []
    },
    {
        show: 1,
        divided: true,
        label: "添加附图",
        icon: "el-icon-plus",
        disabled: "this.formstate==0",
        methods: 'openImgUpload',
        param: "",
        children: []
    },
    {
        show: 1,
        divided: false,
        label: "添加附件",
        icon: "el-icon-plus",
        disabled: "this.formstate==0",
        methods: 'openFileUpload',
        param: "",
        children: []
    },
    {
        show: 1,
        divided: false,
        label: "添加附言",
        icon: "el-icon-plus",
        disabled: "this.formstate==0",
        methods: 'openPostscript',
        param: "",
        children: []
    },
]
export const processBar = [
    {
        show: 1,
        divided: false,
        label: '供应商报价',
        icon: "",
        disabled: "this.formstate==0",
        methods: 'selectSupplier',
        param: "",
        children: []
    },
]
