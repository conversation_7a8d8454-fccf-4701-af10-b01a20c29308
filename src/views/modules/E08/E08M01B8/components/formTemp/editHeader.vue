<template>
  <el-form
    ref="formdata"
    :model="formdata"
    :label-width="'100px'"
    class="custInfo"
    :rules="formRules"
  >
    <p class="formTitle">{{ title }}</p>
    <el-divider content-position="left">编码规则</el-divider>
    <el-row>
      <el-col :span="4">
        <div @click="cleValidate('uidgroupguid')">
          <el-form-item label="分组名称" prop="uidgroupguid">
            <el-cascader
              ref="cascader"
              v-model="formdata.uidgroupguid"
              :options="groupnameOptions"
              :props="defaultProps"
              :show-all-levels="false"
              placeholder="请选择分组名称"
              style="width: 100%"
              size="small"
              @change="$emit('handleChangeGroupid', $event)"
            />
          </el-form-item>
        </div>
      </el-col>
      <el-col :span="4">
        <div @click="cleValidate('goodsuid')">
          <el-form-item label="货品编码" prop="goodsuid">
            <el-input
              v-model="formdata.goodsuid"
              placeholder="请输入货品编码"
              clearable
              size="small"
            />
          </el-form-item>
        </div>
      </el-col>
      <el-col :span="4">
        <div @click="cleValidate('taxrate')">
          <el-form-item label="默认税率" prop="taxrate">
            <el-input
              v-model="formdata.taxrate"
              placeholder="请输入默认税率"
              size="small"
            />
          </el-form-item>
        </div>
      </el-col>
    </el-row>
    <el-divider content-position="left">货品信息</el-divider>
    <el-row>
      <el-col :span="6">
        <div ref="colRefs" @click="cleValidate('goodsname')">
          <el-form-item label="货品名称" prop="goodsname">
            <el-popover
              ref="goodsnamedict"
              placement="bottom"
              trigger="click"
              :width="eleWidth"
              class="salemanStyle"
              @show="
                $refs.goodsnameRef.bindData('projectData', '');
                selectValue = [];
                $refs.goodsnameRef.selectIdList = [];
              "
            >
              <TextGenerator
                ref="goodsnameRef"
                :select-value="selectValue"
                :billcode="'goods.goodsname'"
                @singleSel="getSelectItem"
                @deleteItemVal="deleteItem"
                @closedic="$refs['goodsnamedict'].doClose()"
              />
              <div slot="reference">
                <el-input
                  v-model="formdata.goodsname"
                  placeholder="请输入货品名称"
                  clearable
                  size="small"
                  @input="$emit('getpingyin', formdata.goodsname)"
                />
              </div>
            </el-popover>
          </el-form-item>
        </div>
      </el-col>

      <el-col :span="6">
        <div @click="cleValidate('goodsspec')">
          <el-form-item label="规格描述" prop="goodsspec">
            <el-popover
              ref="goodsspecdict"
              placement="bottom"
              trigger="click"
              :width="eleWidth"
              class="salemanStyle"
              @show="
                $refs.goodsspecRef.bindData('projectData', '');
                selectValue = [];
                $refs.goodsspecRef.selectIdList = [];
              "
            >
              <TextGenerator
                ref="goodsspecRef"
                :select-value="selectValue"
                :billcode="'goods.goodsspec'"
                @singleSel="getSelectItem"
                @deleteItemVal="deleteItem"
                @closedic="$refs['goodsspecdict'].doClose()"
              />
              <div slot="reference">
                <el-input
                  v-model="formdata.goodsspec"
                  placeholder="请输入货品名称"
                  clearable
                  size="small"
                />
              </div>
            </el-popover>
          </el-form-item>
        </div>
      </el-col>
      <el-col :span="6">
        <div @click="cleValidate('goodsunit')">
          <el-form-item label="货品单位" prop="goodsunit">
            <el-popover
              ref="goodsunitdict"
              placement="bottom"
              trigger="click"
              class="salemanStyle"
              :width="eleWidth"
              @show="$refs.goodsunitRef.bindData()"
            >
              <SelDict
                ref="goodsunitRef"
                :multi="0"
                :billcode="'goods.goodsunit'"
                @singleSel="
                  formdata.goodsunit = $event.dictvalue;
                  $refs['goodsunitdict'].doClose();
                  cleValidate('goodsunit');
                "
                @closedic="$refs['goodsunitdict'].doClose()"
              />
              <div slot="reference">
                <el-input
                  v-model="formdata.goodsunit"
                  placeholder="请选择货品单位"
                  clearable
                  size="small"
                />
              </div>
            </el-popover>
          </el-form-item>
        </div>
      </el-col>
      <el-col :span="6">
        <div @click="cleValidate('goodsstate')">
          <el-form-item label="货品状态" prop="goodsstate">
            <el-select
              v-model="formdata.goodsstate"
              placeholder="请选择状态"
              size="small"
              style="width: 100%"
              clearable
            >
              <el-option label="物料" value="物料" />
              <el-option label="成品" value="成品" />
              <el-option label="半成品" value="半成品" />
            </el-select>
          </el-form-item>
        </div>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="6">
        <div @click="cleValidate('surface')">
          <el-form-item label="表面处理" prop="surface">
            <el-popover
              ref="surfacedict"
              placement="bottom"
              trigger="click"
              class="salemanStyle"
              :width="eleWidth"
              @show="$refs.surfaceRef.bindData()"
            >
              <SelDict
                ref="surfaceRef"
                :multi="0"
                :billcode="'goods.surface'"
                @singleSel="
                  formdata.surface = $event.dictvalue;
                  $refs['surfacedict'].doClose();
                  cleValidate('surface');
                "
                @closedic="$refs['surfacedict'].doClose()"
              />
              <div slot="reference">
                <el-input
                  v-model="formdata.surface"
                  placeholder="请选择表面处理"
                  clearable
                  size="small"
                />
              </div>
            </el-popover>
          </el-form-item>
        </div>
      </el-col>
      <el-col :span="6">
        <div @click="cleValidate('material')">
          <el-form-item label="货品材质" prop="material">
            <el-popover
              ref="materialdict"
              placement="bottom"
              trigger="click"
              class="salemanStyle"
              :width="eleWidth"
              @show="$refs.materialRef.bindData()"
            >
              <SelDict
                ref="materialRef"
                :multi="0"
                :billcode="'goods.material'"
                @singleSel="
                  formdata.material = $event.dictvalue;
                  $refs['materialdict'].doClose();
                  cleValidate('material');
                "
                @closedic="$refs['materialdict'].doClose()"
              />
              <div slot="reference">
                <el-input
                  v-model="formdata.material"
                  placeholder="请选择货品材质"
                  clearable
                  size="small"
                />
              </div>
            </el-popover>
          </el-form-item>
        </div>
      </el-col>
      <el-col :span="6">
        <div @click="cleValidate('barcode')">
          <el-form-item label="条形码" prop="barcode">
            <el-input
              v-model="formdata.barcode"
              placeholder="条形码"
              clearable
              size="small"
            />
          </el-form-item>
        </div>
      </el-col>
      <el-col :span="6">
        <div @click="cleValidate('goodspinyin')">
          <el-form-item label="货品拼音" prop="goodspinyin">
            <el-input
              v-model="formdata.goodspinyin"
              placeholder="请输入货品拼音"
              clearable
              size="small"
            />
          </el-form-item>
        </div>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="6">
        <div @click="cleValidate('groupname')">
          <el-form-item label="相关厂商" prop="groupname">
            <GroupAutoComplete
              :baseurl="'/sale/D01M01B2/getOnlinePageList'"
              :type="'供应商'"
              :value="formdata.groupname"
              @setRow="
                $emit('setGroupRow', $event);
                cleValidate('groupname');
              "
            />
          </el-form-item>
        </div>
      </el-col>
      <el-col :span="6">
        <div @click="cleValidate('inprice')">
          <el-form-item label="建议进价" prop="inprice">
            <el-input
              v-model="formdata.inprice"
              placeholder="建议进价"
              clearable
              size="small"
            />
          </el-form-item>
        </div>
      </el-col>
      <el-col :span="6">
        <div @click="cleValidate('outprice')">
          <el-form-item label="建议售价" prop="outprice">
            <el-input
              v-model="formdata.outprice"
              placeholder="建议售价"
              clearable
              size="small"
            />
          </el-form-item>
        </div>
      </el-col>

      <el-col :span="6">
        <div @click="cleValidate('partid')">
          <el-form-item label="外部编码" prop="partid">
            <el-input
              v-model="formdata.partid"
              placeholder="请输入外部编码"
              clearable
              size="small"
            />
          </el-form-item>
        </div>
      </el-col>
    </el-row>
    <el-divider content-position="left">库存属性</el-divider>
    <el-row>
      <el-col :span="6">
        <div @click="cleValidate('storeid')">
          <el-form-item label="默认仓库" prop="storeid">
            <el-select
              v-model="formdata.storeid"
              placeholder="请选择仓库"
              style="width: 100%"
              size="small"
              @change="$emit('changeStore', $event)"
            >
              <el-option
                v-for="item in storeOptions"
                :key="item.id"
                :label="item.storename"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </div>
      </el-col>
      <el-col :span="6">
        <div @click="cleValidate('storelistname')">
          <el-form-item label="授权仓库" prop="storelistname">
            <el-select
              v-model="copystorelist"
              multiple
              style="width: 100%"
              size="small"
              @change="$emit('selStoreList', $event)"
            >
              <el-option
                v-for="item in storeOptions"
                :key="item.id"
                :value="item.id"
                :label="item.storename"
              />
            </el-select>
          </el-form-item>
        </div>
      </el-col>
      <el-col :span="6">
        <div @click="cleValidate('safestock')">
          <el-form-item label="安全库存" prop="safestock">
            <el-input
              v-model="formdata.safestock"
              placeholder="安全库存"
              clearable
              size="small"
            />
          </el-form-item>
        </div>
      </el-col>
      <el-col :span="6">
        <div style="line-height: 40px; margin-left: 20px">
          <el-checkbox
            v-model="formdata.batchmg"
            label="批次存储"
            :true-label="1"
            :false-label="0"
          />
          <el-checkbox
            v-model="formdata.batchonly"
            label="批次独立"
            :true-label="1"
            :false-label="0"
          />
          <el-checkbox
            v-model="formdata.packsnmark"
            label="SN包装"
            :true-label="1"
            :false-label="0"
          />
          <el-checkbox
            v-model="formdata.skumark"
            label="SKU"
            :true-label="1"
            :false-label="0"
          />
        </div>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="6">
        <div @click="cleValidate('quickcode')">
          <el-form-item label="快码字段" prop="quickcode">
            <el-input
              v-model="formdata.quickcode"
              placeholder="请输入快码字段"
              clearable
              size="small"
            />
          </el-form-item>
        </div>
      </el-col>
      <el-col :span="6">
        <div @click="cleValidate('brandname')">
          <el-form-item label="品牌" prop="brandname">
            <el-popover
              ref="brandnamedict"
              placement="bottom"
              trigger="click"
              class="salemanStyle"
              :width="eleWidth"
              @show="$refs.brandnameRef.bindData()"
            >
              <SelDict
                ref="brandnameRef"
                :multi="0"
                :billcode="'goods.brandname'"
                @singleSel="
                  formdata.brandname = $event.dictvalue;
                  $refs['brandnamedict'].doClose();
                  cleValidate('material');
                "
                @closedic="$refs['brandnamedict'].doClose()"
              />
              <div slot="reference">
                <el-input
                  v-model="formdata.brandname"
                  placeholder="请选择品牌"
                  clearable
                  size="small"
                />
              </div>
            </el-popover>
          </el-form-item>
        </div>
      </el-col>
      <el-col :span="6">
        <div @click="cleValidate('alertsqty')">
          <el-form-item label="预警数量" prop="alertsqty">
            <el-input
              v-model="formdata.alertsqty"
              placeholder="预警数量"
              clearable
              size="small"
            />
          </el-form-item>
        </div>
      </el-col>
      <el-col :span="6">
        <div>
          <el-form-item label="" label-width="22px">
            <el-checkbox
              v-model="formdata.enabledmark"
              label="状态"
              :true-label="1"
              :false-label="0"
            />
          </el-form-item>
        </div>
      </el-col>
    </el-row>
    <el-divider content-position="left">
      <span
        style="cursor: pointer"
        @click="ishowAdd = !ishowAdd"
      >附加信息
        <i :class="ishowAdd ? 'el-icon-arrow-down' : 'el-icon-arrow-up'" /></span>
    </el-divider>
    <el-col v-show="ishowAdd" :span="6" style="min-width: 308px">
      <el-row>
        <el-form-item label="多单位" prop="unitjson">
          <el-input
            v-model="MultiUnit"
            placeholder="多单位"
            readonly
            size="small"
            style="width: 100%;"
          >
            <el-button
              slot="append"
              class="filter-item"
              size="mini"
              @click="openUnitjson"
            >编辑
            </el-button>
          </el-input>
        </el-form-item>
        <!-- <el-form-item label="物料单位" prop="matqtyunit">
          <div style="display: flex; justify-content: space-between">
            <el-select
              v-model="formdata.matqtyunit"
              placeholder="请选择"
              size="small"
              style="width: 100%"
            >
              <el-option label="主单位" :value="0"></el-option>
              <el-option label="重量" :value="1"></el-option>
              <el-option label="面积" :value="2"></el-option>
              <el-option label="体积" :value="3"></el-option>
            </el-select>
          </div>
        </el-form-item> -->
      </el-row>
      <el-row>
        <el-form-item label="单件重量" prop="weightqty">
          <div style="display: flex; justify-content: space-between">
            <el-input
              v-model="formdata.weightqty"
              placeholder="单件重量"
              clearable
              size="small"
            />
            <el-input
              v-model="formdata.weightunit"
              placeholder="重量单位"
              size="small"
              style="width: 140px; margin-left: 4px"
            />
          </div>
        </el-form-item>
      </el-row>
      <el-row>
        <el-form-item label="单件面积" prop="areaqty">
          <div style="display: flex; justify-content: space-between">
            <el-input
              v-model="formdata.areaqty"
              placeholder="单件面积"
              clearable
              size="small"
            />
            <el-input
              v-model="formdata.areaunit"
              placeholder="面积单位"
              size="small"
              style="width: 140px; margin-left: 4px"
            />
          </div>
        </el-form-item>
      </el-row>
      <el-row>
        <el-form-item label="单件体积" prop="volumeqty">
          <div style="display: flex; justify-content: space-between">
            <el-input
              v-model="formdata.volumeqty"
              placeholder="单件体积"
              clearable
              size="small"
            />
            <el-input
              v-model="formdata.volumeunit"
              placeholder="体积单位"
              size="small"
              style="width: 140px; margin-left: 4px"
            />
          </div>
        </el-form-item>
      </el-row>
      <el-row>
        <el-form-item label="包装数量" prop="packqty">
          <div style="display: flex; justify-content: space-between">
            <el-input
              v-model="formdata.packqty"
              placeholder="包装数量"
              clearable
              size="small"
            />
            <el-input
              v-model="formdata.packunit"
              placeholder="包装单位"
              size="small"
              style="width: 140px; margin-left: 4px"
            />
          </div>
        </el-form-item>
      </el-row>
    </el-col>
    <el-col v-show="ishowAdd" :span="8" style="min-width: 308px">
      <div v-if="goodsCustList.length" class="hybrid-collapse">
        <div
          v-for="(item, index) in goodsCustListChild"
          :key="index"
          class="collapse-item"
        >
          <div v-if="item.Type == 'group'">
            <div
              v-if="item.children"
              class="collapse-header"
              @click="item.isShow = !item.isShow"
            >
              <i
                :class="
                  !item.isShow
                    ? 'el-icon-circle-plus-outline'
                    : 'el-icon-remove-outline'
                "
              />
              <span style="margin-left: 4px">{{ item.custname }}</span>
            </div>
            <template>
              <div
                v-for="(i, num) in item.children"
                v-show="item.isShow"
                :key="num"
                class="header-row"
                style="border-bottom: 1px solid #ebeef5"
              >
                <div class="header-row-title">
                  {{ i.custname }}
                </div>
                <div style="flex: 1; margin: 0px 20px">
                  <el-select
                    v-if="i.valuejson"
                    v-model="formdata[i.custkey]"
                    default-first-option
                    allow-create
                    filterable
                    clearable
                    style="width: 100%"
                    size="small"
                    :placeholder="i.custname"
                    @change="$emit('setCustList')"
                  >
                    <el-option
                      v-for="a in i.valuejson.split(',')"
                      :key="a"
                      :label="a"
                      :value="a"
                    />
                  </el-select>
                  <el-input
                    v-else
                    v-model="formdata[i.custkey]"
                    :placeholder="i.custname"
                    clearable
                    size="small"
                    style="width: 100%"
                    @blur="$emit('setCustList')"
                  />
                </div>
              </div>
            </template>
          </div>
          <div
            v-else
            class="header-row"
            style="border-bottom: 1px solid #ebeef5"
          >
            <div class="header-row-title">
              {{ item.custname }}
            </div>
            <div style="flex: 1; margin: 0px 20px">
              <el-select
                v-if="item.valuejson"
                v-model="formdata[item.custkey]"
                default-first-option
                allow-create
                filterable
                clearable
                style="width: 100%"
                size="small"
                :placeholder="item.custname"
                @change="$emit('setCustList')"
              >
                <el-option
                  v-for="a in item.valuejson.split(',')"
                  :key="a"
                  :label="a"
                  :value="a"
                />
              </el-select>
              <el-input
                v-else
                v-model="formdata[item.custkey]"
                :placeholder="item.custname"
                clearable
                size="small"
                style="width: 100%"
                @blur="$emit('setCustList')"
              />
            </div>
          </div>
        </div>
      </div>
      <div v-else class="hybrid-collapse">
        <el-empty
          description="无定义项"
          :image-size="100"
          style="padding: 15px 0"
        />
      </div>
    </el-col>
    <el-col v-show="ishowAdd" :span="4" style="min-width: 200px">
      <el-form-item label="附图" prop="goodsphoto1">
        <div v-if="!!formdata.id" class="cardphotoImg">
          <img
            :src="
              formdata.goodsphoto1
                ? formdata.goodsphoto1
                : require('@/assets/404_images/noFace.jpg')
            "
            alt=""
            @click="$emit('openImgInfo')"
          >
          <div
            v-if="!!formdata.goodsphoto1"
            class="imgClose"
            @click="formdata.goodsphoto1 = ''"
          >
            <i class="el-icon-close" />
          </div>
        </div>
        <div v-else style="color: #666">请先保存货品后，再添加图片!</div>
      </el-form-item>
    </el-col>
  </el-form>
</template>
<script>
import TextGenerator from '@/components/TextGenerator/select.vue'
export default {
  components: {
    TextGenerator
  },
  props: {
    formdata: {
      type: Object
    },
    title: {
      type: String
    },
    storeOptions: {
      type: Array
    },
    groupnameOptions: {
      type: Array
    },
    storelist: {
      type: Array
    },
    customDataList: {
      type: Array
    },
    goodsCustList: {
      type: Array
    }
  },
  data() {
    return {
      formRules: {
        goodsname: [
          { required: true, trigger: 'blur', message: '货品名称为必填项' }
        ],
        goodsunit: [
          { required: true, trigger: 'blur', message: '货品单位为必填项' }
        ],
        goodsstate: [
          { required: true, trigger: 'blur', message: '货品状态为必填项' }
        ],
        safestock: [
          { required: true, trigger: 'blur', message: '安全库存为必填项' }
        ],
        inprice: [
          { required: true, trigger: 'blur', message: '建议进价为必填项' }
        ],
        outprice: [
          { required: true, trigger: 'blur', message: '建议售价为必填项' }
        ],
        storeid: [
          { required: true, trigger: 'blur', message: '默认仓库为必填项' }
        ],
        ivquantity: [
          { required: true, trigger: 'blur', message: '库存数量为必填项' }
        ],
        ageprice: [
          { required: true, trigger: 'blur', message: '库存单价为必填项' }
        ],
        uidgroupguid: [
          { required: true, trigger: 'blur', message: '分组名称为必填项' }
        ],
        goodsuid: [
          { required: true, trigger: 'blur', message: '货品编码为必填项' }
        ]
      },
      ishowAdd: false,
      defaultProps: {
        children: 'children',
        label: 'groupname',
        value: 'id'
      },
      copystorelist: [],
      eleWidth: '', // 字典宽度
      selectValue: [], // 选择的名称数组
      activeNames: [],
      goodsCustListChild: []
    }
  },
  computed: {
    MultiUnit() {
      if (typeof this.formdata.unitjson === 'string') {
        this.formdata.unitjson = JSON.parse(this.formdata.unitjson)
      }
      if (!this.formdata.unitjson?.length) return ''
      return this.formdata.unitjson
        .map((item) => {
          if (item.name) {
            return `(${item.sqty} ${item.name} = ${item.mqty} ${this.formdata.goodsunit})`
          }
          return null
        })
        .filter(Boolean) // Remove null/empty values
        .join(',')
    }
  },
  watch: {
    storelist: function(val, oldVal) {
      this.copystorelist = this.storelist
    },
    copystorelist: function(val, oldVal) {
      this.$emit('setStoreList', this.copystorelist)
    }
  },
  mounted() {
    window.addEventListener('resize', () => {
      this.eleWidth = this.SelDict()
    })
    this.eleWidth = this.SelDict()
    this.bindTreeData()
  },
  methods: {
    openUnitjson() {
      this.$emit('openUnitjson')
    },
    cleValidate(val) {
      this.$refs.formdata.clearValidate(val)
    },
    // 字段宽度计算
    SelDict() {
      const ele = this.$refs.colRefs
      return `${ele.offsetWidth - 100}`
    },
    // 返回上一级
    deleteItem(type) {
      const name = type.split('.')[1]
      this.selectValue.pop()
      let str = ''
      for (let i = 0; i < this.selectValue.length; i++) {
        str += `${this.selectValue[i].tgcode}${this.selectValue[i].tgvalue}`
      }
      this.formdata[name] = str
    },
    // 选择值格式格式化
    getSelectItem(item, type) {
      const name = type.split('.')[1]
      const index = this.selectValue.findIndex(
        (items) => items.tgvalue === item.tgvalue
      )
      if (index < 0) {
        this.selectValue.push(item)
      }

      let str = ''
      for (let i = 0; i < this.selectValue.length; i++) {
        str += `${this.selectValue[i].tgcode}${this.selectValue[i].tgvalue}`
      }
      this.formdata[name] = str
    },
    toggleExpand(index) {
      this.$set(
        this.goodsCustListChild[index],
        'isShow',
        !this.goodsCustListChild[index].isShow
      )
      console.log(index, this.goodsCustListChild)
    },
    bindTreeData() {
      if (!this.goodsCustList.length) {
        return
      }
      this.$request
        .get('/system/SYSM07B8/getListByModuleCode?Code=D91M01S4')
        .then((res) => {
          if (res.data.code == 200) {
            const resdata = res.data.data.map((v) => {
              return {
                id: v.id,
                custgroupid: v.parentid,
                custname: v.groupname,
                Type: 'group'
              }
            })
            var arr = [...resdata, ...this.goodsCustList]
            this.goodsCustListChild = this.transData(
              arr,
              'id',
              'custgroupid',
              'children'
            )
            for (var i = 0; i < this.goodsCustListChild.length; i++) {
              var Item = this.goodsCustListChild[i]
              this.$set(Item, 'isShow', true)
            }
            console.log(this.goodsCustListChild)
          }
        })
    },
    transData(a, idStr, pidStr, chindrenStr) {
      var r = []
      var hash = {}
      var id = idStr
      var pid = pidStr
      var children = chindrenStr
      var i = 0
      var j = 0
      var len = a.length
      for (; i < len; i++) {
        hash[a[i][id]] = a[i]
      }
      for (; j < len; j++) {
        var aVal = a[j]
        var hashVP = hash[aVal[pid]]
        if (hashVP) {
          !hashVP[children] && (hashVP[children] = [])
          hashVP[children].push(aVal)
        } else {
          r.push(aVal)
        }
      }
      return r
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep.custInfo .el-row {
  margin-bottom: -20px;
  display: flex;
  flex-wrap: wrap;
}
.getNextCode {
  font-size: 15px;
  color: #409eff;
  cursor: pointer;
  border-left: 1px solid #dcdfe6;
  padding: 7px 10px;
}
.cardphotoImg {
  cursor: pointer;
  width: 120px;
  height: 120px;
  overflow: hidden;
  border-radius: 10px;
  border: 1px solid #dcdfe6;
  position: relative;
}
.cardphotoImg img {
  width: 100%;
  height: 100%;
}
.cardphotoImg:hover .imgClose {
  position: absolute;
  top: 0;
  right: 0px;
  padding: 0 10px;
  color: #fff;
  background: rgba(0, 0, 0, 0.521);
  line-height: 25px;
  border-radius: 2px;
}
.hybrid-collapse {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin: 0 20px;
  height: 210px;
  overflow-y: auto;
}

.collapse-item {
  margin-bottom: 2px;
}

.collapse-header {
  padding: 6px 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  background: #efefef;
  font-weight: bold;
  font-size: 14px;
}

.header-row {
  display: flex;
  align-items: center;
}
.header-row-title {
  width: 30%;
  min-width: 120px;
  border-right: 1px solid #ebeef5;
  padding: 10px 20px;
  text-align: center;
  background: #fbfbfb;
  font-size: 14px;
}
</style>
