export const formTemp = {
  header: {
    type: 0,
    title: "",
    content: [
      {
        type: "divider",
        center: "left",
        label: "编码规则",
      },
      {
        type: 'form',
        rowitem: [
          {
            col: 5,
            code: "partgroupid",
            label: "分组名称",
            type: "select",
            methods: "",
            param: "",
            required: true,

          },
        ]
      },
      {
        rowitem: [
          {
            col: 5,
            code: "itemname",
            label: "名称",
            type: "input",
            methods: "",
            param: "",
            required: true
          },
          {
            col: 5,
            code: "itemtype",
            label: "类型",
            type: "input",
            methods: "",
            param: ""
          },
          {
            col: 5,
            code: "enabledmark",
            label: "有效",
            type: "checkbox",
            methods: "",
            param: ""
          }
        ]
      },
      {
        rowitem: [
          {
            col: 5,
            code: "itemspec",
            label: "规格",
            type: "input",
            methods: "",
            param: ""
          },
          {
            col: 5,
            code: "itemunit",
            label: "单位",
            type: "input",
            methods: "",
            param: ""
          },
          {
            col: 2,
            code: "allowedit",
            label: "允许编辑",
            type: "checkbox",
            methods: "",
            param: ""
          },
          {
            col: 2,
            code: "allowdelete",
            label: "允许删除",
            type: "checkbox",
            methods: "",
            param: ""
          }
        ]
      },
      {
        type: "divider",
        center: "left",
        label: "价格设置",
      },
      {
        rowitem: [
          {
            col: 5,
            code: "baseprice",
            label: "基础价",
            type: "input",
            methods: "countPrice",
            param: ""
          },
          {
            col: 5,
            code: "rebate",
            label: "折扣",
            type: "number",
            methods: "countPrice",
            param: ""
          },
          {
            col: 5,
            code: "rebatesec",
            label: "二级折扣",
            type: "number",
            methods: "countPrice",
            param: ""
          },
        ]
      },
      {
        rowitem: [
          {
            col: 5,
            code: "pricemin",
            label: "最小单价",
            type: "input",
            methods: "",
            param: ""
          },
          {
            col: 5,
            code: "pricemax",
            label: "最大单价",
            type: "input",
            methods: "",
            param: ""
          },
          {
            col: 5,
            code: "price",
            label: "单价",
            type: "text",
            methods: "",
            param: ""
          },
        ]
      },
    ],
  },

  footer: {
    type: 0,
    content: [
      {
        type: "divider",
        center: "left",
        label: "",
      },
      {
        rowitem: [
          {
            col: 22,
            code: "summary",
            label: "摘要",
            type: "input",
            methods: "",
            param: ""
          },
        ]
      },
      {
        type: "foot",
        rowitem: [
          {
            col: 4,
            code: "createby",
            label: "创建人",
            type: "text"
          },
          {
            col: 4,
            code: "createdate",
            label: "创建日期",
            type: "text"
          },
          {
            col: 4,
            code: "lister",
            label: "制表",
            type: "text"
          },
          {
            col: 4,
            code: "modifydate",
            label: "修改日期",
            type: "text"
          },
        ]
      }
    ],
  },
  th: {
    type: 0,
    content: []
  },
  list: {
    type: 0,
    content: []
  },
  item: {
    type: 0,
    content: []
  },
}