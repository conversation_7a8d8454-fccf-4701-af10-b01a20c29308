<template>
  <div>
    <div
      class="fileList"
      :class="showFileList ? 'showFileList' : 'hideFileList'"
    >
      <div v-if="lst.length != 0">
        <div
          class="fileUpload fileList-item"
          v-for="(i, index) in lst"
          :key="index"
        >
          <div class="fileUploadShow">
            <img
              :src="
                i.id
                  ? require('@/assets/knows_images/' +
                      getFileType(i.filesuffix) +
                      '.png')
                  : require('@/assets/knows_images/other.png')
              "
              alt=""
            />
          </div>
          <div class="fileUploadInfo" v-if="i.id">
            <p class="ellipsis">名称：{{ i.fileoriname }}</p>
            <p>大小：{{ i.filesize ? getFileSize(i.filesize) : 0 + "KB" }}</p>
            <p>类型：{{ getFileType(i.filesuffix) }}</p>
            <a
              class="downFileBtn"
              :href="i.fileurl"
              :download="i.fileoriname"
              target="_blank"
              >下载文件</a
            >
          </div>
        </div>
      </div>
      <div v-else v-show="showFileList" class="noData">暂无文件内容</div>
    </div>

    <div v-show="false">
      <input ref="upload" type="file" @change="getFile" />
    </div>
  </div>
</template>
<script>
import request from "@/utils/request";
export default {
  props: ["idx"],
  data() {
    return {
      file: {},
      uploadFile: "",
      uploadFileName: "",
      uploadFileSize: 0,
      uploadFileType: "",
      lst: [],
      showFileList: true,
      queryParams: {
        PageNum: 1,
        PageSize: 10,
        OrderType: 1,
        SearchType: 1,
      },
    };
  },
  created() {
    // this.bindData();
  },
  methods: {
    bindData() {
      this.queryParams.SearchPojo = { relateid: this.idx };
      request
        .post("/utils/D96M03B1/getPageList", JSON.stringify(this.queryParams))
        .then((res) => {
          if (res.data.code == 200) {
            this.lst = res.data.data.list;
          }
        });
    },
    getFile() {
      const inputDOM = this.$refs.upload;
      const file = inputDOM.files[0];
      console.log(file);
      this.file = file;
      this.uploadFileName = file.name;
      this.uploadFileSize = Number(file.size / 1024).toFixed(2);
      //   if(this.uploadFileSize>1024){ //+'KB'
      //     this.uploadFileSize=Number(file.size / 1024/1024).toFixed(2)+'MB'
      //   }
      this.uploadFileType = this.getFileType(file.name);
      this.uploadFile = require("@/assets/knows_images/" +
        this.uploadFileType +
        ".png");
    },
    submitUploadFile() {
      var formData = new FormData();
      formData.append("file", this.file);
      request
        .post("/utils/D96M03B1/upload?relateid=" + this.idx, formData)
        .then((res) => {
          if (res.data.code == 200) {
            this.$message.success("文件上传成功");
            // this.$emit('bindData');
            this.file = {};
            this.uploadFileName = "";
            this.uploadFileType = "";
            this.uploadFileSize = 0;
            this.$emit("closeDialog");
          } else {
            this.$message.warning(res.data.msg || "文件上传失败");
          }
        })
        .catch((er) => {
          this.$message.error("请求错误");
        });
    },
    // 删除
    delItem(row) {
      this.$confirm("此操作将永久删除该附件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          request
            .get("/utils/D96M03B1/delete?key=" + row.id)
            .then((res) => {
              if (res.data.code == 200) {
                this.$message.success("删除成功");
                this.bindData();
              } else {
                this.$message.warning(res.data.msg || "删除失败");
              }
            })
            .catch((er) => {
              this.$message.error("请求错误");
            });
        })
        .catch(() => {});
    },
    // 获取文件大小
    getFileSize(data) {
      var size = Number(data / 1024).toFixed(2) + "KB";
      return size;
    },
    // 获取文件类型
    getFileType(fileName) {
      // 根据后缀判断文件类型
      let fileSuffix = "";
      // 结果
      let result = "";
      try {
        let flieArr = fileName.split(".");
        fileSuffix = flieArr[flieArr.length - 1];
      } catch (err) {
        fileSuffix = "";
      }
      // fileName无后缀返回 false
      if (!fileSuffix) {
        result = false;
        return result;
      }

      // 匹配txt
      let txtlist = ["txt"];
      result = txtlist.some(function (item) {
        return item == fileSuffix;
      });
      if (result) {
        result = "txt";
        return result;
      }
      // 匹配 excel
      let excelist = ["xls", "xlsx"];
      result = excelist.some(function (item) {
        return item == fileSuffix;
      });
      if (result) {
        result = "excel";
        return result;
      }
      // 匹配 word
      let wordlist = ["doc", "docx"];
      result = wordlist.some(function (item) {
        return item == fileSuffix;
      });
      if (result) {
        result = "word";
        return result;
      }
      // 匹配 pdf
      let pdflist = ["pdf"];
      result = pdflist.some(function (item) {
        return item == fileSuffix;
      });
      if (result) {
        result = "pdf";
        return result;
      }
      // 匹配 ppt
      let pptlist = ["ppt", "pptx"];
      result = pptlist.some(function (item) {
        return item == fileSuffix;
      });
      if (result) {
        result = "ppt";
        return result;
      }
      result = "other";
      return result;
    },
  },
};
</script>
<style lang="scss" scoped>
.fileUpload {
  display: flex;
  align-items: center;
  //   margin-top: 10px;
  padding: 5px;
  position: relative;
  .fileUploadShow {
    width: 75px;
    height: 80px;
    text-align: center;
    padding: 5px;
    img {
      width: auto;
      height: 100%;
      max-width: 100%;
      min-width: 50px;
      // max-height: 100%;
    }
  }
  .fileUploadInfo {
    margin-left: 10px;
    line-height: 20px;
    width: calc(100% - 115px);
    p {
      margin: 0 !important;
    }
  }
}
.fileList {
  cursor: pointer;
  position: relative;
  height: 0;
  overflow: auto;

  .fileList-item {
    margin: 8px 0;
    border-radius: 5px;
    .closeBtn {
      position: absolute;
      right: 10px;
      top: 10px;
      i {
        font-size: 20px;
        font-weight: bold;
        color: rgb(235, 14, 14);
      }
      i:hover {
        background: rgb(248, 238, 238);
      }
    }
    .downFileBtn {
      color: #409eff;
    }
  }
  .fileList-item:hover {
    background: #c6e2ff;
  }
}
.showFileList {
  animation: showHeight 0.5s ease;
  -webkit-animation: showHeight 0.5s ease;
  animation-fill-mode: forwards;
  height: 0px;
}
@keyframes showHeight {
  from {
    height: 0px;
  }
  to {
    height: 220px;
  }
}

@-webkit-keyframes showHeight {
  from {
    height: 0px;
  }
  to {
    height: 220px;
  }
}
.hideFileList {
  animation: hideHeight 0.5s ease;
  -webkit-animation: hideHeight 0.5s ease;
  animation-fill-mode: forwards;
  height: 220px;
}
@keyframes hideHeight {
  from {
    height: 220px;
  }
  to {
    height: 0px;
  }
}

@-webkit-keyframes hideHeight {
  from {
    height: 220px;
  }
  to {
    height: 0px;
  }
}
.checkFile {
  margin: 8px auto;
  text-align: center;
  cursor: pointer;
  span {
    color: #409eff;
  }
}
.noData {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 22px;
  color: #c0c4cc;
}
.ellipsis {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-wrap: break-word;
}
</style>