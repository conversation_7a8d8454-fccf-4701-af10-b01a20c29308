<template>
  <div>
      <div v-if="imgList.length!=0">
            <el-carousel indicator-position="outside">
      <el-carousel-item v-for="item in imgList" :key="item">
        <img class="imgStyle" :src="item" alt="" />
      </el-carousel-item>
    </el-carousel>
      </div>
    <div v-else class="nodata">
        <img src="../../../../../../assets/404_images/noFace.jpg" alt="" />
    </div>
  </div>
</template>
<script>
import request from "@/utils/request";
import lrz from "lrz";
export default {
  props: ["idx","imgList"],
  data() {
    return {
    //   imgList: [],
      queryParams: {
        PageNum: 1,
        PageSize: 10,
        OrderType: 1,
        SearchType: 1,
      },
    };
  },
  created() {
    // this.bindData();
  },
  methods: {
    bindData() {
      this.queryParams.SearchPojo = { relateid: this.idx };
      request
        .post("/utils/D96M05B1/getPageList", JSON.stringify(this.queryParams))
        .then((res) => {
          if (res.data.code == 200) {
              this.imgList=[]
            // this.lst = res.data.data.list;
            var list= res.data.data.list;
            if(list.length!=0){
                 for(var i=0;i<list.length;i++){
                this.imgList.push(list[i].fileurl)
            }
            }

          }
        });
    },
  },
};
</script>
<style lang="scss" scoped>
.imgStyle{
    width: 100%;
    height: 100%;
}
.nodata{
    img{
        margin: 0 auto;
    }
}
</style>
