/**
 * 2023-05-19 hu
 * params App_Workgroup主表参数
 * 
 */

var params = [
    "id",  //id
    "custjson",// <PERSON><PERSON><PERSON><PERSON>
    "unitjson",// unitjson
    "goodsuid",  //货品编码
    "goodsname",  //名称
    "goodsspec",  //规格
    "goodsunit",  //货品单位
    "goodsstate",  //货品状态
    "goodspinyin",  //拼音码
    "versionnum",  //版本号
    "material",  //材质
    "surface",  //表面处理
    "barcode",  //条形码
    "safestock",  //安全库存
    "inprice",  //建议进价
    "outprice",  //建议售价
    "groupid",  //关联厂商
    "groupname",
    "fileguid",  //相关附件
    "drawing",  //图档名称
    "taxrate",  //默认税率
    "storeid",  //默认仓库
    "storelistname",  //受权仓库
    "storelistguid",  //受权仓库guid
    "ivquantity",  //库存数量
    "ageprice",  //库存单价
    "uidgroupguid",  //分组guid
    "uidgroupcode",  //分组编码
    "uidgroupname",  //分组名称
    "uidgroupnum",  //分组序号
    "partid",  //外部编码
    "pid",  //主料号id
    "puid",  //主料号
    "enabledmark",  //有效标识
    "goodsphoto1",  //附图1
    "goodsphoto2",  //附图2
    "remark",  //备注
    // "createby",  //创建者
    // "createbyid",  //创建者id
    // "createdate",  //新建日期
    // "lister",  //制表
    // "listerid",  //制表id
    // "modifydate",  //修改日期
    // "deletemark",  //删除标识
    // "deletelister",  //删除人员
    // "deletelisterid",  //删除人员id
    // "deletedate",  //删除日期
    "batchmg",  //批次管理
    "batchonly",  //批次独立
    "skumark",  //sku库存
    "packsnmark",  //sn包装
    "virtualitem",  //虚拟货品
    "bomid",  //标准bomid
    "quickcode",  //快速码
    "brandname",  //品牌
    "buyremqty",  //采购在订
    "wkwsremqty",  //生产在制
    "wkscremqty",  //加工在制
    "busremqty",  //销售待出
    "mrpremqty",  //运算占用
    "requremqty",  //领料待出
    "alertsqty",  //预警数量
    "intqtymark",  //整数结算
    "weightqty",  //单件重量
    "weightunit",  //重量单位
    "lengthqty",  //单件长度
    "lengthunit",  //长度单位
    "areaqty",  //单件面积
    "areaunit",  //面积单位
    "volumeqty",  //单件体积
    "volumeunit",  //体积单位
    "packqty",  //包装数量
    "packunit",  //包装单位
    "matqtyunit",  //物料0主1重量2面积3体积
    "overflowqty",  //最高库存
    "custom1",  //自定义1
    "custom2",  //自定义2
    "custom3",  //自定义3
    "custom4",  //自定义4
    "custom5",  //自定义5
    "custom6",  //自定义6
    "custom7",  //自定义7
    "custom8",  //自定义8
    "custom9",  //自定义9
    "custom10",  //自定义10
    // "deptid",  //部门id
    // "tenantid",  //租户id
    // "tenantname",  //租户名称
    // "revision",  //乐观锁

]

export default { params }