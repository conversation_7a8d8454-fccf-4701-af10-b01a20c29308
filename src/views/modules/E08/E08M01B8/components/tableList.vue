<template>
  <div>
    <ve-table
      ref="tableList"
      :key="keynum"
      row-key-field-name="id"
      :max-height="tableMaxHeight"
      :scroll-width="tableMinWidth"
      :style="{ 'word-break': 'break-all' }"
      is-horizontal-resize
      :fixed-header="true"
      :columns="customData"
      :table-data="lst"
      border-x
      border-y
      :border-around="true"
      :column-hidden-option="{ defaultHiddenColumnKeys: columnHidden }"
      :column-width-resize-option="{ enable: true, minWidth: 50 }"
      :virtual-scroll-option="virtualScrollOption"
      :checkbox-option="checkboxOption"
      :footer-data="footerData"
      :fixed-footer="true"
      :event-custom-option="eventCustomOption"
      :sort-option="sortOption"
    />
    <div v-show="total > 0" class="flex j-s a-c">
      <div class="flex a-c">
        <Pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.PageNum"
          :limit.sync="queryParams.PageSize"
          @pagination="getList"
        />
      </div>
      <div style="margin-right: 40px">
        <Scene
          ref="scene"
          :code="tableForm.formcode"
          @bindData="bindData"
        />
      </div>
    </div>
    <PrintServer
      ref="PrintServer"
      :formdata="selectList"
      :printcode="'D91M01B1Edit'"
      :commonurl="'/e08/E08M13B1/printBatchBill'"
      :weburl="'/e08/E08M13B1/printBatchWebBill'"
    />
    <PrintServer
      ref="PrintPageServer"
      :formdata="lst"
      :query-params="queryParams"
      :printcode="'D91M01B1Th'"
      :commonurl="'/e08/E08M13B1/printPageList'"
      :weburl="'/e08/E08M13B1/printWebPageList'"
    />
  </div>
</template>
<script>
import request from '@/utils/request'
import { tableList } from './tablecolums.js'
import Pictures from './formTemp/pictures.vue' // 导入货品图片
import FileList from './formTemp/FileList.vue' // 导入货品附言
export default {
  components: {
    Pictures,
    FileList
  },
  props: ['online', 'formtemplate'],
  data() {
    return {
      lst: [], // 数据列表数组
      total: 0, // 条数
      idx: 0, // 当前的id
      keynum: 0, // 表格的key
      // 分页参数
      queryParams: {
        PageNum: 1,
        PageSize: 20,
        OrderType: 1,
        SearchType: 1
      },
      tableForm: tableList, // 表格列数据
      customList: [], // spu数组
      selectList: [], // table数据被选中的数组
      goodsInfo: {},
      totalfields: ['refno'], // 合计字段
      exportitle: '货品信息', // 导出 表名称
      imgList: [], // 附图
      postscriptContent: '', // 附言
      filelist: [], // 附件
      // =======================
      customData: [], // 列数据
      columnHidden: [], // 列隐藏
      footerData: [], // 底部合计行
      // 获取选中单元格信息
      cellTotal: 0,
      cellNum: 0,
      checkboxOption: {
        selectedRowKeys: [],
        // 单选
        selectedRowChange: ({ row, isSelected, selectedRowKeys }) => {
          this.checkboxOption.selectedRowKeys = selectedRowKeys
          if (selectedRowKeys.includes(row.id)) {
            this.selectList.push(row)
          } else {
            var index = this.selectList.findIndex((a) => a.id == row.id)
            if (index != -1) {
              this.selectList.splice(index, 1)
            }
          }
        },
        // 全选
        selectedAllChange: ({ isSelected, selectedRowKeys }) => {
          if (isSelected) {
            this.checkboxOption.selectedRowKeys = selectedRowKeys
            if (selectedRowKeys.length != 0) {
              for (var i = 0; i < selectedRowKeys.length; i++) {
                this.selectList.push({ id: selectedRowKeys[i] })
              }
            }
          } else {
            this.selectList = []
            this.checkboxOption.selectedRowKeys = []
          }
        }
      },
      // 点击单元格事件
      eventCustomOption: {
        bodyCellEvents: ({ row, column, rowIndx }) => {
          return {
            mouseup: (event) => {
              this.countCellData()
            }
          }
        }
      },
      // 虚拟滚动
      rowScroll: 0,
      virtualScrollOption: {
        enable: true,
        scrolling: ({ startRowIndex }) => {
          this.rowScroll = startRowIndex
        }
      },
      // 排序
      sortOption: {
        sortChange: (params) => {
          this.changeSort(params)
        }
      }
    }
  },
  computed: {
    // 表格最大高度
    tableMaxHeight() {
      var tableMaxHeight = window.innerHeight - 160
      if (tableMaxHeight < 600) {
        tableMaxHeight = 600
      }
      return tableMaxHeight + 'px'
    },
    // 表格最小宽度
    tableMinWidth() {
      var tableWidth = 'calc(100vw - 64px)'
      if (this.tableForm.item.length != 0) {
        tableWidth = 0
        for (var i = 0; i < this.tableForm.item.length; i++) {
          var Item = this.tableForm.item[i]
          if (Item.displaymark) {
            tableWidth += Number(Item.minwidth)
          }
        }
      }
      return tableWidth
    }
  },
  methods: {
    // 数据绑定
    bindData() {
      if (
        !!this.$refs.scene &&
        this.$refs.scene.radio != -1 &&
        this.total != 0
      ) {
        this.queryParams.scenedata = JSON.parse(
          this.$refs.scene.lst[this.$refs.scene.radio].scenedata
        )
      } else {
        if (this.queryParams.SearchType == 1) {
          this.$delete(this.queryParams, 'scenedata')
        }
        if (this.$refs.scene) {
          this.$refs.scene.radio = -1
        }
      }
      var BaseUrl = '/e08/E08M13B1/getPageList'
      request
        .post(BaseUrl, JSON.stringify(this.queryParams))
        .then((res) => {
          if (res.data.code == 200) {
            this.lst = res.data.data.list
            this.total = res.data.data.total
            for (var i = 0; i < this.lst.length; i++) {
              var Item = this.lst[i]
              var jsonData = Item.custjson ? JSON.parse(Item.custjson) : []
              for (var j = 0; j < jsonData.length; j++) {
                this.$set(this.lst[i], jsonData[j].key, jsonData[j].value)
              }
            }
          } else {
            this.$message.warning(res.data.msg || '请求失败')
          }
        })
        .catch((error) => {
          this.$message.error(error || '请求错误')
        })
    },
    // 获取用户自定义spu列
    async getColumn() {
      var colunmList = tableList
      if (this.formtemplate.list.type) {
        colunmList.item = this.formtemplate.list.content
      }
      this.$getColumn(this.tableForm.formcode, colunmList, 0, 1).then(
        (data) => {
          this.customList = data.customList
          this.tableForm = Object.assign({}, data.colList) // 表头
          this.initTable(this.tableForm)
          this.$emit('sendTableForm', this.tableForm)
        }
      )
    },
    // 初始化表格
    initTable(data) {
      var customData = []
      this.columnHidden = []
      data['item'].forEach((item, index) => {
        var obj = {
          field: item.itemcode,
          key: item.itemcode,
          title: item.itemname,
          width: !isNaN(item.minwidth) ? Number(item.minwidth) : item.minwidth,
          displaymark: item.displaymark,
          fixed: item.fixed ? (item.fixed == 1 ? 'left' : 'right') : false,
          ellipsis: item.overflow ? { showTitle: true } : false,
          align: item.aligntype ? item.aligntype : 'center',
          sortBy: item.sortable ? '' : false,
          renderBodyCell: ({ row, column, rowIndex }, h) => {
            var html = null
            if (
              item.itemcode == 'createdate' ||
              item.itemcode == 'modifydate'
            ) {
              return this.$options.filters.dateFormats(row[item.itemcode])
            } else if (item.itemcode == 'goodsuid') {
              html = (
                <el-button
                  type='text'
                  size='small'
                  style='padding: 4px 15px'
                  on-click={() => this.showForm(row.id)}
                >
                  {row[item.itemcode] ? row[item.itemcode] : '货品编码'}
                </el-button>
              )
              return html
            } else if (item.itemcode == 'ivquantity') {
              html = (
                <div>
                  <el-popover placement='left' trigger='click' title='货品信息'>
                    <div v-show={!!this.goodsInfo}>
                      <div class='ellipsis'>
                        <i class='el-icon-discount' style='color: #909399'></i>
                        货品编码：{this.goodsInfo.goodsuid}
                      </div>
                      <div class='ellipsis'>
                        <i class='el-icon-coin' style='color: #909399'></i>
                        货品名称：{this.goodsInfo.goodsname}
                      </div>
                      <div>
                        <i class='el-icon-s-home' style='color: #409eff'></i>
                        仓库名称：{this.goodsInfo.storename}
                      </div>
                      <div>
                        <i class='el-icon-s-order' style='color: #409eff'></i>
                        账面库存：{this.goodsInfo.quantity}
                      </div>
                      <div>
                        <i class='el-icon-s-data' style='color: #6dc76a'></i>
                        可用数量：{this.goodsInfo.stoqty}
                      </div>
                      <div>
                        <i
                          class='el-icon-circle-plus'
                          style='color: #6dc76a'
                        ></i>
                        采购待入：{this.goodsInfo.buyremqty}
                      </div>
                      <div>
                        <i class='el-icon-remove' style='color: #F56C6C'></i>
                        订单待用：{this.goodsInfo.busremqty}
                      </div>
                      <div>
                        <i class='el-icon-remove' style='color: #F56C6C'></i>
                        领料待出：{this.goodsInfo.requremqty}
                      </div>
                    </div>
                    <div
                      v-show={!this.goodsInfo}
                      class='noData'
                      style='font-size: 18px'
                    >
                      暂无内容
                    </div>

                    <span
                      slot='reference'
                      class='textunderline'
                      on-click={() => this.showData(row, rowIndex)}
                      style={
                        row.ivquantity < row.safestock
                          ? 'color:#f02423;font-weight:bold'
                          : ''
                      }
                    >
                      {row[item.itemcode]}
                    </span>
                  </el-popover>
                </div>
              )
              return html
            } else if (item.itemcode == 'operate') {
              html = (
                <div class='btn-box' style='height: 34px'>
                  <el-popover
                    placement='left'
                    trigger='click'
                    width='500'
                    on-show={() => this.getImgList(row.id)}
                  >
                    <Pictures
                      ref='SelCust'
                      idx={row.id}
                      imgList={this.imgList}
                    ></Pictures>
                    <el-button
                      slot='reference'
                      type='text'
                      icon='el-icon-picture'
                    ></el-button>
                  </el-popover>
                  <el-popover
                    placement='left'
                    trigger='click'
                    width='500'
                    on-show={() => this.getWordList(row.id)}
                  >
                    <div style='position: relative; min-height: 100px'>
                      <div v-show={!!this.postscriptContent}>
                        {this.postscriptContent}
                      </div>
                      <div v-show={!this.postscriptContent} class='noData'>
                        暂无附言
                      </div>
                    </div>

                    <el-button
                      slot='reference'
                      type='text'
                      icon='el-icon-chat-dot-round'
                      style='margin: 0 10px'
                    ></el-button>
                  </el-popover>
                  <el-popover
                    placement='left'
                    trigger='click'
                    width='500'
                    on-show={() => this.getFlieList(row.id)}
                  >
                    <FileList
                      ref='FileList'
                      idx={row.id}
                      filelist={this.filelist}
                    ></FileList>
                    <el-button
                      slot='reference'
                      type='text'
                      icon='el-icon-folder'
                    ></el-button>
                  </el-popover>
                </div>
              )
              return html
            } else {
              return row[item.itemcode]
            }
          }
        }
        if (!item.displaymark) {
          this.columnHidden.push(item.itemcode)
        }
        customData.push(obj)
      })
      customData.unshift({
        field: '',
        key: 'checkbox',
        type: 'checkbox',
        title: '',
        width: 50,
        align: 'center',
        fixed: 'left'
      })
      customData.unshift({
        field: 'index',
        key: 'index',
        title: 'ID',
        width: 50,
        align: 'center',
        fixed: 'left',
        operationColumn: true,
        renderBodyCell: ({ row, column, rowIndex }, h) => {
          return rowIndex + this.rowScroll + 1
        }
      })

      this.customData = customData
      this.keynum += 1
    },
    pagePrint() {
      this.$refs.PrintPageServer.printButton(1, 1)
    },
    btnPrint() {
      this.$refs.PrintServer.printButton(2, 1)
    },
    // ===================================公共方法===============
    // 选择单元格计算
    countCellData() {
      var rangeObj =
        this.$refs.tableList.getRangeCellSelection().selectionRangeKeys //
      var rangeObjIndex =
        this.$refs.tableList.getRangeCellSelection().selectionRangeIndexes //
      var fields = ['baseprice', 'rebate', 'price', 'pricemin', 'pricemax']
      this.$countCellData(this, fields, rangeObj, rangeObjIndex)
    },
    getSummary() {
      this.$getSummary(this, this.totalfields)
    },
    // 分页组件事件
    getList(data) {
      this.queryParams.PageNum = data.page
      this.queryParams.PageSize = data.limit
      this.bindData()
    },
    // 模糊查询
    search(val) {
      this.$search(this, val, 0, ['goodsname', 'goodsspec', 'goodsspec', 'partid'])
    },
    searchGroupuid(val) {
      if (val != '') {
        this.queryParams.scenedata = [
          {
            field: 'uidgroupguid',
            fieldtype: 0,
            math: 'equal',
            value: val
          }
        ]
      } else {
        this.$delete(this.queryParams, 'scenedata')
      }
      this.$delete(this.queryParams, 'OrderBy')
      this.queryParams.SearchType = 0 // 高级搜索
      this.queryParams.PageNum = 1
      this.bindData()
    },
    // 高级搜索
    advancedSearch(val) {
      // this.$advancedSearch(this, val);
      this.queryParams.scenedata = val
      if (val[0].field == '') {
        this.queryParams.SearchType = 1
      } else {
        this.queryParams.SearchType = 0
      }
      this.queryParams.PageNum = 1
      this.bindData()
    },
    // 排序
    changeSort(row) {
      for (var key in row) {
        if (row[key] != '') {
          var val = {
            prop: key
          }
          if (row[key] == 'desc') {
            this.queryParams.OrderType = 1
          } else {
            this.queryParams.OrderType = 0
          }
          this.queryParams.OrderBy = this.$tableSort(val, this.tableForm)
          this.bindData()
          break
        }
      }
    },
    // 打开编辑页面，传入被编辑的客户id
    showForm(val) {
      this.$emit('showForm', val)
    },
    // 导出
    btnExport() {
      this.$btnExport(this.lst, this.tableForm, this.exportitle)
    },
    // =========================自定义方法===========================================
    showData(row) {
      var queryParams = {
        PageNum: 1,
        PageSize: 10,
        OrderType: 1,
        SearchType: 1
      }
      queryParams.SearchPojo = {
        goodsuid: row.goodsuid
      }
      request
        .post(
          '/store/D04M04B1/getMachMatQtyPageListByGoods?key=' + row.id,
          JSON.stringify(queryParams)
        )
        .then((res) => {
          this.goodsInfo = res.data.data.list[0]
        })
    },
    groupsearch(res) {
      if (res != '') {
        this.queryParams.SearchPojo = { partgroupid: res }
      } else {
        this.$delete(this.queryParams, 'SearchPojo')
      }
      this.queryParams.SearchType = 1
      this.queryParams.PageNum = 1
      this.bindData()
    },
    // ====================================
    // 批量删除
    allDelete() {
      if (this.selectList.length == 0) {
        this.$message.warning('请先选择货品')
        return
      }
      this.$confirm('此操作将永久删除记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.deleteRows()
        })
        .catch(() => {})
    },
    // 移除行数据
    async deleteRows(index, rows) {
      var that = this
      var val = this.selectList
      if (val) {
        const promiseList = [] // 异步请求列表
        for (const item of val) {
          const promise = new Promise((resolve, reject) => {
            request
              .get(`/E08M13B1/delete?key=${item.id}`)
              .then((res) => {
                if (res.data.code == 200) {
                  if (res.data.data == 0) {
                    reject('删除失败,' + item.goodsname + '在系统中已使用')
                  } else {
                    resolve('删除成功')
                  }
                } else {
                  reject('删除失败')
                }
              })
              .catch((er) => {
                reject('删除失败')
              })
          })
          promiseList.push(promise) // 每个异步请求push到列表中
        }
        await Promise.all(promiseList)
          .then((res) => {
            // 等所有异步请求完成后执行
            this.$message.success('删除成功')
          })
          .catch((er) => {
            this.$message.warning(er)
          })
          .finally(() => {
            this.selectList = []
            this.checkboxOption.selectedRowKeys = []
            this.bindData()
          })
      }
    },
    // 附图
    getImgList(id) {
      var queryParams = {
        PageNum: 1,
        PageSize: 10,
        OrderType: 1,
        SearchType: 1,
        SearchPojo: { relateid: id }
      }
      request
        .post('/utils/D96M05B1/getPageList', JSON.stringify(queryParams))
        .then((res) => {
          if (res.data.code == 200) {
            this.imgList = []
            var list = res.data.data.list
            if (list.length != 0) {
              for (var i = 0; i < list.length; i++) {
                this.imgList.push(list[i].fileurl)
              }
            }
          }
        })
    },
    // 附言
    getWordList(id) {
      this.postscriptContent = ''
      var queryParams = {
        PageNum: 1,
        PageSize: 10,
        OrderType: 1,
        SearchType: 1,
        SearchPojo: { relateid: id }
      }
      request
        .post('/utils/D96M08B1/getPageList', JSON.stringify(queryParams))
        .then((res) => {
          if (res.data.code == 200) {
            if (res.data.data.list.length) {
              var list = res.data.data.list[0]
              this.postscriptContent = list.filecontent
            }
          }
        })
    },
    // 附件
    getFlieList(id) {
      this.filelist = []
      var queryParams = {
        PageNum: 1,
        PageSize: 10,
        OrderType: 1,
        SearchType: 1,
        SearchPojo: { relateid: id }
      }
      request
        .post('/utils/D96M03B1/getPageList', JSON.stringify(queryParams))
        .then((res) => {
          if (res.data.code == 200) {
            if (res.data.data.list.length) {
              var list = res.data.data.list[0]
              this.filelist = list.filecontent
            }
          }
        })
    }
  }
}
</script>
<style lang="scss" scoped>
@import "@/styles/mycustom.scss";
::v-deep .noData {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 30px;
  color: #c0c4cc;
}
</style>
