<template>
  <div style="width: 100%; box-sizing: border-box" class="component-container">
    <div
      style="top: 0px; width: 100%; z-index: 99"
      class="form_button flex a-c j-end p-r"
    >
      <div class="button-container p-a" style="top: 0px; right: 30px">
        <EditGroupBtns
          ref="EditGroupBtns"
          :showcontent="['save', 'print', 'operate', 'process']"
          :formdata="formdata"
          :operate-bar="operateBar"
          :process-bar="processBar"
          :formstate="formstate"
          :submitting="submitting"
          @submitForm="submitForm"
          @closeForm="closeForm"
          @printButton="$refs.PrintServer.printButton(0, 1)"
          @clickMethods="clickMethods"
        />
      </div>
    </div>
    <!-- =======================form表单部分=================================== -->
    <div
      style="
        padding: 20px;
        box-sizing: content-box;
        height: 100%;
        overflow: auto;
      "
    >
      <div
        class="form-border form-container shandow form_info flex f-d-c"
        style="width: 100%"
        :style="{ height: formcontainHeight }"
      >
        <FormTemp
          ref="formtemp"
          style="
            width: 100%;
            display: flex;
            flex-direction: column;
            height: 100%;
          "
          :formdata="formdata"
          :formtemplate="formtemplate"
          :selectform="selectform"
          @clickMethods="clickMethods"
        >
          <template v-if="!formtemplate.header.type" #Header>
            <div class="form form-head p-r">
              <EditHeader
                ref="formHeader"
                :title="
                  formtemplate.header.title ? formtemplate.header.title : title
                "
                :formdata="formdata"
                :storelist="storelist"
                :store-options="storeOptions"
                :groupname-options="groupnameOptions"
                :custom-data-list="customDataList"
                :goods-cust-list="goodsCustList"
                @handleChangeGroupid="handleChangeGroupid"
                @getpingyin="getpingyin"
                @setGroupRow="setGroupRow"
                @changeStore="changeStore"
                @setStoreList="storelist = $event"
                @selStoreList="selStoreList"
                @openImgInfo="ImgInfoVisible = true"
                @setCustList="setCustList"
                @openUnitjson="openUnitjson"
              />
            </div>
          </template>
        </FormTemp>
      </div>
    </div>
    <!-- ===========================组件============================ -->
    <PrintServer
      ref="PrintServer"
      :formdata="formdata"
      :printcode="'D91M01B1Edit'"
      :commonurl="'/e08/E08M13B1/printBill'"
      :weburl="'/e08/E08M13B1/printWebBill'"
    />
    <el-dialog
      v-if="SupplierVisible"
      title="供应商报价"
      :append-to-body="true"
      :visible.sync="SupplierVisible"
      width="60vw"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <Supplier ref="supplier" :idx="idx" :multi="1" />
    </el-dialog>
    <el-dialog
      v-if="imgUploadVisible"
      title="添加附图"
      :visible.sync="imgUploadVisible"
      width="500px"
      :append-to-body="true"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <ImgUpload
        ref="imgUpload"
        :idx="idx"
        @closeDialog="imgUploadVisible = false"
      />
      <div slot="footer">
        <el-button
          type="primary"
          size="small"
          @click="$refs.imgUpload.submitUploadFile()"
        >确定上传</el-button>
        <el-button
          size="small"
          @click="imgUploadVisible = false"
        >关 闭</el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-if="fileUploadVisible"
      title="添加附件"
      :visible.sync="fileUploadVisible"
      width="500px"
      :append-to-body="true"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <FileUpload
        ref="fileUpload"
        :idx="idx"
        :modulecode="formdata.goodsuid"
        :module="'D91M01B1'"
        @closeDialog="fileUploadVisible = false"
      />
      <div slot="footer">
        <el-button
          type="primary"
          size="small"
          @click="$refs.fileUpload.submitUploadFile()"
        >确定上传</el-button>
        <el-button
          size="small"
          @click="fileUploadVisible = false"
        >关 闭</el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-if="PostscriptVisible"
      title="添加附言"
      :visible.sync="PostscriptVisible"
      width="64vw"
      :append-to-body="true"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <PostUpload
        ref="postUpload"
        :idx="idx"
        :formdata="formdata"
        @closeDialog="PostscriptVisible = false"
      />
      <div slot="footer">
        <el-button
          type="primary"
          size="small"
          @click="$refs.postUpload.submitUploadFile()"
        >确 定</el-button>
        <el-button
          size="small"
          @click="PostscriptVisible = false"
        >关 闭</el-button>
      </div>
    </el-dialog>
    <!--附件插图  -->
    <el-dialog
      v-if="ImgInfoVisible"
      title="附件插图"
      :visible.sync="ImgInfoVisible"
      width="860px"
      top="5vh"
      :append-to-body="true"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <SelImgInfo ref="selImgInfo" :multi="1" :idx="idx" />
      <div slot="footer">
        <el-button
          type="primary"
          size="small"
          @click="submitselImg"
        >确 定</el-button>
        <el-button
          size="small"
          @click="ImgInfoVisible = false"
        >取 消</el-button>
      </div>
    </el-dialog>
    <!-- 历史货品 -->
    <el-dialog
      v-if="historyGoodsVisible"
      title="历史货品"
      :visible.sync="historyGoodsVisible"
      width="860px"
      top="5vh"
      :append-to-body="true"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <SelGoods ref="selGoods" :multi="0" />
      <div slot="footer">
        <el-button
          type="primary"
          size="small"
          @click="submitselGoods"
        >确 定</el-button>
        <el-button
          size="small"
          @click="historyGoodsVisible = false"
        >取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-if="unitjsonVissable"
      title="多单位设置"
      :visible.sync="unitjsonVissable"
      width="860px"
      top="5vh"
      :append-to-body="true"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <div
        v-for="(item, index) in unitJsonData"
        :key="index"
        class="unit-json-ul"
      >
        <div class="unit-json-name">辅助单位{{ index + 1 }}</div>
        <div class="unit-json-item">
          <el-input
            v-model="item.sqty"
            placeholder="请输入辅助数量"
            style="width: 200px; margin-right: 10px"
          />
          <el-input
            v-model="item.name"
            placeholder="请输入辅助单位名称"
            style="width: 200px"
          />
          <span style="margin: 0 10px">=</span>
          <el-input
            v-model="item.mqty"
            placeholder="请输入基本数量"
            style="width: 200px"
          />
          <span style="margin: 0 10px">{{ formdata.goodsunit }}</span>
          <el-button
            size="small"
            icon="el-icon-delete"
            @click="
              item.sqty = '';
              item.name = '';
              item.mqty = '';
            "
          >清空</el-button>
        </div>
      </div>
      <div slot="footer">
        <el-button
          type="primary"
          size="small"
          @click="submitUnitJson"
        >确 定</el-button>
        <el-button
          size="small"
          @click="unitjsonVissable = false"
        >取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import request from '@/utils/request'
import CRUD from '../CRUD.JS'
import saveparam from './formTemp/saveparam'
import EditHeader from './formTemp/editHeader.vue'
import { operateBar, processBar } from './formTemp/operate.js'
import { formTemp } from './formTemp'
import FormTemp from '@/components/FormTemp/index.vue'
import pinyin from 'js-pinyin' // 汉字转拼音
import Supplier from './supplierItem.vue' // 供应商报价
import ImgUpload from './fileUpload/D96M05B1/fileUpload.vue'
import FileUpload from './fileUpload/D96M03B1/fileUpload.vue'
import PostUpload from './fileUpload/D96M08B1/fileUpload.vue'
import SelImgInfo from './fileUpload/D96M05B1/select.vue'
import SelGoods from './select.vue'
export default {
  name: 'Formedit',
  components: {
    EditHeader,
    FormTemp,
    Supplier,
    FileUpload,
    ImgUpload,
    PostUpload,
    SelImgInfo,
    SelGoods
  },
  props: ['idx', 'goodsCustList'],
  data() {
    return {
      // 表单信息
      title: '货品信息',
      operateBar: operateBar,
      processBar: processBar,
      formdata: {
        ageprice: 0,
        alertsqty: 0,
        barcode: '',
        batchmg: 0,
        batchonly: 0,
        custjson: [],
        unitjson: [],
        createdate: new Date(),
        modifydate: new Date(),
        deletelister: '',
        enabledmark: 1, // 有效性默认为1
        packsnmark: 0,
        goodsname: '',
        goodspinyin: '',
        goodsspec: '',
        goodsstate: '',
        goodsuid: '',
        goodsunit: '',
        groupid: '',
        groupname: '',
        inprice: 0,
        ivquantity: 0,
        material: '',
        outprice: 0,
        partid: '',
        pid: '',
        puid: '',
        remark: '',
        safestock: 0,
        taxrate: 13,
        storeid: '',
        storelistguid: '', // 授权仓库id
        storelistname: '', // 授权仓库名称
        surface: '',
        skumark: 0,
        uidgroupcode: '',
        uidgroupguid: '',
        uidgroupname: '',
        uidgroupnum: 0,
        versionnum: '',
        brandname: '',
        virtualitem: 0,
        lister: JSON.parse(window.localStorage.getItem('getInfo')).realname, // 制表
        createby: JSON.parse(window.localStorage.getItem('getInfo')).realname // 创建者
      },
      // 非通用变量
      formstate: 0,
      submitting: 0,
      formtemplate: formTemp,
      selectform: [],
      // 其他
      SupplierVisible: false, // 供应商报价
      imgUploadVisible: false,
      fileUploadVisible: false,
      PostscriptVisible: false,
      historyGoodsVisible: false, // 历史货品
      unitjsonVissable: false,
      // 附加信息
      ishowAdd: false,
      ImgInfoVisible: false,
      // 自定义内容
      customDataList: [],
      storelist: [], // 同步多选仓库
      //
      goodsunitRefsVisible: false,
      surfaceRefsVisible: false,
      materialRefsVisible: false,
      brandnameRefsVisible: false,
      // 授权仓库
      storeOptions: [],
      // 分组名称
      groupnameOptions: [],
      //
      unitJsonData: [
        {
          use: 'aux1', // 辅助1
          name: '', // 单位名,
          mqty: '', // 基本数量
          sqty: '' // 辅助数量
        },
        {
          use: 'aux2', // # 辅助2
          name: '', // 单位名,
          mqty: '',
          sqty: ''
        },
        {
          use: 'aux3', // # 辅助23
          name: '', // 单位名,
          mqty: '',
          sqty: ''
        }
      ]
    }
  },
  computed: {
    formcontainHeight() {
      var formcontainHeight = window.innerHeight - 113
      if (formcontainHeight < 600) {
        formcontainHeight = 600
      }
      return formcontainHeight + 'px'
    }
  },
  watch: {
    idx: function(val, oldVal) {
      this.bindData()
    }
  },
  created() {
    this.bindDataByStoreList()
    this.bindDataByUidGroupName()
    this.getListByCode()
  },
  mounted() {
    this.bindTemp()
    this.bindData()
  },
  methods: {
    // =====================初始化=======================
    // 界面设置
    bindTemp() {
      this.formtemplate = formTemp
      // if(this.formtemplate.item.type){
      //   this.$refs.elitem.table
      // }
    },
    // 加载列表
    bindData(id = this.idx) {
      this.formstate = 0
      if (id) {
        request
          .get(`/E08M13B1/getEntity?key=${id}`)
          .then((res) => {
            if (res.data.code == 200) {
              this.formdata = res.data.data
              if (this.formdata.storelistname) {
                this.storelist = this.formdata.storelistguid.split(',') // 显示授权仓库
              }
              if (this.formdata.unitjson) {
                this.formdata.unitjson =
                  JSON.parse(res.data.data.unitjson) || []
              } else {
                this.formdata.unitjson = []
              }

              this.initCustList() // 初始化货品自定项
              // 判断单据状态
              this.formstate = this.formdata.id ? 1 : 0
              console.log(this.formdata.custjson, 'custjson')
            } else {
              this.$alert(
                res.data.code + ' 读取异常，请刷新列表后重试',
                '错误',
                {
                  confirmButtonText: '确定',
                  type: 'warning',
                  callback: (action) => {
                    this.closeForm()
                  }
                }
              )
            }
          })
          .catch((error) => {
            this.$message.error(error || '请求错误')
          })
      }
    },
    initCustList() {
      if (
        this.formdata.custjson &&
        typeof this.formdata.custjson === 'string'
      ) {
        var jsonData = JSON.parse(this.formdata.custjson)
        for (var j = 0; j < jsonData.length; j++) {
          this.$set(this.formdata, jsonData[j].key, jsonData[j].value)
        }
        this.formdata.custjson = jsonData || []
      } else {
        this.formdata.custjson = []
        for (var i = 0; i < this.goodsCustList.length; i++) {
          var Item = this.goodsCustList[i]
          if (Item.defvalue) {
            this.$set(this.formdata, Item.custkey, Item.defvalue) // 设置默认值
          }
        }
        this.setCustList() //
      }
    },
    //* *=============================公共项目================================ */
    submitForm() {
      var validateRule
      if (!this.formtemplate.header.type) {
        validateRule = this.$refs.formHeader
      } else {
        validateRule = this.$refs.formtemp.$refs.formHeader
      }
      validateRule.$refs['formdata'].validate((valid) => {
        if (valid) {
          this.saveForm()
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },

    saveForm() {
      this.submitting = 1
      var paramdata = {}
      paramdata = this.$getParam(saveparam, paramdata, this.formdata)
      if (!paramdata.custjson.length) {
        this.$set(paramdata, 'custjson', '')
      } else {
        this.$set(paramdata, 'custjson', JSON.stringify(paramdata.custjson))
      }
      if (this.idx == 0) {
        CRUD.add(paramdata)
          .then((res) => {
            if (res.code == 200) {
              this.$message.success('保存成功')
              this.$emit('changeIdx', res.data.id)
              this.$emit('bindData')
              if (this.$ismicroapp) {
                const { bindData, lstp } = window.$wujie.props
                if (bindData) {
                  if (lstp.orgidx) {
                    bindData(lstp.orgidx)
                  } else {
                    bindData()
                  }
                  console.log('v-window 弹窗环境 更新父列表')
                }
              }
              this.formdata = res.data
              this.submitting = 0
              this.formstate = this.formdata.id ? 1 : 0
              this.initCustList()
            }
          })
          .catch((er) => {
            this.$message.warning(er || '保存失败')
            this.submitting = 0
          })
      } else {
        CRUD.update(paramdata)
          .then((res) => {
            if (res.code == 200) {
              this.$message.success('保存成功')
              this.$emit('bindData')
              if (this.$ismicroapp) {
                const { bindData, lstp } = window.$wujie.props
                if (bindData) {
                  if (lstp.orgidx) {
                    bindData(lstp.orgidx)
                  } else {
                    bindData()
                  }
                  console.log('v-window 弹窗环境 更新父列表')
                }
              }
              this.formdata = res.data
              this.submitting = 0
              this.formstate = this.formdata.id ? 1 : 0
              this.initCustList()
            }
          })
          .catch((er) => {
            this.$message.warning(er || '保存失败')
            this.submitting = 0
          })
      }
    },
    // =====================工具项===================
    clickMethods(val) {
      // param 为简单参数，例如 1、0、true、false
      this[val.meth](val.param)
    },

    // 关闭formedit对话框
    closeForm() {
      if (this.$ismicroapp) {
        const { close } = window.$wujie.props
        // 状态为弹窗的场景下执行关闭弹窗
        if (close) {
          close()
          console.log('v-window 弹窗环境 关闭弹窗')
          return
        }
      }
      this.$emit('closeForm')
    },
    deleteForm(idx) {
      this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          CRUD.delete(this)
        })
        .catch(() => {})
    },
    // ===============自定义项=====================
    submitselImg() {
      const imgRow = this.$refs.selImgInfo.selrows
      this.formdata.goodsphoto1 = imgRow.fileurl
      this.ImgInfoVisible = false
    },
    openImgUpload() {
      this.imgUploadVisible = true
    },
    openFileUpload() {
      this.fileUploadVisible = true
    },
    openPostscript() {
      this.PostscriptVisible = true
    },
    // 供应商报价
    selectSupplier() {
      this.SupplierVisible = true
    },
    // 引入历史货品
    openHistoryGoods() {
      this.historyGoodsVisible = true
      this.$nextTick(() => {
        this.$refs.selGoods.bindData()
      })
    },
    submitselGoods() {
      this.formdata = this.$refs.selGoods.selrows
      this.formdata.uidgroupguid = ''
      this.formdata.uidgroupname = ''
      this.formdata.uidgroupcode = ''
      this.formdata.uidgroupnum = ''
      this.formdata.goodsuid = ''
      this.historyGoodsVisible = false
    },
    // 获取到分组名称
    bindDataByUidGroupName() {
      const queryParams = {
        PageNum: 1,
        PageSize: 10000,
        OrderType: 0,
        SearchType: 1,
        OrderBy: 'rownum'
      }
      request
        .post('/e08/E08M13S1/getPageList', JSON.stringify(queryParams))
        .then((res) => {
          if (res.data.code == 200) {
            this.groupnameOptions = this.changeFormat(res.data.data.list)
          }
        })
        .catch((error) => {})
    },
    // 分组名称 选中
    handleChangeGroupid(val) {
      var uid = val[val.length - 1]
      this.formdata.uidgroupguid = val[val.length - 1]
      this.changeByGroup(uid)
    },
    // 选择的分组发生变化
    changeByGroup(val) {
      request.get('/e08/E08M13B1/getNewUidByGroup?key=' + val).then((res) => {
        this.formdata.goodsuid = res.data.data.goodsuid
        this.formdata.uidgroupcode = res.data.data.uidgroupcode
        this.formdata.uidgroupnum = res.data.data.uidgroupnum
        this.formdata.uidgroupname = res.data.data.uidgroupname
        this.formdata.batchmg = res.data.data.batchmg
        this.formdata.batchonly = res.data.data.batchonly
        this.formdata.packsnmark = res.data.data.packsnmark
        this.formdata.skumark = res.data.data.skumark
      })
    },

    getListByCode() {
      request.get('/system/SYSM07B6/getListByCode?key=d91m01').then((res) => {
        if (res.data.code == 200) {
          if (res.data.data.length == 0) {
            this.customDataList = []
          } else {
            this.customDataList = res.data.data
          }
        } else {
          this.$message.warning(res.data.msg || '查询失败')
        }
      })
    },
    // 得到货品的拼音
    getpingyin(val) {
      pinyin.setOptions({ checkPolyphone: false, charCase: 1 })
      this.formdata.goodspinyin = pinyin.getFullChars(val)
    },
    // 获取到仓库列表用于复选
    bindDataByStoreList() {
      const queryParams = {
        PageNum: 1,
        PageSize: 500,
        OrderType: 1,
        SearchType: 1
      }
      request
        .post('/store/D04M21S1/getPageList', JSON.stringify(queryParams))
        .then((res) => {
          if (res.data.code == 200) {
            this.storeOptions = res.data.data.list
          }
        })
        .catch((error) => {})
    },
    changeStore(val) {
      this.storeOptions.forEach((item) => {
        if (item.id == val) {
          this.formdata.storename = item.storename
          this.formdata.storeid = item.id
          // 判断授权仓库中是否含有默认仓库选中的值
          var index = this.storelist.findIndex((a) => a == val)
          if (index == -1) {
            this.storelist.push(val)
            this.selStoreList(this.storelist)
          }
        }
      })
    },
    // 选择授权仓库
    selStoreList(val) {
      // 判断授权仓库中是否含有默认仓库的id
      var index = val.findIndex((a) => a == this.formdata.storeid)
      if (index == -1) {
        this.$message.warning('授权仓库须含有默认仓库')
        this.storelist = this.storelist.filter((item) => {
          return item !== this.formdata.storeid
        })
        this.storelist.splice(index, 0, this.formdata.storeid)
        return
      }
      let str = ''
      let strname = ''
      // 转换为字符串存入formdata
      val.forEach((v, index, arr) => {
        if (index == arr.length - 1) {
          str = str + v
        } else {
          str = str + v + ','
        }
      })
      for (var i = 0; i < this.storeOptions.length; i++) {
        for (var j = 0; j < val.length; j++) {
          if (val[j] == this.storeOptions[i].id) {
            strname += this.storeOptions[i].storename + ','
          }
        }
      }
      var reg = /,$/gi
      strname = strname.replace(reg, '') // 去除末尾逗号
      this.formdata.storelistguid = str // 仓库的名字 xxx，xxx，xxx
      this.formdata.storelistname = strname
    },
    setGroupRow(val) {
      this.formdata.groupname = val.groupname
      this.formdata.groupid = val.id
      this.selVisible = false // 关闭下拉框
    },
    changeIdx(val) {
      this.idx = val
    },
    changeFormat(data) {
      const result = []
      if (!Array.isArray(data)) {
        return result
      }
      data.forEach((item) => {
        delete item.children
      })
      const map = {}
      data.forEach((item) => {
        map[item.id] = item
      })
      data.forEach((item) => {
        const parent = map[item.parentid]
        if (parent) {
          (parent.children || (parent.children = [])).push(item)
        } else {
          result.push(item)
        }
      })
      return result
    },

    setCustList() {
      var ArrData = []
      for (var i = 0; i < this.goodsCustList.length; i++) {
        var Item = this.goodsCustList[i]
        var obj = {
          key: Item.custkey,
          value: this.formdata[Item.custkey]
            ? String(this.formdata[Item.custkey]).replace(
              /["'{}<>:\[\]\s]/g,
              ''
            )
            : ''
        }
        if (obj.value) {
          ArrData.push(obj)
        }
      }

      this.$set(this.formdata, 'custjson', ArrData.length ? ArrData : [])
    },
    openUnitjson() {
      if (this.formdata.unitjson.length) {
        this.unitJsonData = [].concat(this.formdata.unitjson)
      }
      this.unitjsonVissable = true
    },
    submitUnitJson() {
      this.formdata.unitjson = this.unitJsonData
      this.unitjsonVissable = false
    }
  }
}
</script>
<style scoped lang="scss">
//灰色背景
$bg-gray: #e2e2e2;
$bg-component: #f5f5f5;

::v-deep .el-scrollbar__wrap {
  overflow-x: hidden;
}

::v-deep .is-horizontal {
  display: none;
}

::v-deep.custInfo .el-row {
  margin-bottom: -20px;
  display: flex;
  flex-wrap: wrap;
}

/**修改错误提示 */
::v-deep .el-form-item__error {
  top: 1px;
  background: #fff;
  height: 25px;
  margin-top: 5px;
  margin-left: 5px;
  margin-bottom: 5px;
  width: 120px;
  display: flex;
  align-items: center;
}

/**美化用  外框阴影*/
.shandow {
  box-shadow: 0px 0px 10px $bg-gray;
}
.form_button {
  .button-container {
    background: $bg-gray;
    padding: 10px;
  }
}
//表格边
.form-border {
  border: 2px solid #dbdbdb;
}

.component-container {
  background: $bg-component;
  height: calc(100vh - 84px);

  .form-container {
    background: #fff;
  }

  .form {
    margin-right: 20px;
    margin-left: 20px;
    width: calc(100% - 40px);
  }

  .form-head {
    //工具栏上下padding 加按键高度 -容器的上padding
    margin-top: 32px;
  }
}
.unit-json-ul {
  margin-bottom: 10px;
  .unit-json-name {
    font-size: 14px;
    color: #606266;
    line-height: 40px;
    padding: 0 12px 0 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    font-weight: bold;
  }
}
</style>
