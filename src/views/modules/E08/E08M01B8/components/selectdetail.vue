<template>
  <div>
    <div class="filter-container" style="margin-bottom: 5px">
      <el-input
        v-model="strfilter"
        placeholder="请输入查询"
        style="width: 200px"
        class="filter-item"
        size="small"
        @keyup.enter.native="search(strfilter)"
      />
      <el-button
        class="filter-item"
        type="primary"
        size="small"
        @click="search(strfilter)"
      >
        查询
      </el-button>
      <div style="float: right">
        <el-radio-group v-model="goodsType" size="small" @change="getStatus()">
          <el-radio-button label="半成品" />
          <el-radio-button label="成品" />
        </el-radio-group>
      </div>
    </div>
    <div style="margin-bottom: 10px">
      <el-table
        ref="selectgoods"
        v-loading="listLoading"
        :data="lst"
        element-loading-text="Loading"
        height="380px"
        border
        fit
        highlight-current-row
        style="overflow: auto"
        :header-cell-style="{
          background: '#F3F4F7',
          color: '#555',
          padding: '3px 0px 3px 0px',
        }"
        :cell-style="{ padding: '4px 0px 4px 0px' }"
        :row-class-name="rowIndex"
        @row-click="rowClick"
      >
        <!-- ---复选列--- -->
        <el-table-column v-if="multi == 1" type="selection" width="40" />
        <!-- ---单选列--- -->
        <el-table-column v-else label="" width="40" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-radio
              v-model="radio"
              :label="scope.$index"
              @change.native="getCurrentRow(scope.row)"
            >
              {{ " " }}</el-radio>
          </template>
        </el-table-column>
        <el-table-column
          label="货品编码"
          align="center"
          min-width="80"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span>{{ scope.row.goodsuid }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="货品名称"
          align="center"
          min-width="80"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span>{{ scope.row.goodsname }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="货品规格"
          align="center"
          min-width="80"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span>{{ scope.row.goodsspec }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="货品状态"
          align="center"
          min-width="50"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span>{{ scope.row.goodsstate }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="单位"
          align="center"
          min-width="50"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span>{{ scope.row.goodsunit }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="外部编码"
          align="center"
          min-width="80"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span>{{ scope.row.partid }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="当前库存"
          align="center"
          min-width="50"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span>{{ scope.row.ivquantity }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.PageNum"
      :limit.sync="queryParams.PageSize"
      @pagination="GetList"
    />
  </div>
</template>

<script>
import request from '@/utils/request'
import Pagination from '@/components/Pagination/index.vue'
export default {
  components: {
    Pagination
  },
  props: ['multi', 'groupid', 'goodsstate'],
  data() {
    return {
      title: '货品信息',
      listLoading: true,
      lst: [], //  列表数据
      strfilter: '', // 搜索框内输入的值
      total: 0,
      radio: '', // 单选用参数默认为0即可
      selrows: '', // 选择的内容 单选变量
      queryParams: {
        PageNum: 1,
        PageSize: 10,
        OrderType: 1,
        SearchType: 1
      },
      goodsType: '成品',
      goodsVal: 'p'
    }
  },
  created() {
    this.bindData()
  },
  methods: {
    //  单选列方法 07/26修改
    getCurrentRow(row) {
      this.selrows = row
      this.$emit('singleSel', row) // 07/26修改
      this.$forceUpdate()
    },
    // 分页组件事件
    GetList(data) {
      this.queryParams.PageNum = data.page
      this.queryParams.PageSize = data.limit
      this.bindData()
    },
    // 加载列表
    bindData() {
      // 查询 goodsstate Bom-成品
      this.listLoading = true
      if (this.groupid) {
        var selObj = { groupid: this.groupid }
        if (this.queryParams.SearchPojo) {
          this.queryParams.SearchPojo = Object.assign(
            this.queryParams.SearchPojo,
            selObj
          )
        } else {
          this.queryParams.SearchPojo = selObj
        }
      }
      if (!this.queryParams.scenedata) {
        this.queryParams.scenedata = [
          {
            field: 'Mat_Goods.enabledmark',
            fieldtype: 0,
            math: 'like',
            value: 1
          }
        ]
      } else {
        var index = this.queryParams.scenedata.findIndex(
          (a) => a.field == 'Mat_Goods.enabledmark'
        )
        if (index == -1) {
          this.queryParams.scenedata.push({
            field: 'Mat_Goods.enabledmark',
            fieldtype: 0,
            math: 'like',
            value: 1
          })
        }
      }
      request
        .post(
          '/e08/E08M13B1/getNoBomPageList?state=' + this.goodsVal,
          JSON.stringify(this.queryParams)
        )
        .then((res) => {
          if (res.data.code == 200) {
            this.lst = res.data.data.list
            this.total = res.data.data.total
          }
          this.listLoading = false
        })
        .catch((error) => {
          this.listLoading = false
        })
    },
    // 查询
    search(res) {
      if (res != '') {
        this.queryParams.SearchPojo = {
          goodsname: res,
          goodsuid: res,
          goodsspec: res
        }
      } else {
        this.$delete(this.queryParams, 'SearchPojo')
      }
      this.queryParams.SearchType = 1
      this.queryParams.PageNum = 1
      this.bindData()
    },
    getStatus() {
      this.strfilter = ''
      this.$delete(this.queryParams, 'SearchPojo')
      if (this.goodsType == '成品') {
        this.goodsVal = 'p'
      } else if (this.goodsType == '半成品') {
        this.goodsVal = 's'
      }
      this.bindData()
    },
    rowIndex({ row, rowIndex }) {
      row.row_index = rowIndex
    },
    rowClick(row) {
      this.radio = row.row_index
      this.getCurrentRow(row)
    }
  }
}
</script>
<style lang="scss" scoped>
.current-row {
  background: blue;
}
</style>
