<template>
  <div>
    <el-button type="primary" @click="$refs.upload.click()">选择附图</el-button>
    <div class="fileUpload">
      <div class="fileUploadShow">
        <img
        @click="$refs.upload.click()"
          :src="
            uploadImg ? uploadImg : require('@/assets/404_images/noFace.jpg')
          "
          alt=""
        />
      </div>
      <div class="fileUploadInfo" v-if="uploadImg">
        <!-- <p class="ellipsis">名称：{{ uploadFileName }}</p>
        <p>大小：{{ uploadFileSize + "KB" }}</p>
        <p>类型：{{ uploadFileType }}</p> -->
         <p>名称：{{ uploadImgName }}</p>
            <p>大小：{{ uploadImgSize }}</p>
            <p>类型：{{ uploadImgType }}</p>
      </div>
    </div>
    <div class="checkFile">
      <span
        @click="
          bindData;
          showFileList = !showFileList;
        "
        >查看已上传附图
        <i
          :class="showFileList ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"
        ></i>
      </span>
    </div>
    <div
      class="fileList"
      :class="showFileList ? 'showFileList' : 'hideFileList'"
    >
      <div v-if="lst.length != 0">
        <div
          class="fileUpload fileList-item"
          v-for="(i, index) in lst"
          :key="index"
        >
          <div class="fileUploadShow">
            <img v-if="i.fileurl" :src="i.fileurl" alt="" />
            <img v-else src="../../../../../../../assets/404_images/noFace.jpg" alt="" />
          </div>
          <div class="fileUploadInfo" v-if="i.id">
            <p>名称：{{  i.fileoriname }}</p>
            <p>大小：{{ getFileSize(i.filesize)}}</p>
            <p>类型：{{ i.filesuffix }}</p>
          </div>
          <div class="closeBtn">
            <i @click="delItem(i)" class="el-icon-close"></i>
          </div>
        </div>
      </div>
      <div v-else v-show="showFileList" class="noData">暂无文件内容</div>
    </div>

    <div v-show="false">
      <input ref="upload" type="file" @change="getFile" />
    </div>
  </div>
</template>
<script>
import request from "@/utils/request";
import lrz from "lrz";
export default {
  props: ["idx"],
  data() {
    return {
      fileTemp: {},
      uploadImg: "",
      uploadImgName: "",
      uploadImgSize: 0,
      uploadImgType: "",
      lst: [],
      showFileList: true,
      queryParams: {
        PageNum: 1,
        PageSize: 10,
        OrderType: 1,
        SearchType: 1,
      },
    };
  },
  created() {
    this.bindData();
  },
  methods: {
    bindData() {
      this.queryParams.SearchPojo = { relateid: this.idx };
      request
        .post("/utils/D96M05B1/getPageList", JSON.stringify(this.queryParams))
        .then((res) => {
          if (res.data.code == 200) {
            this.lst = res.data.data.list;
          }
        });
    },
    getFile() {
      const inputDOM = this.$refs.upload;
      const file = inputDOM.files[0];
      console.log(file);
     lrz(file).then((rst) => {
        console.log(rst);
        this.uploadImg = rst.base64;
        this.uploadImgName = rst.origin.name;
        this.uploadImgSize = Number(rst.fileLen / 1024).toFixed(2) + "KB";
        this.uploadImgType = rst.file.type;
        // this.uploadBase64(rst.formData);
        this.fileTemp = rst;
      });
    },
    submitUploadFile() {
      // var formData = new FormData();
      // formData.append("file", this.fileTemp);
      request
        .post("/utils/D96M05B1/upload?relateid=" + this.idx, this.fileTemp.formData)
        .then((res) => {
          if (res.data.code == 200) {
            this.$message.success("图片上传成功");
            // this.$emit('bindData');
             this.fileTemp = {};
             this.uploadImg = "";
            this.uploadImgName = "";
            this.uploadImgSize = 0;
            this.uploadImgType = "";
            this.bindData();
            // this.$emit("closeDialog");
          } else {
            this.$message.warning(res.data.msg || "图片上传失败");
          }
        })
        .catch((er) => {
          this.$message.error("请求错误");
        });
    },
    // 删除
    delItem(row) {
      this.$confirm("此操作将永久删除该附件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          request
            .get("/utils/D96M05B1/delete?key=" + row.id)
            .then((res) => {
              if (res.data.code == 200) {
                this.$message.success("删除成功");
                this.bindData();
              } else {
                this.$message.warning(res.data.msg || "删除失败");
              }
            })
            .catch((er) => {
              this.$message.error("请求错误");
            });
        })
        .catch(() => {});
    },
    // 获取文件大小
    getFileSize(data) {
      var size = Number(data / 1024).toFixed(2) + "KB";
      return size;
    },
  },
};
</script>
<style lang="scss" scoped>
.fileUpload {
  display: flex;
  align-items: center;
  //   margin-top: 10px;
  padding: 5px;
  position: relative;
  .fileUploadShow {
    width: 85px;
    height: 90px;
    text-align: center;
     border: 1px solid #969696;
    padding: 5px;
    img {
      width: auto;
      height: 100%;
      max-width: 100%;
      min-width: 50px;
      // max-height: 100%;
    }
  }
  .fileUploadInfo {
    margin-left: 10px;
    line-height: 20px;
    width: calc(100% - 115px);
  }
}
.fileList {
  cursor: pointer;
  position: relative;
  height: 0;
  overflow: auto;

  .fileList-item {
    margin: 8px 0;
    border-radius: 5px;
    .closeBtn {
      position: absolute;
      right: 10px;
      top: 10px;
      i {
        font-size: 20px;
        font-weight: bold;
        color: rgb(235, 14, 14);
      }
      i:hover {
        background: rgb(248, 238, 238);
      }
    }
    .downFileBtn {
      color: #409eff;
    }
  }
  .fileList-item:hover {
    background: #c6e2ff;
  }
}
.showFileList {
  animation: showHeight 0.5s ease;
  -webkit-animation: showHeight 0.5s ease;
  animation-fill-mode: forwards;
  height: 0px;
}
@keyframes showHeight {
  from {
    height: 0px;
  }
  to {
    height: 220px;
  }
}

@-webkit-keyframes showHeight {
  from {
    height: 0px;
  }
  to {
    height: 220px;
  }
}
.hideFileList {
  animation: hideHeight 0.5s ease;
  -webkit-animation: hideHeight 0.5s ease;
  animation-fill-mode: forwards;
  height: 220px;
}
@keyframes hideHeight {
  from {
    height: 220px;
  }
  to {
    height: 0px;
  }
}

@-webkit-keyframes hideHeight {
  from {
    height: 220px;
  }
  to {
    height: 0px;
  }
}
.checkFile {
  margin: 8px auto;
  text-align: center;
  cursor: pointer;
  span {
    color: #409eff;
  }
}
.noData {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 22px;
  color: #c0c4cc;
}
.ellipsis {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-wrap: break-word;
}
</style>
