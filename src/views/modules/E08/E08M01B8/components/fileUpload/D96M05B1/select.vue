<template>
  <div class="">
    <div class="filter-container" style="margin-bottom: 5px;">
      <div>
        <el-input
          v-model="strfilter"
          placeholder="请输入查询"
          prefix-icon="el-icon-search"
          style="width: 200px"
          class="filter-item"
          size="small"
        />
        <el-button
          class="filter-item"
          type="primary"
          size="small"
          @click="search(strfilter)"
        >
          查询
        </el-button>
      </div>

      <div class="uploadImg">
        <el-button
          type="primary"
          size="small
          "
          @click="openImgUpload()"
          >上传</el-button
        >
      </div>
    </div>
    <div class="material-content">
      <div
        style="
          display: flex;
          flex-wrap: wrap;
          flex: 1;
          align-content: flex-start;
        "
        v-if="lst.length != 0"
      >
        <div
          class="img-item"
          v-for="(i, index) in lst"
          :key="index"
          @click="getCurrentRow(i, index)"
        >
          <div
            class="imgcountent"
            :style="selIndex == index ? ' border: 2px solid #409eff; ' : ''"
          >
            <img v-if="i.fileurl" :src="i.fileurl" alt="" />
            <img v-else src="../../../../../../../assets/404_images/noFace.jpg" alt="" />
          </div>
          <div class="imgTitle ellipsis">
            <span :style="selIndex == index ? 'color:#409eff' : ''"
              >{{ i.fileoriname }}
            </span>
          </div>
        </div>
      </div>
      <div v-else class="noData">暂无图片内容</div>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.PageNum"
      :limit.sync="queryParams.PageSize"
      @pagination="GetList"
    />
     <div v-show="false">
      <input ref="upload" type="file" @change="getFile" />
    </div>
    <el-dialog
      title="图片上传"
      width="500px"
      v-if="ImgUploadVisible"
      :visible.sync="ImgUploadVisible"
      :append-to-body="true"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <div>
        <el-button type="primary" @click="$refs.upload.click()"
          >选择文件</el-button
        >
        <div class="imgUpload">
          <div class="imgUploadShow">
            <img
              :src="
                uploadImg
                  ? uploadImg
                  : require('@/assets/404_images/noFace.jpg')
              "
              alt=""
            />
          </div>
          <div class="imgUploadInfo" v-if="uploadImg">
            <p>名称：{{ uploadImgName }}</p>
            <p>大小：{{ uploadImgSize }}</p>
            <p>类型：{{ uploadImgType }}</p>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer" style="margin-top: -20px">
        <el-button type="primary" size="small" @click="submitImgUpload()"
          >确 定</el-button
        >
        <el-button size="small" @click="ImgUploadVisible = false"
          >取 消</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import request from "@/utils/request";
import Pagination from "@/components/Pagination/index.vue";
import lrz from "lrz";
export default {
  components: {
    Pagination,
  },
  props: ["multi","idx"],
  data() {
    return {
      title: "附件插图",
      lst: [], //  列表数据
      strfilter: "", // 搜索框内输入的值
      selrows: "", // 选择的内容 单选变量
      total: 0,
      selIndex: -1,
      queryParams: {
        PageNum: 1,
        PageSize: 10,
        OrderType: 1,
        SearchType: 1,
      },
       // 上传图片
      ImgUploadVisible: false,
      uploadImg: "",
      uploadImgName: "",
      uploadImgSize: 0,
      uploadImgType: "",
      fileTemp: {},
    };
  },
  watch: {},
  created() {
    this.bindData();
  },
  methods: {
    //  单选列方法
    getCurrentRow(row, index) {
      this.selIndex = index;
      this.selrows = row;
      this.$forceUpdate();
      // if(this.multi){
      //     return
      // }
      this.$emit("singleSel", row);
    },
    // 分页组件事件
    GetList(data) {
      this.queryParams.PageNum = data.page;
      this.queryParams.PageSize = data.limit;
      this.bindData();
    },
    // 加载列表
    bindData() {
       var selObj={relateid:this.idx};
      if(!!this.queryParams.SearchPojo){
        this.queryParams.SearchPojo=Object.assign(this.queryParams.SearchPojo,selObj);
      }else{
        this.queryParams.SearchPojo=selObj;
      }
      request
        .post("/utils/D96M05B1/getPageList", JSON.stringify(this.queryParams))
        .then((response) => {
          if (response.data.code == 200) {
            this.lst = response.data.data.list;
            this.total = response.data.data.total;
          }
        })
        .catch((error) => {
          this.$message.error("请求错误");
        });
    },
    // 查询
    search(res) {
      if (res != "") {
        this.queryParams.SearchPojo = { fileoriname: res };
      } else {
        this.$delete(this.queryParams, "SearchPojo");
      }
      this.queryParams.SearchType = 1;
      this.queryParams.PageNum = 1;
      this.bindData();
    },
    //上传图片
     openImgUpload() {
      this.ImgUploadVisible = true;
       this.uploadImg ='';
        this.uploadImgName = '';
        this.uploadImgSize = 0 + "KB";
        this.uploadImgType = '';
    },
      getFile() {
      const inputDOM = this.$refs.upload;
      const file = inputDOM.files[0];
      lrz(file).then((rst) => {
        console.log(rst);
        this.uploadImg = rst.base64;
        this.uploadImgName = rst.origin.name;
        this.uploadImgSize = Number(rst.fileLen / 1024).toFixed(2) + "KB";
        this.uploadImgType = rst.file.type;
        this.fileTemp=rst
      });
    },
    submitImgUpload() {
      this.uploadBase64(this.fileTemp.formData);
    },
    uploadBase64(file) {
      request
        .post("/utils/D96M05B1/upload?relateid="+this.idx, file, {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        })
        .then((res) => {
          if (res.data.code == 200) {
            console.log(res);
            this.uploadImg = '';
            this.$refs.upload.value = "";
            this.fileTemp={};
             this.ImgUploadVisible=false;
            this.bindData();

          } else {
            this.$message.warning(res.data.msg || "上传图片失败");
          }
        });
    },
  },

};
</script>
<style lang="scss" scoped>
.filter-container{
    display: flex;
    align-items: center;
    justify-content: space-between;
    // border-bottom: 1px solid #c0c4cc;
    // .uploadImg{
    //     margin-right: 40px;
    // }
}

.material-content {
  background: #fff;
  display: flex;
  flex-wrap: wrap;
  padding: 20px;
  overflow: auto;
  position: relative;
  height: calc(100% - 70px);
  max-height: 620px;
   min-height: 200px;
  .img-item {
    //  flex: 1;
    width: 190px;
    height: 190px;
    .imgcountent {
      width: 120px;
      height: 120px;
      margin: 0 auto;
      border: 1px solid #969696;
      padding: 8px;
      box-sizing: border-box;
      text-align: center;
      position: relative;
      img {
        // width: 100%;
        width: auto;
        height: 100%;
        max-width: 100%;
        min-width: 100px;
      }
    }
    .imgTitle {
      width: 120px;
      margin: 15px auto;
      font-size: 14px;
      color: #69696d;
      text-align: center;
      position: relative;
      i {
        position: absolute;
        right: 0px;
        top: 2px;
        color: #409eff;
      }
    }
  }
}
.imgUpload {
    display: flex;
    .imgUploadShow {
      width: 180px;
      height: 180px;
      text-align: center;
      border: 1px solid #969696;
      margin-top: 10px;
      padding: 5px;
      img {
        width: auto;
        height: 100%;
        max-width: 100%;
        min-width: 160px;
        // max-height: 100%;
      }
    }
    .imgUploadInfo {
      margin-left: 10px;
    }
  }
.noData {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 30px;
  color: #c0c4cc;
}
.noscrollbar::-webkit-scrollbar {
  width: 0px;
  height: 1px;
}
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
</style>
