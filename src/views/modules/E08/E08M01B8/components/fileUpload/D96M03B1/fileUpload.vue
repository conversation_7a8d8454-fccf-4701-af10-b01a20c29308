<template>
  <div>
    <el-button type="primary" @click="$refs.upload.click()">选择附件</el-button>
    <div class="fileUpload">
      <div class="fileUploadShow">
        <img
          :src="
            uploadFile ? uploadFile : require('@/assets/knows_images/other.png')
          "
          alt=""
        />
      </div>
      <div class="fileUploadInfo" v-if="uploadFile">
        <p class="ellipsis">名称：{{ uploadFileName }}</p>
        <p>大小：{{ uploadFileSize + "KB" }}</p>
        <p>类型：{{ uploadFileType }}</p>
        <!-- <p>公共： <el-checkbox
                      v-model="isPublicMark"
                      label=""
                      :true-label="1"
                      :false-label="0"
                    /></p> -->
      </div>
    </div>
    <div class="checkFile">
      <span
        @click="
          bindData;
          showFileList = !showFileList;
        "
        >查看已上传附件
        <i
          :class="showFileList ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"
        ></i>
      </span>
    </div>
    <div
      class="fileList"
      :class="showFileList ? 'showFileList' : 'hideFileList'"
    >
      <div v-if="lst.length != 0">
        <div
          class="fileUpload fileList-item"
          v-for="(i, index) in lst"
          :key="index"
        >
          <div class="fileUploadShow">
            <img
              :src="
                i.id
                  ? require('@/assets/knows_images/' +
                      getFileType(i.filesuffix) +
                      '.png')
                  : require('@/assets/knows_images/other.png')
              "
              alt=""
            />
          </div>
          <div class="fileUploadInfo" v-if="i.id">
            <p class="ellipsis">名称：{{ i.fileoriname }}</p>
            <p>大小：{{ i.filesize ? getFileSize(i.filesize) : 0 + "KB" }}</p>
            <p>类型：{{ getFileType(i.filesuffix) }}</p>
            <p>
              公共：
              <el-checkbox
                v-model="i.publicmark"
                @change="changePublickMark(i)"
                label=""
                :true-label="1"
                :false-label="0"
              />
            </p>
            <!-- <a
              class="downFileBtn"
              :href="i.fileurl"
              :download="i.fileoriname"
              target="_blank"
              >下载文件</a
            > -->
            <el-button
             
              type="text"
              icon="el-icon-download"
              style="font-weight: 400;"
              @click="checkPower(i,'download')"
              >下载文件</el-button
            >
            <el-button
              v-if="
                ['png', 'jpg', 'jpeg', 'bmp', 'gif', 'webp'].includes(
                  i.filesuffix.toLowerCase()
                )
              "
              type="text"
              icon="el-icon-picture-outline"
              style="font-weight: 400;"
              @click="checkPower(i,'show')"
              >预览</el-button
            >
          </div>
          <div class="closeBtn">
            <i @click="delItem(i)" class="el-icon-close"></i>
          </div>
        </div>
      </div>
      <div v-else v-show="showFileList" class="noData">暂无文件内容</div>
    </div>

    <div v-show="false">
      <input ref="upload" type="file" @change="getFile" />
    </div>
    <el-image-viewer
      ref="ImageViewerRef"
      v-if="imageViewVisible"
      :visible.sync="imageViewVisible"
      append-to-body
      :on-close="closeViwer"
      :url-list="[ImageUrlList]"
    />
  </div>
</template>
<script>
import request from "@/utils/request";
import ElImageViewer from "element-ui/packages/image/src/image-viewer";
export default {
  props: ["idx", "modulecode", "module"],
  components: {
    ElImageViewer,
  },
  data() {
    return {
      file: {},
      uploadFile: "",
      uploadFileName: "",
      uploadFileSize: 0,
      uploadFileType: "",
      lst: [],
      showFileList: true,
      queryParams: {
        PageNum: 1,
        PageSize: 10,
        OrderType: 1,
        SearchType: 1,
      },
      isPublicMark: 0,
      ImageUrlList: "",
      imageViewVisible: "",
    };
  },
  created() {
    this.bindData();
  },
  methods: {
    bindData() {
      this.lst=[];
      // this.queryParams.scenedata = [
      //   {
      //     field: "Uts_Attachment.relateid",
      //     fieldtype: 0,
      //     math: "equal",
      //     value: this.idx,
      //   },
      // ];
      // if (this.modulecode) {
      //   this.queryParams.scenedata.push({
      //     field: "Uts_Attachment.modulecode",
      //     fieldtype: 0,
      //     math: "equal",
      //     value: this.modulecode,
      //   });
      // }
      // if (this.module) {
      //   this.queryParams.scenedata.push({
      //     field: "Uts_Attachment.module",
      //     fieldtype: 0,
      //     math: "equal",
      //     value: this.module,
      //   });
      // }
      // const baseUrl = `/utils/D96M03B1/getPageList`; JSON.stringify(this.queryParams)
    
      const query = [
        this.modulecode && `modulecode=${this.modulecode}`,
        this.module && `module=${this.module}`,
      ]
        .filter(Boolean)
        .join("&");
      const baseUrl = `/utils/D96M03B1/getList${query ? `?${query}` : ""}`;
      request.post(baseUrl).then((res) => {
        if (res.data.code == 200) {
          this.lst = res.data.data;
        }else{
          this.$message.warning(res.data.msg||"获取数据失败");
        }
      });
    },
    changePublickMark(row) {
      var paramObj = Object.assign({}, row);
      this.$request
        .post("/utils/D96M03B1/update", JSON.stringify(paramObj))
        .then((res) => {
          if (res.data.code == 200) {
            this.$message.success(
              res.data.data.publicmark == 1 ? "设置成功" : "取消成功"
            );
            // this.bindData();
            row = res.data.data;
          } else {
            this.$message.warning(res.data.msg || "保存失败");
          }
        });
    },
    getFile() {
      const inputDOM = this.$refs.upload;
      const file = inputDOM.files[0];
      console.log(file);
      this.file = file;
      this.uploadFileName = file.name;
      this.uploadFileSize = Number(file.size / 1024).toFixed(2);
      //   if(this.uploadFileSize>1024){ //+'KB'
      //     this.uploadFileSize=Number(file.size / 1024/1024).toFixed(2)+'MB'
      //   }
      this.uploadFileType = this.getFileType(file.name);
      this.uploadFile = require("@/assets/knows_images/" +
        this.uploadFileType +
        ".png");
    },
    submitUploadFile() {
      var formData = new FormData();
      formData.append("file", this.file);
      const baseUrl = `/utils/D96M03B1/upload?relateid=${this.idx}${
        this.modulecode ? `&modulecode=${this.modulecode}` : ""
      }${this.module ? `&module=${this.module}` : ""}`;
      request
        .post(baseUrl, formData)
        .then((res) => {
          if (res.data.code == 200) {
            this.$message.success("文件上传成功");
            // this.$emit('bindData');
            this.file = {};
            this.uploadFile = "";
            this.uploadFileName = "";
            this.uploadFileType = "";
            this.uploadFileSize = 0;
            this.bindData();
            // this.$emit("closeDialog");
          } else {
            this.$message.warning(res.data.msg || "文件上传失败");
          }
        })
        .catch((er) => {
          this.$message.error("请求错误");
        });
    },
    // 删除
    delItem(row) {
      this.$confirm("此操作将永久删除该附件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          request
            .get("/utils/D96M03B1/delete?key=" + row.id)
            .then((res) => {
              if (res.data.code == 200) {
                this.$message.success("删除成功");
                this.bindData();
              } else {
                this.$message.warning(res.data.msg || "删除失败");
              }
            })
            .catch((er) => {
              this.$message.error("请求错误");
            });
        })
        .catch(() => {});
    },
    closeViwer() {
      this.ImageUrlList="";
      this.imageViewVisible = false;
    },
    async checkPower(row, type) {
      const { dirname, filename, fileoriname } = row;
      const token = this.$store.getters.token;
      const baseurl = `/utils/File/proxy/${dirname}/${filename}?sec=${token}`;

      try {
        const res = await this.$request.get(baseurl, { responseType: "blob" });
        const pdfUrl = window.URL.createObjectURL(
          new Blob([res.data], { type: "application/pdf" })
        );

        if (type === "show") {
          this.handlePreview(pdfUrl);
        } else if (type === "download") {
          this.handleDownload(pdfUrl, fileoriname);
        }

        // 统一释放URL对象（防止内存泄漏）
        setTimeout(() => window.URL.revokeObjectURL(pdfUrl), 1000);
      } catch (err) {
        this.$message.error(err?.message || "文件操作失败");
      }
    },

    // 抽离预览逻辑
    handlePreview(pdfUrl) {
      this.ImageUrlList = pdfUrl;
      this.imageViewVisible = true;
      this.$nextTick(() => {
        this.$refs.ImageViewerRef?.$el?.style.setProperty("z-index", "9999");
      });
    },

    // 抽离下载逻辑
    handleDownload(pdfUrl, filename) {
      const link = document.createElement("a");
      link.href = pdfUrl;
      link.download = filename;
      link.style.display = "none";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link); // 清理DOM
    },
    // 获取文件大小
    getFileSize(data) {
      var size = Number(data / 1024).toFixed(2) + "KB";
      return size;
    },
    // 获取文件类型
    getFileType(fileName) {
      // 根据后缀判断文件类型
      let fileSuffix = "";
      // 结果
      let result = "";
      try {
        let flieArr = fileName.split(".");
        fileSuffix = flieArr[flieArr.length - 1];
      } catch (err) {
        fileSuffix = "";
      }
      // fileName无后缀返回 false
      if (!fileSuffix) {
        result = false;
        return result;
      }

      // 匹配txt
      let txtlist = ["txt"];
      result = txtlist.some(function (item) {
        return item == fileSuffix;
      });
      if (result) {
        result = "txt";
        return result;
      }
      // 匹配 excel
      let excelist = ["xls", "xlsx"];
      result = excelist.some(function (item) {
        return item == fileSuffix;
      });
      if (result) {
        result = "excel";
        return result;
      }
      // 匹配 word
      let wordlist = ["doc", "docx"];
      result = wordlist.some(function (item) {
        return item == fileSuffix;
      });
      if (result) {
        result = "word";
        return result;
      }
      // 匹配 pdf
      let pdflist = ["pdf"];
      result = pdflist.some(function (item) {
        return item == fileSuffix;
      });
      if (result) {
        result = "pdf";
        return result;
      }
      // 匹配 ppt
      let pptlist = ["ppt", "pptx"];
      result = pptlist.some(function (item) {
        return item == fileSuffix;
      });
      if (result) {
        result = "ppt";
        return result;
      }
      // 图片格式
      let imglist = ["png", "jpg", "jpeg", "bmp", "gif", "webp"];
      // 进行图片匹配
      result = imglist.some(function (item) {
        return item == fileSuffix.toLowerCase();
      });
      if (result) {
        result = "image";
        return result;
      }
      //   // 匹配 视频
      //   let videolist = ["mp4", "m2v", "mkv"];
      //   result = videolist.some(function(item) {
      //     return item == fileSuffix;
      //   });
      //   if (result) {
      //     result = "video";
      //     return result;
      //   }
      //   // 匹配 音频
      //   let radiolist = ["mp3", "wav", "wmv"];
      //   result = radiolist.some(function(item) {
      //     return item == fileSuffix;
      //   });
      //   if (result) {
      //     result = "radio";
      //     return result;
      //   }
      // 其他 文件类型
      result = "other";
      return result;
    },
  },
};
</script>
<style lang="scss" scoped>
.fileUpload {
  display: flex;
  align-items: center;
  //   margin-top: 10px;
  padding: 5px;
  position: relative;
  .fileUploadShow {
    width: 75px;
    height: 80px;
    text-align: center;
    padding: 5px;
    img {
      width: auto;
      height: 100%;
      max-width: 100%;
      min-width: 50px;
      // max-height: 100%;
    }
  }
  .fileUploadInfo {
    margin-left: 10px;
    line-height: 20px;
    width: calc(100% - 115px);
    p {
      margin: 5px 0;
    }
  }
}
.fileList {
  cursor: pointer;
  position: relative;
  height: 0;
  overflow: auto;

  .fileList-item {
    margin: 8px 0;
    border-radius: 5px;
    .closeBtn {
      position: absolute;
      right: 10px;
      top: 10px;
      i {
        font-size: 20px;
        font-weight: bold;
        color: rgb(235, 14, 14);
      }
      i:hover {
        background: rgb(248, 238, 238);
      }
    }
    .downFileBtn {
      color: #409eff;
    }
  }
  .fileList-item:hover {
    background: #c6e2ff;
  }
}
.showFileList {
  animation: showHeight 0.5s ease;
  -webkit-animation: showHeight 0.5s ease;
  animation-fill-mode: forwards;
  height: 0px;
}
@keyframes showHeight {
  from {
    height: 0px;
  }
  to {
    height: 220px;
  }
}

@-webkit-keyframes showHeight {
  from {
    height: 0px;
  }
  to {
    height: 220px;
  }
}
.hideFileList {
  animation: hideHeight 0.5s ease;
  -webkit-animation: hideHeight 0.5s ease;
  animation-fill-mode: forwards;
  height: 220px;
}
@keyframes hideHeight {
  from {
    height: 220px;
  }
  to {
    height: 0px;
  }
}

@-webkit-keyframes hideHeight {
  from {
    height: 220px;
  }
  to {
    height: 0px;
  }
}
.checkFile {
  margin: 8px auto;
  text-align: center;
  cursor: pointer;
  span {
    color: #409eff;
  }
}
.noData {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 22px;
  color: #c0c4cc;
}
.ellipsis {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-wrap: break-word;
}
</style>
