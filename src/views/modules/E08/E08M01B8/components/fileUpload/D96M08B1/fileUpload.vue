<template>
  <div>
    <!-- <el-button type="primary" @click="$refs.upload.click()">文档上传</el-button> -->
    <el-form
      ref="formdata"
      :model="postscriptdata"
      :label-width="'80px'"
      class="custInfo"
    >
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item label="附言标题">
            <el-input
              v-model="postscriptdata.billtitle"
              placeholder="请输入附言标题"
              clearable
              size="small"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="分组" prop="gengroupid">
            <el-cascader
              v-model="postscriptdata.gengroupid"
              :options="groupData"
              :props="defaultProps"
              clearable
              change-on-select
              :show-all-levels="false"
              style="width: 100%"
              @change="handleChange"
              size="small"
            ></el-cascader>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="标签">
            <el-input
              v-model="postscriptdata.billlabel"
              placeholder="请输入标签"
              clearable
              size="small"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <MyEditor
      ref="MyEditor"
      :height="400"
      :html="postscriptdata.filecontent"
      :excludeKeys="[
        'group-image',
        'insertImage',
        'uploadImage',
        'insertLink',
        'group-video',
        'insertVideo',
        'uploadVideo',
        'fullScreen',
      ]"
    ></MyEditor>
  </div>
</template>
<script>
import MyEditor from "@/components/editorTemplate/index.vue";
import request from "@/utils/request";
export default {
  props: ["idx", "formdata"],
  components: {
    MyEditor,
  },
  data() {
    return {
      postscriptdata: {
        billlabel: "",
        billtitle: "",
        deptid: "",
        filecontent: "",
        filesize: 0,
        gengroupid: "",
        relatedesc: "",
        relateid: "",
        remark: "",
        revision: 0,
        rownum: 0,
        id: "",
      },
      treeTitle: "附言管理",
      groupData: [], //树形图数据
      defaultProps: {
        children: "children",
        label: "label",
        value: "id",
      },
      content: "",
      queryParams: {
        PageNum: 1,
        PageSize: 10,
        OrderType: 1,
        SearchType: 1,
      },
    };
  },
  created() {
    this.bindData();
    this.BindTreeData();
  },
  methods: {
    bindData() {
      this.queryParams.SearchPojo = { relateid: this.idx };
      request
        .post("/utils/D96M08B1/getPageList", JSON.stringify(this.queryParams))
        .then((res) => {
          console.log("D96M08B1", res);
          if (res.data.code == 200) {
            if(res.data.data){
              this.postscriptdata = res.data.data.list[0];
            }

            // this.content=res.data.data.list[0].filecontent
          }
        });
    },
    submitUploadFile() {
      this.postscriptdata.relateid = this.idx;
      this.postscriptdata.relatedesc =
        this.formdata.goodsuid + this.formdata.goodsname;
      this.postscriptdata.filecontent = this.$refs.MyEditor.docHtml;
      console.log(this.postscriptdata);
      if (!!this.postscriptdata.id) {
        var baseUrl = "/utils/D96M08B1/update";
      } else {
        var baseUrl = "/utils/D96M08B1/create";
      }
      request
        .post(baseUrl, JSON.stringify(this.postscriptdata))
        .then((res) => {
          if (res.data.code == 200) {
            this.$emit("closeDialog");
            this.$emit("bindData");
            this.$message.success(res.data.msg || "添加附言成功");
          } else {
            this.$message.warning(res.data.msg || "添加附言失败");
          }
        })
        .catch((er) => {
          this.$message.error("请求错误");
        });
    },
    BindTreeData() {
      request
        .get("/system/SYSM07B8/getListByModuleCode?Code=D96M08B1")
        .then((response) => {
          if (response.data.code == 200) {
            let res = response.data.data.map((v) => {
              return {
                id: v.id,
                pid: v.parentid,
                label: v.groupname,
              };
            });
            let firstItem = [
              {
                id: "0",
                pid: "root",
                label: this.treeTitle,
              },
            ];
            var arr = [...res, ...firstItem];
            this.gengroupData = arr;
            this.groupData = this.transData(arr, "id", "pid", "children");
          }
        });
    },
    handleChange(value) {
      console.log(value);
      if (value.length > 0) {
        this.postscriptdata.gengroupid = value[value.length - 1];
      } else {
        this.postscriptdata.gengroupid = "0";
      }
    },
    transData(a, idStr, pidStr, chindrenStr) {
      var r = [],
        hash = {},
        id = idStr,
        pid = pidStr,
        children = chindrenStr,
        i = 0,
        j = 0,
        len = a.length;
      for (; i < len; i++) {
        hash[a[i][id]] = a[i];
      }
      for (; j < len; j++) {
        var aVal = a[j],
          hashVP = hash[aVal[pid]];
        if (hashVP) {
          !hashVP[children] && (hashVP[children] = []);
          hashVP[children].push(aVal);
        } else {
          r.push(aVal);
        }
      }
      return r;
    },
  },
};
</script>
<style lang="scss" scoped>
.fileUpload {
  display: flex;
  align-items: center;
  //   margin-top: 10px;
  padding: 5px;
  position: relative;
  .fileUploadShow {
    width: 75px;
    height: 80px;
    text-align: center;
    padding: 5px;
    img {
      width: auto;
      height: 100%;
      max-width: 100%;
      min-width: 50px;
      // max-height: 100%;
    }
  }
  .fileUploadInfo {
    margin-left: 10px;
    line-height: 20px;
    width: calc(100% - 115px);
  }
}

.noData {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 22px;
  color: #c0c4cc;
}
.ellipsis {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-wrap: break-word;
}
</style>
