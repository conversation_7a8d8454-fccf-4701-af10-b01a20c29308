<template>
  <div>
    <el-table
      v-loading="listLoading"
      :data="lst"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
      style="overflow: auto"
      :header-cell-style="{
        background: '#F3F4F7',
        color: '#555',
        padding: '3px 0px 3px 0px',
      }"
      :cell-style="{ padding: '4px 0px 4px 0px' }"
      height="352px"
      @sort-change="changeSort"
    >
      <el-table-column align="center" label="ID" min-width="40">
        <template slot-scope="scope">
          <span>{{ scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="单据编号"
        align="center"
        min-width="100"
        show-overflow-tooltip
          prop="refno"
      >
        <template slot-scope="scope">
        <span>{{ scope.row.refno ? scope.row.refno : "单据编号" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="单据类型"
        align="center"
        min-width="80"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span>{{ scope.row.billtype }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="单据日期"
        align="center"
        min-width="100"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span>{{ scope.row.billdate | dateFormats }}</span>
        </template>
      </el-table-column>

      <el-table-column
        label="供应商"
        align="center"
        min-width="120"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.groupname }}
        </template>
      </el-table-column>
      <el-table-column
        label="货品名称"
        align="center"
        min-width="100"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span>{{ scope.row.goodsname }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="单位"
        align="center"
        min-width="80"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span>{{ scope.row.goodsunit }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="数量"
        align="center"
        min-width="80"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span>{{ scope.row.quantity }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="单价"
        align="center"
        min-width="80"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span>{{ scope.row.price }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="金额"
        align="center"
        min-width="100"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span>{{ scope.row.taxamount }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.PageNum"
      :limit.sync="queryParams.PageSize"
      @pagination="GetList"
    />
  </div>
</template>
<script>
import Pagination from "@/components/Pagination/index.vue";
import request from "@/utils/request";
export default {
  components: {
    Pagination,
  },
   props: ["idx"],
  data() {
    return {
      lst: [],
      searchstr: "", // sql查询文本
      total: 0,
      listLoading: !true,
      queryParams: {
        PageNum: 1,
        PageSize: 10,
        OrderType: 1,
        SearchType: 0,
      },
    };
  },
  created() {
  },
  mounted() {
    // this.bindData();
  },
  methods: {
    bindData() {
        console.log(this.idx,'idx')

      this.listLoading = true;
      this.queryParams.SearchPojo={goodsid:this.idx}
      request
        .post("/buy/D03M07B1/getPageList", JSON.stringify(this.queryParams))
        .then((response) => {

          if (response.data.code == 200) {
            this.lst = response.data.data.list;
            this.total = response.data.data.total;
          }
          this.listLoading = false;
        })
        .catch((error) => {
          this.listLoading = false;
        });
    },
    // 分页组件事件
    GetList(data) {
      this.queryParams.PageNum = data.page;
      this.queryParams.PageSize = data.limit;
      this.bindData();
    },
    // 查询
    search(res) {
      if (res != "") {
        this.queryParams.SearchPojo = {
          refno: res,
          billtitle:res,
          groupname: res,
          goodsname: res,
          goodsuid: res,
          partid:res,
          groupuid:res,
          groupname:res,
        };
      } else {
        this.$delete(this.queryParams, "SearchPojo");
      }
      this.$delete(this.queryParams, "OrderBy");
      this.queryParams.SearchType = 1;
      this.queryParams.PageNum = 1;
      this.bindData();
    },
    // 高级搜索
    advancedSearch(val) {
      this.queryParams.SearchPojo = val;
      this.queryParams.SearchType = 0; // 是否开启高级搜索
      this.queryParams.PageNum = 1;
      this.bindData();
    },
    // 排序
     changeSort(val) {
      if (val.order == "descending") {
        this.queryParams.OrderType = 1;
      } else {
        this.queryParams.OrderType = 0;
      }
      this.queryParams.OrderBy = this.$tableSort(val, this.tableForm);
      this.bindData();
    },
  },

};
</script>


