<template>
  <div>
    <!--  货品导入排版 -->
    <div>
      <!-- 头部 -->
      <div>
        <el-form
          ref="mainData"
          :model="mainData"
          :label-width="formLabelWidth"
          class="custInfo"
          auto-complete="off"
        >
          <!-- :rules="formRules" -->
          <el-row>
            <el-col :span="8">
              <el-form-item label="货品分组">
                <!-- <span  class="el-form-item__label">{{ formdata.navpid }}</span> -->
                <el-cascader
                  v-model="mainData.matGroup"
                  :options="menuData"
                  :props="defaultProps"
                  clearable
                  :show-all-levels="false"
                  size="small"
                  :disabled="goodsData.length ? true : false"
                  @change="handleCascader"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="默认仓库">
                <el-select
                  v-model="mainData.matStore"
                  :disabled="goodsData.length ? true : false"
                  placeholder="请选择仓库"
                  style="width: 100%"
                  size="small"
                >
                  <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                    @focus="setMinWidthEmpty"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="8">
              <el-form-item label="">
                <el-checkbox
                      v-model="formdata.packsnmark"
                      label="SN包装"
                      :true-label="1"
                      :false-label="0"
                    />
              </el-form-item>
            </el-col> -->
          </el-row>
        </el-form>
      </div>
      <el-divider content-position="left" />
      <!-- 导入预览 table -->
      <div>
        <el-row>
          <el-button-group>
            <el-button
              type="primary"
              icon="el-icon-folder-add"
              size="mini"
              @click="showImport"
            >
              文件导入
            </el-button>
            <el-button
              type="primary"
              icon="el-icon-brush"
              size="mini"
              :disabled="goodsData.length == 0"
              @click="checkGoodsUid"
            >
              识别编码
            </el-button>
          </el-button-group>
        </el-row>
        <el-table
          ref="multipleTable"
          :data="goodsData"
          element-loading-text="Loading"
          border
          fit
          highlight-current-row
          size="small"
          class="tb-edit tableBox"
          style="overflow: auto"
          :header-cell-style="{
            background: '#F3F4F7',
            color: '#555',
            padding: '4px 0px 4px 0px',
          }"
          :cell-style="{ padding: '0px 0px' }"
          :row-style="{ height: '40px' }"
          height="40vh"
        >
          <el-table-column align="center" label="ID" min-width="50">
            <template slot-scope="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>
          <!-- <el-table-column
                label="识别编码"
                align="center"
                min-width="100"
                show-overflow-tooltip
              >
                 <template slot-scope="scope">
                  <span>{{ scope.row.good ||"识别编码" }}</span>
                </template>
              </el-table-column> -->
          <el-table-column
            label="货品编码"
            align="center"
            min-width="100"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <span>{{ scope.row.goodsuid }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="货品名称"
            align="center"
            min-width="120"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <span>{{ scope.row.goodsname }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="货品规格"
            align="center"
            min-width="120"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <span>{{ scope.row.goodsspec }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="货品状态"
            align="center"
            min-width="120"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <span>{{ scope.row.goodsstate }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="单位"
            align="center"
            min-width="120"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <span>{{ scope.row.goodsunit }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="安全库存"
            align="center"
            min-width="120"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <span>{{ scope.row.safestock }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="当前库存"
            align="center"
            min-width="120"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <span>{{ scope.row.ivquantity }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="材质"
            align="center"
            min-width="120"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <span>{{ scope.row.material }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="条形码"
            align="center"
            min-width="120"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <span>{{ scope.row.barcode }}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column
            label="制表"
            align="center"
            min-width="60"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <span>{{ scope.row.lister }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="创建日期"
            align="center"
            min-width="70"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <span>{{ scope.row.createdate | dateFormat }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="修改日期"
            align="center"
            min-width="70"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <span>{{ scope.row.modifydate | dateFormat }}</span>
            </template>
          </el-table-column> -->
        </el-table>
      </div>
      <!-- 底部操作按钮 -->
      <div style="margin: 15px 0 0 0; height: 45px">
        <!-- <el-button
          type="primary"
          style="margin-right: 20px"
          @click="checkGoodsUid"
          >识别编码</el-button
        > -->
        <el-button
          type="primary"
          style="float: right; margin-right: 20px"
          @click="submitGoods"
        >确 定</el-button>
        <el-button
          style="float: right; margin-right: 20px"
          @click="closeDialog"
        >关 闭</el-button>
      </div>
    </div>
    <!-- 导入组件内容 -->
    <el-dialog
      title="货品信息"
      width="400px"
      :visible.sync="importVisble"
      append-to-body
    >
      <div>
        <el-upload
          ref="upload"
          class="upload-demo"
          :drag="true"
          action=""
          :multiple="false"
          :on-change="handleChange"
          :on-remove="handleRemove"
          :on-preview="handlePreview"
          :on-success="handleSuccess"
          :limit="limitUpload"
          :auto-upload="false"
          accept=".xlsx,.xls,.csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">上传文件</div>
          <!-- <em>上传文件</em> -->
          <div slot="tip" class="el-upload__tip">
            <p>
              只能上传xlsx / xls文件<el-button
                type="text"
                @click.native="modelExport"
              >下载模板</el-button>
            </p>
          </div>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          size="small"
          @click="importf"
        >确 定</el-button>
        <el-button size="small" @click="importVisble = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import request from '@/utils/request'
import CRUD from '../CRUD.JS'
export default {
  filters: {
    // ------------------------通用过滤--------------------------
    dateFormat(dataStr) {
      if (!dataStr) {
        return
      }
      var dt = new Date(dataStr)
      var y = dt.getFullYear()
      var m = (dt.getMonth() + 1).toString().padStart(2, '0')
      var d = dt.getDate().toString().padStart(2, '0')
      var hh = dt.getHours().toString().padStart(2, '0')
      var mm = dt.getMinutes().toString().padStart(2, '0')
      var ss = dt.getSeconds().toString().padStart(2, '0')
      return `${y}-${m}-${d} ${hh}:${mm}:${ss}`
    }
  },
  data() {
    return {
      limitUpload: 1,
      fileTemp: {},
      staffVisible: false,
      goodsData: [], // 表格的数据源
      formdata: {
        ageprice: 0,
        barcode: '',
        batchmg: 0,
        batchonly: 0,
        createdate: new Date(),
        modifydate: new Date(),
        deletelister: '',
        enabledmark: 1, // 有效性默认为1
        // fileguid: "string",相关附件
        goodsname: '',
        goodspinyin: '',
        goodsspec: '',
        goodsstate: '',
        goodsuid: '',
        goodsunit: '',
        groupid: '',
        inprice: 0,
        ivquantity: 0,
        material: '',
        outprice: 0,
        partid: '',
        pid: '',
        puid: '',
        remark: '',
        safestock: 0,
        storeid: '',
        storelistguid: '',
        storelistname: '',
        surface: '',
        uidgroupcode: '',
        uidgroupguid: '',
        uidgroupname: '',
        uidgroupnum: 0,
        versionnum: '',
        virtualitem: 0,
        // packsnmark: 1,
        lister: JSON.parse(window.localStorage.getItem('getInfo')).realname, // 制表
        createby: JSON.parse(window.localStorage.getItem('getInfo')).realname // 创建者
      },
      formLabelWidth: '100px',
      // 其他变量
      // 分组选项
      menuData: [],
      defaultProps: {
        children: 'children',
        label: 'label',
        value: 'id'
      },
      // 保存主数据
      mainData: {
        matGroup: '', // 货品分组 赋值注意this.mainData.matGroup[this.mainData.matGroup.length-1]
        matStore: '' // 货品仓库
      },
      // 选择组件数据  选择仓库
      // 选择组件数据
      options: [
        {
          value: '临时id', // 同步的值
          label: '仓库' // 显示的值
        }
      ],
      // 显示导入窗口
      importVisble: false,
      // 保存失败计数器
      failcount: 0,
      // 允许新增货品信息
      allowUpload: false
    }
  },
  created() {
    this.bindData()
    this.BindStoreData()
  },
  methods: {
    // 读取货品分组信息
    async bindData() {
      const queryParams = {
        PageNum: 1,
        PageSize: 500,
        OrderType: 1,
        SearchType: 1
      }
      this.listLoading = true
      await request
        // 需要修改为货品分组的api
        .post('/e08/E08M13S1/getPageList', JSON.stringify(queryParams)) // 货品API 接口
        // .post("/system/SYSM05B2/getPageList", JSON.stringify(queryParams)) // 参考API 接口
        .then((response) => {
          console.log('=====读取货品分组======', response)
          console.log(response.data.data.list)
          if (response.data.code == 200) {
            // 转换读取到的数据为需要的结构
            this.test = response.data.data.list
            console.log('实际读取的list', this.test)
            const res = response.data.data.list.map((v) => {
              return {
                id: v.id,
                pid: v.parentid,
                label: v.groupname
              }
            })
            var arr = [...res]
            this.menuData = this.transData(arr, 'id', 'pid', 'children')
            console.log('this.menuData', this.menuData)
          }
        })
    },
    // 读取仓库信息
    BindStoreData() {
      const queryParams = {
        PageNum: 1,
        PageSize: 500,
        OrderType: 1,
        SearchType: 1
      }
      request
        // 修改项api地址
        .post('/store/D04M21S1/getPageList', JSON.stringify(queryParams))
        .then((response) => {
          console.log('=====读取仓库信息======', response)
          if (response.data.code == 200) {
            console.log(response.data.data.list)
            this.options = response.data.data.list.map((v) => {
              return {
                value: v.id, // 同步的值 仓库id
                label: v.storename // 显示的值
              }
            })
          }
        })
        .catch((error) => {})
    },

    // 上传excel
    handleChange(file, fileList) {
      this.fileTemp = file.raw
      const fileName = file.raw.name
      const fileType = fileName.substring(fileName.lastIndexOf('.') + 1)
      // 判断上传文件格式
      if (this.fileTemp) {
        if (fileType == 'xlsx' || fileType == 'xls') {
          // this.importf(this.fileTemp);
        } else {
          this.$message({
            type: 'warning',
            message: '文件格式错误，请删除后重新上传！'
          })
        }
      } else {
        this.$message({
          type: 'warning',
          message: '请上传文件！'
        })
      }
    },
    // 上传-预览  //上传数据提交的excel 导入数据
    importf(obj) {
      const _this = this
      const inputDOM = this.$refs.inputer
      var file = this.fileTemp
      var formData = new FormData()
      formData.append('file', file)
      request
        .post('/e08/E08M14B1/importExecl', formData, {
          headers: {
            'Content-Type': 'multipart/form-data' // 值得注意的是，这个地方一定要把请求头更改一下
          }
        })
        .then((res) => {
          console.log('文件导入', res)
          if (res.data.code == 200) {
            this.$message.success('文件导入成功')
            // 关闭上传接口
            this.importVisble = false
            // 赋值table
            const oldData = res.data.data.map((v) => {
              v.storeid = _this.mainData.matStore // 加入仓库id
              v.uidgroupguid =
                _this.mainData.matGroup[_this.mainData.matGroup.length - 1] // 加入分组ID
              return v
            })
            this.goodsData = oldData
            // this.$emit('closeDialog')
          } else {
            this.$message.warning('文件导入失败，请重试')
          }
        })
    },
    handlePreview(file) {
      console.log(file)
    },
    handleSuccess(file, fileList) {
      console.log('handleSuccess', file)
    },
    handleRemove(file, fileList) {
      console.log('handleRemove', file)
    },
    // ==============================工具项================================
    setMinWidthEmpty(val) {
      // 无数据的情况下，给请选择提示设置最小宽度
      const domEmpty = document.getElementsByClassName(
        'el-select-dropdown__empty'
      )
      if (domEmpty.length > 0) {
        domEmpty[0].style['min-width'] = val.srcElement.clientWidth + 2 + 'px'
      }
    },
    // 格式化数据  zhang
    transData(a, idStr, pidStr, chindrenStr) {
      var r = []
      var hash = {}
      var id = idStr
      var pid = pidStr
      var children = chindrenStr
      var i = 0
      var j = 0
      var len = a.length
      for (; i < len; i++) {
        hash[a[i][id]] = a[i]
      }
      for (; j < len; j++) {
        var aVal = a[j]
        var hashVP = hash[aVal[pid]]
        if (hashVP) {
          !hashVP[children] && (hashVP[children] = [])
          hashVP[children].push(aVal)
        } else {
          r.push(aVal)
        }
      }
      return r
    },
    // 格式化读取的数据 hu
    changeFormat(data) {
      const result = []
      if (!Array.isArray(data)) {
        return result
      }
      // 移除子项 //货品无chiledren不需要
      // data.forEach((item) => {
      //   delete item.children;
      // });
      const map = {}
      data.forEach((item) => {
        // map[item.navid] = item;
        map[item.id] = item
      })
      data.forEach((item) => {
        const parent = map[item.navpid]

        if (parent) {
          (parent.children || (parent.children = [])).push(item)
        } else {
          result.push(item)
        }
      })
      return result
    },
    // =============================自定义==============================
    // 点击选择货品分组
    handleCascader(value) {
      console.log(value) // 返回数组要最后一项
    },
    // 显示货品导入小窗口
    showImport() {
      this.importVisble = true
    },
    // 下载模板
    modelExport() {
      request
        .get('/e08/E08M14B1/exportModel', { responseType: 'blob' })
        .then((res) => {
          console.log(res)
          const link = document.createElement('a')
          const blob = new Blob([res.data], { type: 'application/vnd.ms-excel' })
          link.style.display = 'none'
          link.href = URL.createObjectURL(blob)
          link.download = '货品信息模板.xls'
          document.body.appendChild(link)
          // 模拟点击事件
          link.click()
        })
        .catch((err) => {
          console.log(err)
        })
    },
    // 之后crud只提交没有goodsuid的对象
    async checkGoodsUid() {
      const that = this
      let newGoodsCounter = 0
      const promiseList = [] // 异步请求列表
      for (var i = 0; i < that.goodsData.length; i++) {
        const promise = new Promise((resolve, reject) => {
          // 查询goosUid是否存在
          const data = {
            goodsname: that.goodsData[i].goodsname,
            goodsspec: that.goodsData[i].goodsspec,
            partid: that.goodsData[i].partid ? that.goodsData[i].partid : ''
            // uidgroupguid: that.goodsData[i].uidgroupguid
            //   ? that.goodsData[i].uidgroupguid
            //   : "",
            // storelistguid: that.goodsData[i].storelistguid
            //   ? that.goodsData[i].storelistguid
            //   : "",
          } // 查询提交的对象
          const goodsIndex = i
          console.log('that.goodsData[i]', that.goodsData[i])
          CRUD.checkGoodsUid(data)
            .then((res) => {
              console.log('res.data', !!res.data)
              if (res.data) {
                // 货品已在货品表内
                // that.goodsData[goodsIndex] = res.data;
                this.goodsData[goodsIndex].goodsid = res.data.id
                this.goodsData[goodsIndex].goodsuid = res.data.goodsuid
                this.goodsData[goodsIndex].isNew = false
                resolve(res.data)
              } else {
                // 新货品不在货品表内
                newGoodsCounter++
                this.goodsData[goodsIndex].isNew = true
                reject('新货品不在货品表内')
              }
            })
            .catch((er) => {
              console.log(er)
              reject('查询失败')
            })
        })
        promiseList.push(promise)
      }
      await Promise.allSettled(promiseList)
        .then((res) => {
          console.log(res)
        })
        .catch((er) => {})
        .finally(() => {
          console.log(newGoodsCounter)
          that.$message(
            `识别完毕,共${that.goodsData.length}条,需导入新货品${newGoodsCounter}条`
          )
          this.allowUpload = true
        })
    },
    // 逐条向后端导入货品信息
    async submitGoods() {
      if (!this.allowUpload) {
        return this.$message(`请先在选择仓库和货品分组，导入货品再保存`)
      }
      console.log('执行逐个保存')
      var that = this
      let failcount = 0 // 失败计数器
      const promiseList = [] // 异步请求列表
      const num = 0
      for (var i = that.goodsData.length - 1; i >= 0; i--) {
        // 判断是否需要保存 新的才要保存
        if (that.goodsData[i].isNew) {
          // let promise = new Promise((resolve, reject) => {
          that.formdata.goodsname = that.goodsData[i].goodsname // 货品名称
          that.formdata.goodsuid = that.goodsData[i].goodsuid // 货品编码
          that.formdata.goodsspec = that.goodsData[i].goodsspec // 货品规格
          that.formdata.goodsunit = that.goodsData[i].goodsunit // 货品单位
          that.formdata.goodsstate = that.goodsData[i].goodsstate // 货品状态
          that.formdata.storeid = that.goodsData[i].storeid // 仓库id
          that.formdata.uidgroupguid = that.goodsData[i].uidgroupguid // 分组id
          that.formdata.partid = that.goodsData[i].partid // 分组id
          that.formdata.custom1 = that.goodsData[i].custom1 // 自定义1
          that.formdata.custom2 = that.goodsData[i].custom2 // 自定义1
          that.formdata.custom3 = that.goodsData[i].custom3 // 自定义1
          that.formdata.custom4 = that.goodsData[i].custom4 // 自定义1
          that.formdata.custom5 = that.goodsData[i].custom5 // 自定义1
          that.formdata.custom6 = that.goodsData[i].custom6 // 自定义1
          that.formdata.custom7 = that.goodsData[i].custom7 // 自定义1
          that.formdata.custom8 = that.goodsData[i].custom8 // 自定义1
          that.formdata.custom9 = that.goodsData[i].custom9 // 自定义1
          that.formdata.custom10 = that.goodsData[i].custom10 // 自定义1
          that.formdata.brandname = that.goodsData[i].brandname // 品牌

          that.formdata.ageprice = that.goodsData[i].ageprice
            ? that.goodsData[i].ageprice
            : 0
          that.formdata.barcode = that.goodsData[i].barcode
            ? that.goodsData[i].barcode
            : ''
          that.formdata.batchmg = that.goodsData[i].batchmg
            ? that.goodsData[i].batchmg
            : 0
          that.formdata.batchonly = that.goodsData[i].batchonly
            ? that.goodsData[i].batchonly
            : 0
          that.formdata.enabledmark = that.goodsData[i].enabledmark
            ? that.goodsData[i].enabledmark
            : 1
          that.formdata.inprice = that.goodsData[i].inprice
            ? that.goodsData[i].inprice
            : 0
          that.formdata.ivquantity = that.goodsData[i].ivquantity
            ? that.goodsData[i].ivquantity
            : 0
          that.formdata.material = that.goodsData[i].material
            ? that.goodsData[i].material
            : ''
          that.formdata.outprice = that.goodsData[i].outprice
            ? that.goodsData[i].outprice
            : 0
          // 其他非必要项目
          // that.formdata = that.goodsData[i] //整行提交
          const index = i
          await CRUD.importEntity(that.formdata)
            .then((res) => {
              console.log('保存结果', res)
              console.log('完成', num)
              if (res.code == 200) {
                that.goodsData.splice(index, 1)
                //  num++
                //  console.log('that.goodsData.length',that.goodsData.length)
                // resolve("保存成功");
              } else {
                // failcount++
                console.log('保存失败')
              }
            })
            .catch((er) => {
              failcount++
              console.log(failcount, '保存失败')
            })
          // });
          // promiseList.push(promise);
        }
      }
      if (that.goodsData.length == 0) {
        that.$emit('closeDialog')
        that.$emit('bindData')
      }
    },
    // 关闭对话框返回index页面
    closeDialog() {
      this.$emit('closeDialog')
    }
  }
}
</script>
<style lang="css" scoped>
.upload-demo {
  margin: 0 auto;
  text-align: center;
}
::v-deep.custInfo .el-row {
  margin-bottom: -20px;
  display: flex;
  flex-wrap: wrap;
}
</style>
