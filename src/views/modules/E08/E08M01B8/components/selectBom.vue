<template>
  <div>
    <div class="filter-container" style="margin-bottom: 5px">
      <el-input
        v-model="strfilter"
        placeholder="请输入查询"
        style="width: 200px"
        class="filter-item"
        size="small"
        @keyup.enter.native="search(strfilter)"
      />
      <el-button
        class="filter-item"
        type="primary"
        size="small"
        @click="search(strfilter)"
      >
        查询
      </el-button>
      <div style="float: right">
        <el-checkbox
          v-model="roletype"
          label="新品"
          :true-label="1"
          :false-label="0"
          size="mini"
          style="margin-right: 20px"
          @change="checkChange"
        />
        <el-radio-group v-model="goodsType" size="small" @change="getStatus()">
          <el-radio-button label="所有" />
          <el-radio-button label="成品" />
          <el-radio-button label="半成品" />
          <el-radio-button label="物料" />
        </el-radio-group>
      </div>
    </div>
    <div style="margin-bottom: 10px">
      <el-table
        ref="selectgoods"
        v-loading="listLoading"
        :data="lst"
        element-loading-text="Loading"
        height="352px"
        border
        fit
        highlight-current-row
        style="overflow: auto"
        :header-cell-style="{
          background: '#F3F4F7',
          color: '#555',
          padding: '3px 0px 3px 0px',
        }"
        :cell-style="{ padding: '4px 0px 4px 0px' }"
        :row-class-name="rowIndex"
        @row-click="rowClick"
        @sort-change="changeSort"
      >
        <!-- ---复选列--- -->

        <el-table-column v-if="multi == 1" type="selection" width="40" />
        <!-- :selectable="isRepeat" -->
        <el-table-column
          label="货品编码"
          align="center"
          min-width="50"
          show-overflow-tooltip
          sortable
        >
          <template slot-scope="scope">
            <span>{{ scope.row.goodsuid }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="货品名称"
          align="center"
          min-width="50"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span>{{ scope.row.goodsname }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="货品规格"
          align="center"
          min-width="50"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span>{{ scope.row.goodsspec }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="货品状态"
          align="center"
          min-width="50"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span>{{ scope.row.goodsstate }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="颜色"
          align="center"
          min-width="100"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span>{{ scope.row.surface }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="品牌"
          align="center"
          min-width="100"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span>{{ scope.row.brandname }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="单位"
          align="center"
          min-width="50"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span>{{ scope.row.goodsunit }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="当前库存"
          align="center"
          min-width="50"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span>{{ scope.row.ivquantity }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.PageNum"
      :limit.sync="queryParams.PageSize"
      @pagination="GetList"
    />
  </div>
</template>

<script>
import request from '@/utils/request'
import Pagination from '@/components/Pagination/index.vue'
export default {
  components: {
    Pagination
  },

  props: ['multi', 'goodsid', 'lstData'],
  data() {
    return {
      title: '货品信息',
      listLoading: true,
      lst: [], //  列表数据
      strfilter: '', // 搜索框内输入的值
      total: 0,
      radio: '', // 单选用参数默认为0即可
      selrows: '', // 选择的内容 单选变量
      queryParams: {
        PageNum: 1,
        PageSize: 10,
        OrderType: 1,
        SearchType: 1
      },
      stateOptions: [
        {
          value: '所有',
          label: '所有'
        },
        {
          value: '成品',
          label: '成品'
        },
        {
          value: '半成品',
          label: '半成品'
        },
        {
          value: '物料',
          label: '物料'
        }
      ],
      stateValue: '',
      roletype: 0,
      goodsType: '所有',
      goodsVal: 'p'
    }
  },
  computed: {
    configs() {
      return this.$store.getters.userinfo.configs
    }
  },
  created() {
    this.bindData()
  },
  methods: {
    isRepeat(val) {
      // Bom物料防重选
      if (this.configs['module.goods.bomitemsingle'] == 'false') {
        return true
      }
      // 识别本页goodsid和已选id做对比，如果有返回false，否则true
      if (this.lstData.find((obj) => obj.goodsid == val.id)) {
        return false
      }
      return true
    },
    //  单选列方法 07/26修改
    getCurrentRow(row) {
      this.$forceUpdate()
      this.selrows = row
      this.$emit('singleSel') // 07/26修改
    },
    // 分页组件事件
    GetList(data) {
      this.queryParams.PageNum = data.page
      this.queryParams.PageSize = data.limit
      this.bindData()
    },
    // 加载列表
    bindData() {
      // 按条件分页查询不包括指定货品 "?key="+this.goodsid
      if (!this.queryParams.scenedata) {
        this.queryParams.scenedata = [
          {
            field: 'Mat_Goods.enabledmark',
            fieldtype: 0,
            math: 'like',
            value: 1
          }
        ]
      } else {
        var index = this.queryParams.scenedata.findIndex(
          (a) => a.field == 'Mat_Goods.enabledmark'
        )
        if (index == -1) {
          this.queryParams.scenedata.push({
            field: 'Mat_Goods.enabledmark',
            fieldtype: 0,
            math: 'like',
            value: 1
          })
        }
      }
      this.listLoading = true
      request
        .post(
          '/e08/E08M13B1/getPageListUseBom?key=' + this.goodsid,
          JSON.stringify(this.queryParams)
        )
        .then((response) => {
          if (response.data.code == 200) {
            this.lst = response.data.data.list
            this.total = response.data.data.total
          }
          this.listLoading = false
        })
        .catch((error) => {
          this.listLoading = false
        })
    },
    // 查询
    search(res) {
      this.$delete(this.queryParams, 'scenedata')
      if (res != '') {
        this.queryParams.SearchPojo = {
          goodsuid: res,
          goodsname: res,
          groupid: res,
          goodsspec: res,
          partid: res,
          goodsstate: res,
          surface: res
        }
      } else {
        this.$delete(this.queryParams, 'SearchPojo')
      }
      this.queryParams.SearchType = 1
      this.queryParams.PageNum = 1
      this.bindData()
    },
    // 排序
    changeSort(val) {
      if (val.order == 'descending') {
        this.queryParams.OrderType = 1
      } else {
        this.queryParams.OrderType = 0
      }
      this.queryParams.OrderBy = 'Mat_Goods.goodsuid'
      this.bindData()
    },
    rowIndex({ row, rowIndex }) {
      row.row_index = rowIndex
    },
    rowClick(row) {
      console.log(row, 'row')
      this.radio = row.row_index
      this.getCurrentRow(row)
    },
    dateForma(dataStr) {
      if (!dataStr) {
        return
      }
      var dt = new Date(dataStr)
      var y = dt.getFullYear()
      var m = (dt.getMonth() + 1).toString().padStart(2, '0')
      var d = dt.getDate().toString().padStart(2, '0')
      var hh = dt.getHours().toString().padStart(2, '0')
      var mm = dt.getMinutes().toString().padStart(2, '0')
      var ss = dt.getSeconds().toString().padStart(2, '0')
      return `${y}-${m}-${d} ${hh}:${mm}:${ss}`
    },
    startDate(val) {
      if (val == 'start') {
        return this.dateForma(
          new Date(new Date(new Date().toLocaleDateString()).getTime())
        )
      }
      return this.dateForma(
        new Date(
          new Date(new Date().toLocaleDateString()).getTime() +
            24 * 60 * 60 * 1000 -
            1
        )
      )
    },
    checkChange(val) {
      let scenedata = this.queryParams.scenedata
      const newScenedata = [
        {
          field: 'Mat_Goods.CreateDate',
          fieldtype: 2,
          math: 'between',
          value: this.startDate('start'),
          valueb: this.startDate()
        }
      ]
      if (scenedata && scenedata.length) {
        // 之前有精确查询条件
        scenedata = scenedata.filter(
          (item) => item.field != 'Mat_Goods.CreateDate'
        )
        if (val) {
          const set = new Set([...scenedata, ...newScenedata])
          this.queryParams.scenedata = Array.from(set)
        } else {
          this.queryParams.scenedata = scenedata
        }
      } else {
        if (val) {
          this.queryParams.scenedata = newScenedata
        } else {
          this.$delete(this.queryParams, 'scenedata')
        }
      }
      this.bindData()
    },
    getStatus() {
      this.stateChange(this.goodsType)
    },
    stateChange(val) {
      if (this.queryParams.scenedata && this.queryParams.scenedata.length) {
        this.queryParams.scenedata = this.queryParams.scenedata.filter(
          (item) => item.field != 'Mat_Goods.GoodsState'
        )
        if (val == '所有') {
          this.$delete(this.queryParams, 'SearchPojo')
          // this.$delete(this.queryParams, "scenedata");
          this.queryParams.SearchType = 1
          this.queryParams.PageNum = 1
        } else {
          this.$delete(this.queryParams, 'SearchPojo')
          this.queryParams.scenedata.push({
            field: 'Mat_Goods.GoodsState',
            math: 'equal',
            value: val
          })
        }
        this.bindData()
        return
      }
      if (val == '所有') {
        this.$delete(this.queryParams, 'SearchPojo')
        this.$delete(this.queryParams, 'scenedata')
        this.queryParams.SearchType = 1
        this.queryParams.PageNum = 1
      } else {
        this.$delete(this.queryParams, 'SearchPojo')
        this.queryParams.scenedata = [
          {
            field: 'Mat_Goods.GoodsState',
            math: 'equal',
            value: val
          }
        ]
      }
      this.bindData()
    }
  }
}
</script>
<style lang="scss" scoped>
.current-row {
  background: blue;
}
</style>
