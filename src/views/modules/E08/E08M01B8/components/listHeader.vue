<template>
  <div>
    <div :class="'filter-container flex j-s'">
      <div>
        <div
          style="
            display: inline-block;
            height: 28px;
            margin-right: 5px;
            cursor: pointer;
            color: #383838;
          "
          class="p-r"
          @click="$emit('btnshowGroup')"
        >
          <i
            class="p-r"
            :class="[showTree ? 'el-icon-folder-opened' : 'el-icon-folder']"
            style="font-size: 20px; top: 4px; color: #5e5c5c"
          />
        </div>
        <el-input
          v-model="strfilter"
          placeholder="请输入查询"
          prefix-icon="el-icon-search"
          style="width: 260px; height: 100%"
          class="filter-item"
          size="mini"
          @keyup.enter.native="btnSearch"
        >
          <el-button
            slot="append"
            class="filter-item"
            size="mini"
            @click="btnSearch"
          >搜索
          </el-button>
        </el-input>

        <el-button
          class="filter-item"
          style="margin-left: 10px"
          type="primary"
          icon="el-icon-edit"
          plain
          size="mini"
          @click="$emit('btnAdd')"
        >
          添加
        </el-button>
        <el-button
          class="filter-item"
          style="margin-left: 10px"
          type="primary"
          icon="el-icon-upload2"
          plain
          size="mini"
          @click="btnImport"
        >
          导入
        </el-button>
        <!-- <el-button
          class="filter-item"
          style="margin-left: 10px"
          type="primary"
          icon="el-icon-download"
          @click="Export"
          plain
          size="mini"
        >
          导出
        </el-button> -->
        <el-button
          class="filter-item"
          style="margin-left: 10px"
          type="danger"
          icon="el-icon-delete"
          plain
          size="mini"
          @click="allDelete"
        >
          删除
        </el-button>
        <el-button
          size="mini"
          icon="el-icon-printer"
          :title="'打印列表'"
          @click="$emit('pagePrint')"
        >打印</el-button>
        <el-button
          class="filter-item"
          style="margin-left: 10px"
          icon="el-icon-printer"
          plain
          size="mini"
          :title="'打印单据'"
          @click="$emit('btnPrint')"
        >
          单据
        </el-button>
      </div>

      <div class="iShowBtn">
        <el-button
          size="mini"
          icon="el-icon-search"
          title="高级筛选"
          :type="
            $store.state.advancedSearch.modulecode == tableForm.formcode
              ? 'primary'
              : 'default'
          "
          @click="openSearchForm()"
        />
        <el-button
          size="mini"
          icon="el-icon-refresh-right"
          title="刷新"
          @click="bindData"
        />
        <el-button
          size="mini"
          icon="el-icon-download"
          title="导出Excel"
          @click="btnExport"
        />
        <el-button
          size="mini"
          icon="el-icon-s-tools"
          @click="setColumsVisible = true"
        />
        <el-button
          size="mini"
          icon="el-icon-s-help"
          title="帮助"
          @click="$emit('btnHelp')"
        />
      </div>
    </div>
    <el-dialog
      v-if="setColumsVisible"
      width="800px"
      title="列设置"
      :append-to-body="true"
      :visible.sync="setColumsVisible"
      :close-on-click-modal="false"
      :destroy-on-close="true"
    >
      <SetColums
        ref="setcolums"
        :code="tableForm.formcode"
        :table-form="tableForm"
        @bindData="$emit('bindColumn')"
        @closeDialog="setColumsVisible = false"
      />
    </el-dialog>

    <el-dialog
      title="高级筛选"
      width="720px"
      :visible.sync="searchVisible"
      :append-to-body="true"
      :close-on-click-modal="false"
      :destroy-on-close="true"
    >
      <SearchForm
        ref="searchForm"
        :code="tableForm.formcode"
        @advancedSearch="advancedSearch"
        @closedDialog="searchVisible = false"
        @bindData="bindData"
      />
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: 'Listheader',
  props: ['showTree', 'tableForm'],
  data() {
    return {
      strfilter: '',
      formdata: {},
      setColumsVisible: false,
      code: 'D91M01B1List',
      searchVisible: false
    }
  },
  methods: {
    advancedSearch(searchdata) {
      this.$emit('advancedSearch', searchdata)
      this.searchVisible = false
    },
    openSearchForm() {
      this.searchVisible = true
      setTimeout(() => {
        this.$refs.searchForm.getInit()
      }, 100)
    },
    btnSearch() {
      this.$emit('btnSearch', this.strfilter)
    },
    // 导入
    btnImport() {
      this.$emit('btnImport')
    },
    // 导出模板
    modelExport() {
      this.$emit('modelExport')
    },
    bindData() {
      this.$emit('bindData')
    },
    allDelete() {
      this.$emit('allDelete')
    },
    btnExport() {
      this.$emit('btnExport')
    },
    Export() {
      this.$emit('Export')
    }
  }
}
</script>
<style lang="scss" scoped>
.filter-container {
  margin: 0 5px;
  padding: 4px 0px;
  box-sizing: border-box;
  width: 98%;
  position: relative;
  align-items: flex-end;
}

.ishowDetail {
  border: 1px solid #ddd;
  border-bottom: 0;
}
.searchDetail {
  position: absolute;
  top: 38px;
  z-index: 99;
  background-color: #fff;
  width: 98%;
  border: 1px solid #ddd;
  border-top: 0px #ddd dotted;
  left: 10px;
  padding: 10px 10px;
  box-sizing: border-box;
  border-radius: 0 0 4px 4px;
}
.flex {
  display: flex;
  align-items: center;
}
.a-c {
  align-items: center;
}
.j-c {
  justify-content: center;
}
.j-s {
  justify-content: space-between;
}
.p-r {
  position: relative;
}
.p-a {
  position: absolute;
}
.iShowBtn {
  margin-right: 10px;
  cursor: pointer;
  i:hover,
  i:active {
    color: #409eff;
  }
}
</style>
