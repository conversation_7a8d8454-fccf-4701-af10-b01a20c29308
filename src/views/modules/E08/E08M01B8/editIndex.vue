<template>
  <FormEdit
    v-if="visible"
    :idx="idx"
    :goods-cust-list="goodsCustList"
  />
</template>
<script>
import FormEdit from './components/formEdit.vue'
export default {
  name: 'EditIndex',
  components: { FormEdit },
  data() {
    return {
      idx: 0,
      visible: true,
      goodsCustList: []
    }
  },
  created() {
    this.init()
    this.getGoodsCust()
  },
  methods: {
    init() {
      if (this.$ismicroapp) {
        const lstp = window.$wujie.props.lstp || {}
        const { idx } = lstp
        if (idx) {
          this.idx = idx
        }
      }
    },
    async getGoodsCust() {
      this.goodsCustList = []
      try {
        const res = await this.$request.get('/e08/E08M13S4/getList')
        if (res.data.code === 200) {
          this.goodsCustList = res.data.data || []
        } else {
          this.$message({
            message: res.data.msg,
            type: 'error'
          })
        }
      } catch (error) {
        console.log(error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
