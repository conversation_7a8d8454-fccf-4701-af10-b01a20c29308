<template>
  <div style="width: 100%; box-sizing: border-box">
    <!-- =======================form表单部分=================================== -->
    <!-- 以下为表单部分 -->
    <div style="padding: 20px 20px 20px 20px">
      <div style="width: 100%" :style="{ height: formcontainHeight }">
        <!-- ================头部======================= -->
        <div ref="form_main_info" class="form form-head p-r">
          <!-- ==================================新的form格式======================================= -->
          <el-form
            ref="formdata"
            :model="formdata"
            :label-width="formLabelWidth"
            class="custInfo"
            :rules="formRules"
          >
            <div class="formBox">
              <div class="formBoxLeft">
                <el-row>
                  <el-col :span="12">
                    <div>
                      <el-form-item label="父级名称">
                        <el-input
                          v-model="groupPname"
                          size="small"
                          style="width: 200px"
                          disabled
                          readonly
                        />
                      </el-form-item>
                    </div>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <div @click="cleValidate('groupname')">
                      <el-form-item label="分组名称" prop="groupname">
                        <el-input
                          v-model="formdata.groupname"
                          placeholder="请输入分组名称"
                          clearable
                          size="small"
                          style="width: 200px"
                          @input="writeCode"
                        />
                      </el-form-item>
                    </div>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <div @click="cleValidate('groupcode')">
                      <el-form-item label="分组编码">
                        <el-input
                          v-model="formdata.groupcode"
                          placeholder="请输分组编码"
                          size="small"
                          style="width: 200px"
                        />
                      </el-form-item>
                    </div>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="10">
                    <div @click="cleValidate('rownum')">
                      <el-form-item label="排序" prop="rownum">
                        <el-input-number
                          v-model="formdata.rownum"
                          controls-position="right"
                          :min="0"
                          size="small"
                          style="width: 200px"
                        />
                      </el-form-item>
                    </div>
                  </el-col>
                </el-row>
              </div>
              <div class="formBoxRight">
                <el-row>
                  <el-col :span="6">
                    <div>
                      <el-form-item prop="allowitem">
                        <el-checkbox
                          v-model="formdata.allowitem"
                          label="允许货品建立"
                          :true-label="1"
                          :false-label="0"
                          size="mini"
                        />
                      </el-form-item>
                    </div>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <div @click="cleValidate('prefix')">
                      <el-form-item label="前缀" prop="prefix">
                        <el-input
                          v-model="formdata.prefix"
                          placeholder="请输入前缀"
                          clearable
                          size="small"
                          style="width: 200px"
                          :disabled="!formdata.allowitem"
                        />
                      </el-form-item>
                    </div>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <div @click="cleValidate('suffix')">
                      <el-form-item label="后缀" prop="suffix">
                        <el-input
                          v-model="formdata.suffix"
                          placeholder="请输后缀"
                          clearable
                          size="small"
                          style="width: 200px"
                          :disabled="!formdata.allowitem"
                        />
                      </el-form-item>
                    </div>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <div @click="cleValidate('sncode')">
                      <el-form-item label="序号位" prop="sncode">
                        <el-select
                          v-model="formdata.sncode"
                          filterable
                          allow-create
                          clearable
                          placeholder="请输入序号位"
                          size="small"
                          style="width: 200px"
                          :disabled="!formdata.allowitem"
                        >
                          <el-option label="[00]" value="[00]" />
                          <el-option label="[000]" value="[000]" />
                          <el-option label="[0000]" value="[0000]" />
                          <el-option label="[00000]" value="[00000]" />
                          <el-option label="[000000]" value="[000000]" />
                          <el-option label="[0000000]" value="[0000000]" />
                        </el-select>
                      </el-form-item>
                    </div>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <div
                      style="
                        line-height: 40px;
                        padding-left: 60px;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                      "
                    >
                      <el-checkbox
                        v-model="formdata.batchmg"
                        label="批次存储"
                        :true-label="1"
                        :false-label="0"
                      />
                      <el-checkbox
                        v-model="formdata.batchonly"
                        label="批次独立"
                        :true-label="1"
                        :false-label="0"
                      />
                    </div>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <div
                      style="
                        line-height: 40px;
                        padding-left: 60px;
                        margin: 10px 0;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                      "
                    >
                      <el-checkbox
                        v-model="formdata.packsnmark"
                        label="SN包装"
                        :true-label="1"
                        :false-label="0"
                      />
                      <el-checkbox
                        v-model="formdata.skumark"
                        label="SKU"
                        :true-label="1"
                        :false-label="0"
                      />
                    </div>
                  </el-col>
                </el-row>
              </div>
            </div>
          </el-form>
        </div>
        <el-form :label-width="formLabelWidth" class="footFormContent">
          <el-row style="margin-right: 20px">
            <el-col :span="24">
              <el-form-item
                label="摘  要"
                label-position="right"
                label-width="100px"
              >
                <el-input
                  v-model="formdata.remark"
                  placeholder="请输入摘要"
                  clearable
                  size="small"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="4">
              <el-form-item label="创建人">
                <span v-show="formdata.createby" class="el-form-item__label">{{
                  formdata.createby
                }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="创建日期">
                <span
                  v-show="formdata.createdate"
                  class="el-form-item__label"
                >{{ formdata.createdate | dateFormat }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="制表">
                <span v-show="formdata.lister" class="el-form-item__label">{{
                  formdata.lister
                }}</span>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="修改日期">
                <span
                  v-show="formdata.createdate"
                  class="el-form-item__label"
                >{{ formdata.modifydate | dateFormat }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
    <div class="button-container flex j-end">
      <el-button
        v-preventReClick
        type="primary"
        size="small"
        @click.native="submitForm('formdata')"
      >
        保 存</el-button>
      <el-button size="small" @click.native="closeForm"> 关 闭</el-button>
    </div>
    <!-- ===================================弹出组件===================================== -->
  </div>
</template>
<script>
import request from '@/utils/request'
import CRUD from '../CRUD.JS'
const pinyin = require('js-pinyin')
export default {
  name: 'Formedit',
  filters: {
    dateFormat(dataStr) {
      var dt = new Date(dataStr)
      var y = dt.getFullYear()
      var m = (dt.getMonth() + 1).toString().padStart(2, '0')
      var d = dt.getDate().toString().padStart(2, '0')
      var hh = dt.getHours().toString().padStart(2, '0')
      var mm = dt.getMinutes().toString().padStart(2, '0')
      var ss = dt.getSeconds().toString().padStart(2, '0')
      return `${y}-${m}-${d} ${hh}:${mm}:${ss}`
    }
  },
  props: ['idx', 'groupnum'],
  data() {
    return {
      // formdata常规变量
      // 表单信息
      formdata: {
        // 不变项
        lister: JSON.parse(window.localStorage.getItem('getInfo')).realname, // 制表
        createby: JSON.parse(window.localStorage.getItem('getInfo')).realname,
        // 其他formdata
        parentid: '', // 父级ID
        grouptype: '', // 分组类型
        groupcode: '', // 分组编码
        groupname: '', // 分组名称
        rownum: 0,
        grouplevel: 0, // 层数
        allowitem: 1, //
        statecode: '',
        childcount: 0,
        remark: '',
        groupsvg: '',
        skumark: 0, // sku
        packsnmark: 0, // SN包装
        batchmg: 0, // 批次存储
        batchonly: 0 // 批次独立
      },
      // form表单验证
      formRules: {
        // parentid: [
        //   { required: true, trigger: "blur", message: "父级不能为空" },
        // ],
        grouptype: [
          { required: true, trigger: 'blur', message: '分组类型不能为空' }
        ],
        groupcode: [
          { required: true, trigger: 'blur', message: '分组编码不能为空' }
        ],
        groupname: [
          { required: true, trigger: 'blur', message: '分组名称不能为空' }
        ]
      },
      formLabelWidth: '100px',
      dialogVisible: false, // 弹窗
      delDialogVisible: false, // 删除提示
      // ====================非通用变量=============================
      option: '',
      defaultProps: {
        children: 'children',
        label: 'label',
        value: 'id',
        checkStrictly: true
      },
      groupData: [],
      groupPid: '',
      groupPname: '' // 父级分组名称,
    }
  },
  computed: {
    formcontainHeight() {
      return 300 + 'px'
    }
  },
  watch: {
    idx: function(val, oldVal) {
      this.bindData()
    }
  },
  created() {
    this.formdata.parentid = this.$attrs.pid ? this.$attrs.pid : 0
    this.bindData()
    const obj = this.getPlabel(this.$attrs.groupData, this.$attrs.pid)
    this.groupPname = obj.label
  },
  mounted() {
    this.Bindgroupname()
  },
  methods: {
    Bindgroupname() {
      this.listLoading = true
      const data = {
        PageNum: 1,
        PageSize: 10000,
        OrderType: 0,
        SearchType: 1,
        OrderBy: 'rownum'
      }
      request
        .post('/e08/E08M13S1/getPageList', JSON.stringify(data))
        .then((response) => {
          if (response.data.code == 200) {
            this.test = response.data.data.list
            const res = response.data.data.list.map((v) => {
              return {
                id: v.id,
                pid: v.parentid,
                label: v.groupname,
                prefix: v.prefix,
                groupsvg: v.groupsvg,
                rownum: v.rownum
              }
            })
            const firstItem = [
              {
                id: '0',
                pid: -1,
                label: '货品分组',
                prefix: false,
                groupsvg: '',
                rownum: 0
              }
            ]
            var arr = [...res, ...firstItem]
            this.groupData = this.transData(arr, 'id', 'pid', 'children')
          }
          this.listLoading = false
        })
        .catch((error) => {
          this.listLoading = false
        })
    },
    transData(a, idStr, pidStr, chindrenStr) {
      var r = []
      var hash = {}
      var id = idStr
      var pid = pidStr
      var children = chindrenStr
      var i = 0
      var j = 0
      var len = a.length
      for (; i < len; i++) {
        hash[a[i][id]] = a[i]
      }
      for (; j < len; j++) {
        var aVal = a[j]
        var hashVP = hash[aVal[pid]]
        if (hashVP) {
          !hashVP[children] && (hashVP[children] = [])
          hashVP[children].push(aVal)
        } else {
          r.push(aVal)
        }
      }
      return r
    },
    handleChangeGroupid(val) {
      // if(this.formdata.id==val[val.length - 1]){
      //  this.$message.warning("父级不能是自己")
      // }
      this.$set(this.formdata, 'parentid', val[val.length - 1])
      this.$forceUpdate()
    },
    // 获取父级名称
    getPlabel(tree, id) {
      for (const node of tree) {
        if (node.id === id) {
          return node
        }
        if (node.children) {
          const childItem = this.getPlabel(node.children, id)
          if (childItem) {
            return childItem
          }
        }
      }
      return []
    },
    // -----------------------公共项-------------------------------
    // 加载列表
    bindData() {
      this.listLoading = true
      if (this.idx != 0) {
        request
          .get(`/E08M13S1/getEntity?key=${this.idx}`)
          .then((response) => {
            if (response.data.code == 200) {
              this.formdata = response.data.data
              this.groupPid = this.formdata.parentid
            } else {
            }
            this.listLoading = false
          })
          .catch((error) => {
            this.listLoading = false
            this.$message.error('请求错误')
          })
      } else {
        this.formdata.rownum = this.groupnum
      }
    },
    // el默认表单提交验证
    submitForm(formdata) {
      this.$refs[formdata].validate((valid) => {
        if (valid) {
          this.saveForm()
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    saveForm() {
      if (this.idx == 0) {
        CRUD.add(this.formdata)
          .then(() => {
            this.$message.success('保存成功')
            this.$emit('closeForm')
            this.$emit('bindData')
            this.bindData()
          })
          .catch((er) => {
            this.$message.warning(er || '保存失败')
          })
      } else {
        if (this.formdata.id == this.formdata.parentid) {
          this.$message.warning('保存失败：父级不能是自己，请重新选择')
          return
        }
        CRUD.update(this.formdata)
          .then(() => {
            this.$message.success('保存成功')
            this.$emit('closeForm')
            this.$emit('bindData')
            this.bindData()
          })
          .catch((er) => {
            this.$message.warning(er || '保存失败')
          })
      }
    },
    // 关闭formeadd对话框
    closeForm() {
      this.$emit('closeForm')
    },
    // 删除该工单 先询问再删除
    deleteForm(idx) {
      this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          console.log('执行删除')
          this.$microClose(800)
          CRUD.delete(idx)
            .then(() => {
              this.$message.success({
                message: '删除成功！'
              })
              this.$emit('compForm')
            })
            .catch(() => {
              this.$message({
                showClose: true,
                message: '删除失败',
                type: 'warning'
              })
            })
        })
        .catch(() => {})
    },
    check() {
      console.log('check')
    },
    // 清空form表单错误提示
    cleValidate(val) {
      this.$refs.formdata.clearValidate(val)
    },
    // 每个模块独立的方法 不通用
    // --------------------------自定义--------------------------
    writeCode(val) {
      pinyin.setOptions({ checkPolyphone: false, charCase: 1 })
      this.formdata.groupcode = pinyin.getFullChars(val)
    },
    // 可能在个别模块通用的方法
    // --------------------------工具项--------------------------
    getFile() {
      const inputDOM = this.$refs.upload
      const file = inputDOM.files[0]
      var formData = new Blob([file], { type: 'text/plain;charset=utf-8' })
      var reader = new FileReader()
      reader.onload = (e) => {
        this.formdata.groupsvg = e.target.result
      }
      reader.readAsText(formData)
      this.$forceUpdate()
    }
  }
  // 定义私用局部过滤器。只能在当前 vue 对象中使用
}
</script>
<style  scoped  lang="scss">
//灰色背景
$bg-gray: #e2e2e2;
$bg-component: #f5f5f5;
.formBox {
  display: flex;
  justify-content: center;
  .formBoxLeft {
    padding-right: 60px;
    border-right: 1px solid #ccc;
  }
}
::v-deep .el-scrollbar__wrap {
  overflow-x: hidden;
}
::v-deep .is-horizontal {
  display: none;
}
::v-deep .el-form-item {
  width: 100%;
}
::v-deep.custInfo .el-row {
  margin-bottom: -20px;
  display: flex;
  flex-wrap: wrap;
}
/**修改错误提示 */
::v-deep .el-form-item__error {
  top: 0;
  background: #fff;
  height: 30px;
  margin-top: 5px;
  margin-left: 5px;
  width: 120px;
  display: flex;
  align-items: center;
  // display: flex;
  // flex-wrap: wrap ;
}
// ::v-depp .el-input__inner{

// }
/**美化用  外框阴影*/
.shandow {
  box-shadow: 0px 0px 10px $bg-gray;
}
/**位置css */
.p-r {
  position: relative;
}
.p-a {
  position: absolute;
}
.p-f {
  position: fixed;
}
/**flex css  */
.flex {
  display: flex;
  flex-wrap: wrap;
}
.f-1 {
  flex: 1;
}

.f-d-c {
  flex-direction: column;
}

.j-c {
  justify-content: center;
}

.a-c {
  align-items: center;
}

.j-s {
  justify-content: space-between;
}

.j-start {
  justify-content: flex-start;
}
.j-end {
  justify-content: flex-end;
}
//顶部工具栏
.form_button {
  .button-container {
    background: $bg-gray;
    padding: 10px;
    float: right;
  }
}
//表格边框
.form-border {
  border: 2px solid #dbdbdb;
}
.component-container {
  background: $bg-component;
  height: calc(100vh - 84px);
  .form-container {
    background: #fff;
  }
  .form {
    margin-right: 20px;
    margin-left: 20px;
    width: calc(100% - 40px);
  }
  .form-head {
    //工具栏上下padding 加按键高度 -容器的上padding
    margin-top: calc(10px + 32px + 10px - 20px);
    .refNo {
      margin-right: 30px;
      width: 300px;
    }
  }
}
/**更改 表单label的字体格式 */
::v-deep .el-form-item__label {
  font-size: 12px;
  font-weight: 700;
}
</style>
