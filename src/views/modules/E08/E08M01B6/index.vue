<template>
  <div class="cost-overview">
    <!-- 产品标题 -->
    <div class="product-title">
      <h1>按摩仪(FL10121)V2</h1>
    </div>

    <div class="content-wrapper">
      <!-- 左侧费用卡片 -->
      <div class="cost-cards">
        <div class="cost-card plastic">
          <div class="cost-label">塑料费用</div>
          <div class="cost-amount">¥7.039</div>
        </div>

        <div class="cost-card metal">
          <div class="cost-label">金属费用</div>
          <div class="cost-amount">¥3.768</div>
        </div>

        <div class="cost-card electronic">
          <div class="cost-label">电子费用</div>
          <div class="cost-amount">¥83.493</div>
        </div>

        <div class="cost-card accessories">
          <div class="cost-label">配件费用</div>
          <div class="cost-amount">¥2.751</div>
        </div>

        <div class="cost-card packaging">
          <div class="cost-label">包装费用</div>
          <div class="cost-amount">¥3.655</div>
        </div>

        <div class="cost-card processing">
          <div class="cost-label">加工费用</div>
          <div class="cost-amount">¥8.112</div>
        </div>

        <div class="cost-card investment">
          <div class="cost-label">投资费用</div>
          <div class="cost-amount">¥8.71</div>
        </div>

        <div class="cost-card finance">
          <div class="cost-label">财务费用</div>
          <div class="cost-amount">¥39.17</div>
        </div>
      </div>

      <!-- 右侧图表区域 -->
      <div class="chart-section">
        <div class="chart-header">
          <div class="chart-title">
            <span class="title-label">统计数据</span>
            <h2>BOM</h2>
          </div>
          <div class="toggle-switch">
            <el-switch v-model="chartEnabled" active-color="#409EFF"></el-switch>
          </div>
        </div>

        <div class="chart-container">
          <div ref="pieChart" class="pie-chart"></div>
          <div class="chart-legend">
            <div class="legend-item">
              <span class="legend-dot plastic-dot"></span>
              <span class="legend-label">塑料</span>
              <span class="legend-value">7%</span>
            </div>
            <div class="legend-item">
              <span class="legend-dot metal-dot"></span>
              <span class="legend-label">金属</span>
              <span class="legend-value">4%</span>
            </div>
            <div class="legend-item">
              <span class="legend-dot electronic-dot"></span>
              <span class="legend-label">电子</span>
              <span class="legend-value">83%</span>
            </div>
            <div class="legend-item">
              <span class="legend-dot accessories-dot"></span>
              <span class="legend-label">配件</span>
              <span class="legend-value">3%</span>
            </div>
            <div class="legend-item">
              <span class="legend-dot packaging-dot"></span>
              <span class="legend-label">包装</span>
              <span class="legend-value">4%</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'CostOverview',
  data() {
    return {
      chartEnabled: true
    }
  },
  mounted() {
    this.initPieChart()
  },
  methods: {
    initPieChart() {
      const chart = echarts.init(this.$refs.pieChart)
      const option = {
        series: [{
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['50%', '50%'],
          data: [
            { value: 7, name: '塑料', itemStyle: { color: '#6BB6FF' }},
            { value: 4, name: '金属', itemStyle: { color: '#A8C8EC' }},
            { value: 83, name: '电子', itemStyle: { color: '#4285F4' }},
            { value: 3, name: '配件', itemStyle: { color: '#B8D4F0' }},
            { value: 4, name: '包装', itemStyle: { color: '#D1E3F8' }}
          ],
          label: { show: false },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      }
      chart.setOption(option)
    }
  }
}
</script>

<style lang="scss" scoped>
.cost-overview {
  padding: 20px;
  background: #f5f5f5;
  height: 600px;
}

.product-title {
  margin-bottom: 30px;

  h1 {
    font-size: 24px;
    font-weight: bold;
    color: #333;
    margin: 0;
  }
}

.content-wrapper {
  display: flex;
  gap: 40px;
}

.cost-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  flex: 1;
}

.cost-card {
  padding: 20px;
  border-radius: 12px;
  color: white;
  position: relative;

  .cost-label {
    font-size: 16px;
    margin-bottom: 10px;
    opacity: 0.9;
  }

  .cost-amount {
    font-size: 28px;
    font-weight: bold;
  }

  &.plastic {
    background: linear-gradient(135deg, #6BB6FF, #4A9EFF);
  }

  &.metal {
    background: linear-gradient(135deg, #9E9E9E, #757575);
  }

  &.electronic {
    background: linear-gradient(135deg, #2196F3, #1976D2);
  }

  &.accessories {
    background: linear-gradient(135deg, #26C6DA, #00ACC1);
  }

  &.packaging {
    background: linear-gradient(135deg, #FFB74D, #FF9800);
  }

  &.processing {
    background: linear-gradient(135deg, #5C6BC0, #3F51B5);
  }

  &.investment {
    background: linear-gradient(135deg, #66BB6A, #4CAF50);
  }

  &.finance {
    background: linear-gradient(135deg, #424242, #212121);
  }
}

.chart-section {
  flex: 1;
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;

  .chart-title {
    .title-label {
      font-size: 14px;
      color: #999;
      display: block;
      margin-bottom: 5px;
    }

    h2 {
      font-size: 32px;
      font-weight: bold;
      color: #333;
      margin: 0;
    }
  }
}

.chart-container {
  display: flex;
  align-items: center;
  gap: 40px;
}

.pie-chart {
  width: 300px;
  height: 300px;
}

.chart-legend {
  flex: 1;

  .legend-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;

    .legend-dot {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      margin-right: 12px;

      &.plastic-dot {
        background: #6BB6FF;
      }

      &.metal-dot {
        background: #A8C8EC;
      }

      &.electronic-dot {
        background: #4285F4;
      }

      &.accessories-dot {
        background: #B8D4F0;
      }

      &.packaging-dot {
        background: #D1E3F8;
      }
    }

    .legend-label {
      flex: 1;
      font-size: 16px;
      color: #333;
    }

    .legend-value {
      font-size: 16px;
      font-weight: bold;
      color: #333;
    }
  }
}

@media (max-width: 1200px) {
  .content-wrapper {
    flex-direction: column;
  }

  .chart-container {
    flex-direction: column;
    text-align: center;
  }
}
</style>
