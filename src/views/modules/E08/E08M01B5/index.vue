<template>
  <div class="cost-analysis">
    <!-- 产品标题 -->
    <div class="product-title">
      <h1>按摩仪(FL10121)V2</h1>
    </div>

    <!-- 成本卡片区域 -->
    <div class="cost-cards">
      <!-- 总成本卡片 -->
      <div class="cost-card total-cost">
        <el-progress :width="100" type="circle" :percentage="60" :format="(p) => `${p}%\r\n报价进度`">报价进度</el-progress>
      </div>

      <!-- 各项成本卡片 -->
      <div class="cost-card">
        <div class="cost-header">
          <span class="cost-type">塑料成本</span>
        </div>
        <div class="cost-value">25/14</div>
      </div>

      <div class="cost-card">
        <div class="cost-header">
          <span class="cost-type">金属成本</span>
        </div>
        <div class="cost-value">23/21</div>
      </div>

      <div class="cost-card">
        <div class="cost-header">
          <span class="cost-type">电子成本</span>
        </div>
        <div class="cost-value">12/4</div>
      </div>

      <div class="cost-card">
        <div class="cost-header">
          <span class="cost-type">配件成本</span>
        </div>
        <div class="cost-value">7/4</div>
      </div>
    </div>

    <!-- 标签页 -->
    <div class="tabs-section">
      <el-tabs v-model="activeTab" class="cost-tabs">
        <el-tab-pane label="塑料" name="plastic" />
        <el-tab-pane label="金属" name="metal" />
        <el-tab-pane label="电子" name="electronic" />
        <el-tab-pane label="配件" name="accessories" />
        <el-tab-pane label="包装" name="packaging" />
        <el-tab-pane label="人工" name="labor" />
      </el-tabs>

      <!-- 编辑按钮 -->
      <el-button v-if="false" type="text" class="edit-btn">编辑</el-button>
    </div>

    <!-- 详情表格 -->
    <div class="details-table">
      <ve-table
        :key="keynum"
        :columns="tableColumns"
        :table-data="tableData"
        :border-around="true"
        :border-x="true"
        :border-y="true"
        :row-style-option="rowStyleOption"
        :cell-style-option="cellStyleOption"
        :max-height="400"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'CostAnalysis',
  data() {
    return {
      keynum: 0,
      activeTab: 'plastic',
      tableColumns: [
        {
          field: 'name',
          key: 'name',
          title: '物料名称',
          width: 80,
          align: 'center'
        },
        {
          field: 'brand',
          key: 'brand',
          title: '品牌',
          width: 100,
          align: 'center'
        },
        {
          field: 'specifications',
          key: 'specifications',
          title: '规格',
          width: 150,
          align: 'center'
        },
        {
          field: 'type',
          key: 'type',
          title: '类型',
          width: 120,
          align: 'center'
        },
        {
          field: 'origin',
          key: 'origin',
          title: '原产地',
          width: 100,
          align: 'center'
        },
        {
          field: 'unit',
          key: 'unit',
          title: '单位成本',
          width: 100,
          align: 'center'
        },
        {
          field: 'currency',
          key: 'currency',
          title: '货币',
          width: 100,
          align: 'center'
        },
        {
          field: 'quantity',
          key: 'quantity',
          title: '数量',
          width: 100,
          align: 'center'
        },
        {
          field: 'amount',
          key: 'amount',
          title: '金额',
          width: 100,
          align: 'center'
        },
        {
          field: 'remark',
          key: 'remark',
          title: '备注',
          width: 100,
          align: 'center'
        }
      ],
      tableData: [
        {
          id: 1,
          checked: false,
          name: '上盖面板',
          brand: '',
          specifications: '天鹏INR18650-25PG 3.6V',
          type: '电池-锂电子',
          origin: '中国',
          unit: '8.271',
          currency: '人民币',
          quantity: 10,
          amount: 82.71,
          remark: ''
        }
      ],
      rowStyleOption: {
        stripe: true,
        hoverHighlight: true
      },
      cellStyleOption: {
        bodyCellClass: ({ row, column, rowIndex }) => {
          return 'custom-cell'
        }
      }
    }
  },
  created() {
    this.keynum++
  },
  watch: {
    activeTab: {
      handler() {
        this.switchTable();
      }
    }
  },
  methods: {
    handleEdit(row) {
      console.log('编辑:', row)
      this.$message.success('编辑功能')
    },
    switchTable() {
      if (this.activeTab === 'plastic') {
        this.tableData = [
          {
            id: 1,
            checked: false,
            name: '上盖面板',
            brand: '',
            specifications: '天鹏INR18650-25PG 3.6V',
            type: '电池-锂电子',
            origin: '中国',
            unit: '8.271',
            currency: '人民币',
            quantity: 10,
            amount: 82.71,
            remark: ''
          }
        ]
      } else if (this.activeTab === 'metal') {
        this.tableData = [
          {
            id: 1,
            checked: false,
            name: '支架螺丝',
            brand: '',
            specifications: 'PB3.2*12',
            type: '',
            origin: '中国',
            unit: '0.041',
            currency: '人民币',
            quantity: 10,
            amount: 0.41,
            remark: ''
          }
        ]
      } else if (this.activeTab === 'electronic') {
        this.tableData = [
          {
            id: 1,
            checked: false,
            name: '电池',
            brand: '',
            specifications: '18650*2 2500mAh',
            type: '',
            origin: '中国',
            unit: '60',
            currency: '人民币',
            quantity: 10,
            amount: 600,
            remark: ''
          }
        ]
      } else if (this.activeTab === 'accessories') {
        this.tableData = [
          {
            id: 1,
            checked: false,
            name: '防震垫',
            brand: '',
            specifications: '',
            type: '',
            origin: '中国',
            unit: '0.6',
            currency: '人民币',
            quantity: 10,
            amount: 6,
            remark: ''
          }
        ]
      } else if (this.activeTab === 'packaging') {
        this.tableData = [
          {
            id: 1,
            checked: false,
            name: '纸盒',
            brand: '',
            specifications: '',
            type: '',
            origin: '中国',
            unit: '3.5',
            currency: '人民币',
            quantity: 10,
            amount: 35,
            remark: ''
          }
        ]
      } else if (this.activeTab === 'labor') {
        this.tableData = []
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.cost-analysis {
  padding: 20px;
  background: #f5f5f5;
  height: 100%;
}

.product-title {
  margin-bottom: 30px;

  h1 {
    font-size: 24px;
    font-weight: bold;
    color: #333;
    margin: 0;
  }
}

.cost-cards {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

.cost-card {
  background: white;
  border-radius: 8px;
  padding: 5px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-width: 180px;

  &.total-cost {
    text-align: center;
    ::v-deep .el-progress__text {
      white-space: pre-wrap;
    }
    .cost-circle {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background: linear-gradient(135deg, #1890ff, #40a9ff);
      color: white;
      margin: 0 auto;

      .cost-amount {
        font-size: 18px;
        font-weight: bold;
      }

      .cost-label {
        font-size: 12px;
        margin-top: 4px;
      }
    }
  }

  .cost-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;

    .cost-type {
      font-size: 14px;
      color: #666;
    }

    .download-btn {
      color: #ff4d4f;
      padding: 0;

      i {
        font-size: 16px;
      }
    }
  }

  .cost-value {
    font-size: 24px;
    font-weight: bold;
    color: #ff4d4f;
  }
}

.tabs-section {
  background: white;
  border-radius: 8px;
  padding: 0 20px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .cost-tabs {
    flex: 1;

    :deep(.el-tabs__header) {
      margin: 0;

      .el-tabs__nav-wrap {
        &::after {
          display: none;
        }
      }

      .el-tabs__item {
        padding: 0 20px;
        height: 50px;
        line-height: 50px;
        color: #666;

        &.is-active {
          color: #1890ff;
        }
      }

      .el-tabs__active-bar {
        background-color: #1890ff;
      }
    }
  }

  .edit-btn {
    color: #666;
    padding: 0;
    margin-left: 20px;
  }
}

.details-table {
  background: white;
  border-radius: 8px;
  padding: 20px;
  height: 100%;

  :deep(.ve-table) {
    .ve-table-header-tr {
      background: #fafafa;

      th {
        font-weight: 600;
        color: #333;
        border-right: 1px solid #e8e8e8;
        padding: 12px 8px;
      }
    }

    .ve-table-body-tr {
      &:hover {
        background: #f9f9f9;
      }

      td {
        border-right: 1px solid #e8e8e8;
        padding: 12px 8px;

        &.custom-cell {
          vertical-align: middle;
        }
      }
    }
  }
  ::v-deep .ve-table-container {
    height: 400px !important;
  }
}

.product-image-cell {
  img {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 4px;
    background: #f0f0f0;
  }
}
</style>
