<template>
  <div>
    <div class="filter-container" style="margin-bottom: 5px">
      <el-input
        v-model="strfilter"
        placeholder="请输入查询"
        style="width: 200px"
        class="filter-item"
        size="small"
      />
      <el-button
        class="filter-item"
        type="primary"
        size="small"
        @click="search(strfilter)"
      >
        查询
      </el-button>
      <div style="float: right">
        <el-radio-group v-model="goodsType" size="small" @change="getStatus()">
          <el-radio-button label="半成品" />
          <el-radio-button label="成品" />
        </el-radio-group>
      </div>
    </div>
    <div style="margin-bottom: 10px">
      <el-table
        ref="selectVal"
        v-loading="listLoading"
        :data="lst"
        element-loading-text="Loading"
        height="380px"
        border
        fit
        highlight-current-row
        style="overflow: auto"
        :header-cell-style="{
          background: '#F3F4F7',
          color: '#555',
          padding: '3px 0px 3px 0px',
        }"
        :cell-style="{ padding: '4px 0px 4px 0px' }"
        :row-class-name="rowIndex"
        @row-click="rowClick"
      >
        <!-- ---复选列--- -->
        <el-table-column v-if="multi == 1" type="selection" width="40" />
        <!-- ---单选列--- -->
        <el-table-column v-else label="" width="40" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-radio
              v-model="radio"
              :label="scope.$index"
              @change.native="getCurrentRow(scope.row)"
            >
              {{ " " }}</el-radio>
          </template>
        </el-table-column>
        <template v-for="(i, index) in tableForm.item">
          <el-table-column
            v-if="i.displaymark ? true : false"
            :key="index"
            :prop="i.itemcode"
            :column-key="i.itemcode"
            :label="i.itemname"
            :align="i.aligntype ? i.aligntype : 'center'"
            :min-width="i.minwidth"
            :show-overflow-tooltip="i.overflow ? true : false"
            :sortable="i.sortable ? true : false"
          >
            <template slot-scope="scope">
              <span v-if="i.itemcode == 'modifydate'">{{
                scope.row[i.itemcode] | dateFormat
              }}</span>
              <div v-else-if="i.itemcode == 'itemcount'">
                <el-popover
                  :ref="'popover-' + scope.$index"
                  placement="left"
                  trigger="click"
                  title="BOM明细"
                >
                  <div v-if="scope.$index==mypopoverIndex" style="position: relative; min-height: 100px">
                    <mypopover
                      ref="mypopover"
                      style="width: 860px"
                      :table-form="mypopoverTable"
                      :lst="mypopoverData"
                    />
                  </div>
                  <span
                    slot="reference"
                    class="textunderline"
                    @click="mypopoverIndex=scope.$index;getBillList($event, scope.row)"
                  >{{ scope.row[i.itemcode] }}</span>
                </el-popover>
              </div>

              <span v-else>{{ scope.row[i.itemcode] }}</span>
            </template>
          </el-table-column>
        </template>
      </el-table>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.PageNum"
      :limit.sync="queryParams.PageSize"
      @pagination="GetList"
    />
  </div>
</template>

<script>
import request from '@/utils/request'
import Pagination from '@/components/Pagination'
import { tableTh, tableItem } from './tablecolums.js'
import mypopover from '@/components/mypopover/index'
export default {
  components: {
    Pagination,
    mypopover
  },
  props: ['multi', 'groupid'],
  data() {
    return {
      listLoading: true,
      lst: [], //  列表数据
      strfilter: '', // 搜索框内输入的值
      total: 0,
      radio: '', // 单选用参数默认为0即可
      selrows: '', // 选择的内容 单选变量
      queryParams: {
        PageNum: 1,
        PageSize: 10,
        OrderType: 1,
        SearchType: 1
      },
      goodsType: '成品',
      goodsVal: 'p',
      tableForm: tableTh,
      mypopoverTable: tableItem,
      mypopoverData: [],
      mypopoverIndex: -1
    }
  },
  created() {
    this.searchstr = ''
    this.bindData()
    this.getColumn()
  },
  methods: {
    //  单选列方法 07/26修改
    getCurrentRow(row) {
      this.$forceUpdate()
      this.selrows = row
      this.$emit('singleSel') // 07/26修改
    },
    // 分页组件事件
    GetList(data) {
      this.queryParams.PageNum = data.page
      this.queryParams.PageSize = data.limit
      this.bindData()
    },
    // 加载列表
    bindData() {
      // 查询 goodsstate Bom-成品
      this.listLoading = true
      if (this.groupid) {
        var selObj = { groupid: this.groupid }
        if (this.queryParams.SearchPojo) {
          this.queryParams.SearchPojo = Object.assign(
            this.queryParams.SearchPojo,
            selObj
          )
        } else {
          this.queryParams.SearchPojo = selObj
        }
      }
      request
        .post(
          '/e08/E08M14B1/getBillList?state=' + this.goodsVal,
          JSON.stringify(this.queryParams)
        )
        .then((response) => {
          if (response.data.code == 200) {
            this.lst = response.data.data.list
            this.total = response.data.data.total
          }
          this.listLoading = false
        })
        .catch((error) => {
          this.listLoading = false
        })
    },
    getColumn() {
      request
        .get('/system/SYSM07B9/getBillEntityByCode?code=D91M02B1Th')
        .then((res) => {
          if (res.data.code == 200) {
            if (res.data.data == null) {
              this.tableForm = tableTh
              return
            }
            this.tableForm = res.data.data
          }
        })
        .catch((error) => {
          this.$message.error('请求出错')
        })
    },
    getBillList(e, row) {
      this.mypopoverData = row.item
      for (var i = 0; i < this.mypopoverData.length; i++) {
        var obj = this.mypopoverData[i]
        if (!obj.parentgoodsid) { // 判断是否为替代料
          obj.isType = '主料'
        } else {
          for (var j = 0; j < this.mypopoverData.length; j++) {
            if (obj.parentgoodsid = this.mypopoverData[j].goodsid) {
              obj.isType = '替代料'
            }
          }
        }
      }
    },
    // 查询
    search(res) {
      if (res != '') {
        this.queryParams.SearchPojo = { goodsname: res, goodsuid: res }
      } else {
        this.$delete(this.queryParams, 'SearchPojo')
      }
      this.queryParams.SearchType = 1
      this.queryParams.PageNum = 1
      this.bindData()
    },
    getStatus() {
      this.strfilter = ''
      this.$delete(this.queryParams, 'SearchPojo')
      if (this.goodsType == '成品') {
        this.goodsVal = 'p'
      } else if (this.goodsType == '半成品') {
        this.goodsVal = 's'
      }
      this.bindData()
    },
    rowIndex({ row, rowIndex }) {
      row.row_index = rowIndex
    },
    rowClick(row) {
      this.radio = row.row_index
      this.getCurrentRow(row)
    }
  }
}
</script>
<style lang="scss" scoped>
.textunderline {
  text-decoration: underline;
  cursor: pointer;
}
</style>
