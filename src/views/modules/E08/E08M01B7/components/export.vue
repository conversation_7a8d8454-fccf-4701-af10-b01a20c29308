<template>
  <div style="height: 100%">
    <div>
      <el-row>
        <el-button-group>
          <el-button
            type="primary"
            icon="el-icon-folder-add"
            size="mini"
            @click="showImport"
          >
            文件导入
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-brush"
            size="mini"
            :disabled="lst.length == 0"
            @click="checkGoodsUid"
          >
            识别编码
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-brush"
            size="mini"
            :disabled="lst.length == 0"
            @click="checkPartid"
          >
            识别外部编码
          </el-button>
          <!-- <el-button
            type="primary"
            icon="el-icon-brush"
            size="mini"
            @click="checkGoodsUid2"
            :disabled="lst.length == 0"
          >
            识别料号
          </el-button> -->
        </el-button-group>
      </el-row>
    </div>
    <div style="position: relative">
      <el-table
        ref="multipleTable"
        v-loading="listLoading"
        :data="lst"
        element-loading-text="Loading"
        border
        fit
        highlight-current-row
        size="small"
        class="tb-edit"
        style="overflow: auto"
        height="50vh"
        :header-cell-style="{
          background: '#F3F4F7',
          color: '#555',
          padding: '4px 0px 4px 0px',
        }"
        :cell-style="{ padding: '4px 0px' }"
        :row-style="{ height: '20px' }"
      >
        <!--   @selection-change="handleSelectionChange" -->
        <!-- <el-table-column type="selection" width="50" align="center" /> -->
        <el-table-column align="center" label="ID" min-width="50" fixed="left">
          <template slot-scope="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <!-- 修改项 修改列名  fixed="left"-->
        <el-table-column
          label="货品编码"
          align="center"
          width="100"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span>{{ scope.row.goodsuid }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="货品名称"
          align="center"
          width="100"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span>{{ scope.row.goodsname }}</span>
          </template>
        </el-table-column>

        <el-table-column
          label="货品规格"
          align="center"
          width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span>{{ scope.row.goodsspec }}</span>
          </template>
        </el-table-column>

        <el-table-column
          label="货品单位"
          align="center"
          width="100"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span>{{ scope.row.goodsunit }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="主件数量"
          align="center"
          width="100"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span>{{ scope.row.mainqty }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="配件数量"
          align="center"
          width="100"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span>{{ scope.row.subqty }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="损耗率%"
          align="center"
          width="100"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span>{{ scope.row.lossrate }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="配置属性"
          align="center"
          width="100"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span>{{ scope.row.attrcode }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="站位"
          align="center"
          width="110"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span>{{ scope.row.flowcode }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="备注"
          align="center"
          min-width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span>{{ scope.row.remark }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-dialog
      v-if="importVisble"
      title="文件导入"
      width="400px"
      :visible.sync="importVisble"
      append-to-body
    >
      <div>
        <el-upload
          ref="upload"
          class="upload-demo"
          :drag="true"
          action=""
          :multiple="false"
          :on-change="handleChange"
          :on-remove="handleRemove"
          :on-preview="handlePreview"
          :on-success="handleSuccess"
          :limit="limitUpload"
          :auto-upload="false"
          accept=".xlsx,.xls,.csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">上传文件</div>
          <div slot="tip" class="el-upload__tip">
            <p style="text-align: center">
              只能上传xlsx / xls文件<el-button
                type="text"
                @click.native="modelExport"
              >下载模板</el-button>
            </p>
          </div>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          size="small"
          @click="importf"
        >确 定</el-button>
        <el-button size="small" @click="importVisble = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import request from '@/utils/request'
import CRUD from '../CRUD.JS'
export default {
  data() {
    return {
      limitUpload: 1,
      fileTemp: {},
      importVisble: false,
      lst: [],
      listLoading: false,
      allowUpload: false
    }
  },
  watch: {
    lst: function(val, oldVal) {
      console.log('lst', val)
    }
  },
  created() {},
  methods: {
    // 识别编码
    async checkGoodsUid() {
      const that = this
      let newGoodsCounter = 0
      const promiseList = []
      for (var i = 0; i < this.lst.length; i++) {
        const promise = new Promise((resolve, reject) => {
          const _that = that
          const obj = {
            goodsname: that.lst[i].goodsname,
            goodsspec: that.lst[i].goodsspec,
            partid: that.lst[i].partid ? that.lst[i].partid : ''
          }
          const lstIndex = i
          CRUD.checkGoodsUid(obj)
            .then((res) => {
              if (res.data) {
                this.lst[lstIndex].goodsid = res.data.id
                this.lst[lstIndex].goodsbomid = res.data.bomid
                this.lst[lstIndex].goodsuid = res.data.goodsuid
                resolve(res.data)
              } else {
                newGoodsCounter++
                reject('查询错误')
              }
            })
            .catch((er) => {
              reject('查询失败')
            })
        })
        promiseList.push(promise)
      }
      await Promise.allSettled(promiseList)
        .then((res) => {
          console.log(res)
          // that.$message.success("识别编码成功");
          // this.allowUpload = true;
        })
        .catch((er) => {
          that.$message.warning(er)
        })
        .finally(() => {
          that.$message(
            `识别完毕,共${that.lst.length}条,需导入新货品${newGoodsCounter}条`
          )
          this.allowUpload = true
        })
    },
    // 识别编码
    async checkGoodsUid2() {
      const that = this
      let newGoodsCounter = 0
      const promiseList = []
      for (var i = 0; i < this.lst.length; i++) {
        const promise = new Promise((resolve, reject) => {
          const _that = that
          const lstIndex = i
          CRUD.checkGoodsUid2(that.lst[i].goodsuid)
            .then((res) => {
              console.log(res)
              if (res.data) {
                this.lst[lstIndex].goodsid = res.data.id
                this.lst[lstIndex].goodsname = res.data.goodsname
                this.lst[lstIndex].goodsuid = res.data.goodsuid
                this.lst[lstIndex].goodsspec = res.data.goodsspec
                this.lst[lstIndex].goodsunit = res.data.goodsunit
                this.lst[lstIndex].flowcode = res.data.flowcode
                resolve(res.data)
              } else {
                newGoodsCounter++
                reject('查询错误')
              }
            })
            .catch((er) => {
              reject('查询失败')
            })
        })
        promiseList.push(promise)
      }
      await Promise.allSettled(promiseList)
        .then((res) => {
          console.log('ssss', res)
        })
        .catch((er) => {
          that.$message.warning(er)
        })
        .finally(() => {
          that.$message(
            `识别料号完毕,共${that.lst.length}条,有${newGoodsCounter}条货品未识别`
          )
          this.allowUpload = true
        })
    },
    async checkPartid() {
      const that = this
      let newGoodsCounter = 0
      const promiseList = []
      for (var i = 0; i < that.lst.length; i++) {
        const promise = new Promise((resolve, reject) => {
          // 查询partid外部编码是否存在
          const goodsIndex = i
          CRUD.checkPartid(that.lst[i].partid)
            .then((res) => {
              if (res.data) {
                // 货品已在货品表内
                // that.lst[goodsIndex] = res.data;
                this.lst[goodsIndex].goodsid = res.data.id
                this.lst[goodsIndex].goodsuid = res.data.goodsuid
                this.lst[goodsIndex].partid = res.data.partid
                // this.lst[goodsIndex].flowcode = res.data.flowcode; //站位
                this.lst[goodsIndex].isNew = false
                resolve(res.data)
              } else {
                // 新货品不在货品表内
                newGoodsCounter++
                this.lst[goodsIndex].isNew = true
                reject('新货品不在货品表内')
              }
            })
            .catch((er) => {
              console.log(er)
              reject('查询失败')
            })
        })
        promiseList.push(promise)
      }
      await Promise.allSettled(promiseList)
        .then((res) => {
          console.log(res)
        })
        .catch((er) => {})
        .finally(() => {
          console.log('222')
          console.log(newGoodsCounter)
          that.$message(
            `识别完毕,共${that.lst.length}条,需导入新货品${newGoodsCounter}条`
          )
          this.allowUpload = true
        })
    },
    showImport() {
      this.importVisble = true
    },
    // 导出模板
    modelExport() {
      request
        .get('/e08/E08M14B1/exportModel', { responseType: 'blob' })
        .then((res) => {
          console.log(res)
          const link = document.createElement('a')
          const blob = new Blob([res.data], { type: 'application/vnd.ms-excel' })
          link.style.display = 'none'
          link.href = URL.createObjectURL(blob)
          link.download = 'BOM模板.xls'
          document.body.appendChild(link)
          // 模拟点击事件
          link.click()
        })
        .catch((err) => {
          console.log(err)
        })
    },
    // 添加文件
    handleChange(file, fileList) {
      this.fileTemp = file.raw
      const fileName = file.raw.name
      const fileType = fileName.substring(fileName.lastIndexOf('.') + 1)
      // 判断上传文件格式
      if (this.fileTemp) {
        if (fileType == 'xlsx' || fileType == 'xls') {
          // this.importf(this.fileTemp);
        } else {
          this.$message.warning('文件格式错误，请删除后重新上传！')
        }
      } else {
        this.$message.warning('请上传文件！')
      }
    },
    // 文件导入
    importf() {
      const _this = this
      const inputDOM = this.$refs.inputer
      var file = this.fileTemp
      var formData = new FormData()
      formData.append('file', file)
      request
        .post('/e08/E08M14B1/importExecl', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })
        .then((res) => {
          console.log('文件导入', res)
          if (res.data.code == 200) {
            this.$message.success('文件导入成功')
            // 关闭上传接口
            this.importVisble = false
            this.lst = res.data.data
          } else {
            this.$message.warning('文件导入失败，请重试')
          }
        })
    },
    // ==========通用====================
    // 设置table高度
    handlePreview(file) {
      console.log(file)
    },
    handleSuccess(file, fileList) {
      console.log('handleSuccess', file)
    },
    handleRemove(file, fileList) {
      console.log('handleRemove', file)
    }
  }
}
</script>
<style lang="scss" scoped>
.curssor {
  cursor: pointer;
}
.tableBox ::v-deep .el-input__inner{
  padding: 0 5px;
}

.tableBox ::v-deep  .el-date-editor .el-input__inner{
  padding: 0 30px;
}
/**空数据占位取消*/
::v-deep .el-table__empty-block {
  display: none;
}
/**表格默认下外框取消*/
::v-deep .el-table__fixed-right::before,
.el-table__fixed::before {
  bottom: none !important;
}
::v-deep .el-table::before {
  bottom: none;
}
::v-deep .el-table__fixed-right::before .el-table__fixed::before {
  bottom: none !important;
}
/**模拟table的边框 */
.table-container {
  border: 1px solid #ebeef5;
  overflow-x: auto;
  overflow-y: hidden;
}
/**此条控制表格横向滚动条 */
::v-deep .tableBox {
  position: absolute;
  width: 100%;
  max-width: none;
  // //以下这条控制取消显示滚动条
  overflow-y: hidden !important;
}
.table-position {
  position: relative;
  overflow: auto;
  height: 80%;
}
/**flex css  */
.flex {
  display: flex;
  flex-wrap: wrap;
}
.f-1 {
  flex: 1;
}

.f-d-c {
  flex-direction: column;
}

.j-c {
  justify-content: center;
}

.a-c {
  align-items: center;
}

.j-s {
  justify-content: space-between;
}

.j-start {
  justify-content: flex-start;
}
.j-end {
  justify-content: flex-end;
}
.p-r {
  position: relative;
}
.p-a {
  position: absolute;
}
//模拟table的边框
.table-container {
  border: 1px solid #ebeef5;
  overflow: auto;
}
::v-deep .el-table__footer-wrapper {
  display: none;
}

.toolButton {
  line-height: 1;
  font-size: 14px;
  color: #303133;
}
.form {
  margin-right: 20px;
  margin-left: 20px;
  width: calc(100% - 40px);
}
/**修改表单用 */
::v-deep .el-scrollbar__wrap {
  overflow-x: hidden;
}
::v-deep.custInfo .el-row {
  margin-bottom: -20px;
  display: flex;
  flex-wrap: wrap;
}
/**修改错误提示 */
::v-deep .el-form-item__error {
  top: 0;
  background: #fff;
  height: 25px;
  margin-top: 5px;
  margin-left: 5px;
  width: 120px;
  display: flex;
  align-items: center;
  // display: flex;
  // flex-wrap: wrap ;
}
/** */
.addMultiInfo {
  .el-row {
    margin-bottom: 10px;
  }
}
.dialog-body .el-form-item__error {
  top: 0% !important;
  left: 10px !important;
}
::v-deep .el-table__footer-wrapper {
  display: block;
}
::v-deep .el-table__footer-wrapper tbody td {
  color: #555555;
  cursor: pointer;
  font-weight: bold;
}
::v-deep .el-table__footer-wrapper tbody td:first-child {
  color: #555555;
  cursor: auto;
  font-weight: bold;
}
.droupList {
  position: absolute;
  top: 0px;
  right: -10px;
  z-index: 9;
}
</style>

