<template>
  <div style="width: 100%; box-sizing: border-box" class="component-container">
    <div
      style="top: 0px; width: 100%; z-index: 10"
      class="form_button flex a-c j-end p-r"
    >
      <div class="button-container p-a" style="top: 0px; right: 30px">
        <el-button
          v-preventReClick
          type="primary"
          size="small"
          :disabled="!!formdata.assessor"
          @click.native="submitForm('formdata')"
        >
          保 存</el-button>
        <div style="display: inline-block; margin: 0 10px">
          <el-button
            v-if="!formdata.assessor"
            size="small"
            type="primary"
            :disabled="!formdata.id"
            @click="approval()"
          >
            审 核</el-button>
          <el-button v-else size="small" type="primary" @click="DeApproval()">
            反审核</el-button>
        </div>
        <el-button
          :disabled="!formdata.id"
          type="primary"
          size="small"
          @click.native="$refs.PrintServer.printButton(0, 1)"
        >打 印</el-button>
        <el-dropdown
          trigger="click"
          placement="bottom"
          style="margin-left: 10px"
        >
          <el-button
            size="small"
          >操作<i class="el-icon-caret-bottom el-icon--right" /></el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              icon="el-icon-delete"
              :disabled="!formdata.id ? true : !!formdata.assessor"
              @click.native="deleteForm(idx)"
            >删 除</el-dropdown-item>

          </el-dropdown-menu>
        </el-dropdown>
        <el-dropdown trigger="click" placement="bottom" style="margin: 0 10px">
          <el-button
            size="small"
          >过程<i class="el-icon-caret-bottom el-icon--right" /></el-button>
          <el-dropdown-menu slot="dropdown" />
        </el-dropdown>
        <el-button size="small" @click.native="closeForm"> 关 闭</el-button>
      </div>
    </div>
    <!-- =======================form表单部分=================================== -->
    <div style="padding: 20px 20px 20px 20px">
      <!-- 表单容器 -->
      <div
        class="form-border form-container shandow form_info flex f-d-c"
        style="width: 100%"
        :style="{ height: formcontainHeight }"
      >
        <!-- ================头部======================= -->
        <div ref="form_main_info" class="form form-head p-r">
          <div class="flex j-end p-a" style="top: 5px; right: 0">
            <div class="refNo flex j-end">
              <div style="display: flex; justify-content: flex-end">
                <div style="margin-right: 10px; width: 100px">
                  <svg-icon
                    v-if="
                      formdata.finishcount > 0 &&
                        formdata.finishcount + formdata.disannulcount ==
                        formdata.itemcount
                    "
                    icon-class="finish1"
                    style="width: 80px; height: 80px"
                  />
                  <svg-icon
                    v-else-if="
                      formdata.disannulcount > 0 &&
                        formdata.disannulcount == formdata.itemcount
                    "
                    icon-class="revoke"
                    style="width: 80px; height: 80px"
                  />
                </div>
                <div style="margin-right: 10px; width: 90px">
                  <svg-icon
                    v-show="!!formdata.assessor"
                    icon-class="approve"
                    style="width: 80px; height: 80px"
                  />
                </div>
              </div>
            </div>
          </div>
          <!-- 修改项目 -->
          <el-form
            ref="formdata"
            :model="formdata"
            :label-width="formLabelWidth"
            class="custInfo"
            :rules="formRules"
            style="min-height: 110px"
          >
            <p class="formTitle">{{ title }}</p>
            <!-- 修改项目 -->
            <el-row :gutter="10">
              <el-col :span="5">
                <div @click="cleValidate('goodsid')">
                  <el-form-item label="货品编码" prop="goodsid">

                    <el-popover
                      v-model="selVisible"
                      placement="bottom-start"
                      trigger="click"
                      @show="$refs.selGoods.bindData()"
                    >
                      <selGoods
                        ref="selGoods"
                        :multi="multi"
                        :goodsstate="'成品'"
                        style="width: 720px; height: 460px"
                        @singleSel="selGoods"
                      />
                      <div slot="reference">
                        <el-input
                          v-model="formdata.goodsuid"
                          placeholder="请选择货品编码"
                          size="small"
                          readonly
                          style="min-width: 140px"
                          @change="handleBlur"
                        >
                          <i
                            slot="suffix"
                            class="el-icon-circle-plus-outline"
                            style="font-size: 15px; color: rgb(64, 158, 255)"
                          />
                        </el-input>
                      </div>
                    </el-popover>
                  </el-form-item>
                </div>
              </el-col>
              <el-col :span="5">
                <div @click="cleValidate('goodsname')">
                  <el-form-item label="货品名称" prop="goodsname">
                    <el-input
                      v-model="formdata.goodsname"
                      placeholder="请输入货品名称"
                      clearable
                      :readonly="true"
                      size="small"
                    />
                  </el-form-item>
                </div>
              </el-col>
              <el-col :span="5">
                <div @click="cleValidate('goodsspec')">
                  <el-form-item label="规格描述" prop="goodsspec">
                    <el-input
                      v-model="formdata.goodsspec"
                      placeholder="请输入规格描述"
                      clearable
                      :readonly="true"
                      size="small"
                    />
                  </el-form-item>
                </div>
              </el-col>
              <el-col :span="2">
                <div @click="cleValidate('goodsunit')">
                  <el-form-item label="单位" prop="goodsunit">
                    <el-input
                      v-model="formdata.goodsunit"
                      placeholder="单位"
                      clearable
                      :readonly="true"
                      size="small"
                    />
                  </el-form-item>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <!-- <el-col :span="5">
                <div @click="cleValidate('bomtype')">
                <el-form-item label="组件类别" prop="bomtype">
                  <el-select
                    v-model="formdata.bomtype"
                    placeholder="请选择组件类别"
                    style="width: 100%"
                    size="small"
                  >
                    <el-option
                      v-for="item in options"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                      @focus="setMinWidthEmpty"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
                </div>
              </el-col> -->
              <el-col :span="5">
                <div @click="cleValidate('mintime')">
                  <el-form-item label="最小用时" prop="mintime">
                    <el-input
                      v-model="formdata.mintime"
                      placeholder="请输入最小用时"
                      clearable
                      size="small"
                    />
                  </el-form-item>
                </div>
              </el-col>
              <el-col :span="5">
                <div @click="cleValidate('progroupid')">
                  <el-form-item label="工艺线路" prop="progroupid">
                    <el-select
                      v-model="formdata.progroupid"
                      placeholder="请选择工艺线路"
                      style="width: 100%"
                      size="small"
                      clearable
                      @clear="formdata.progroupid = ''"
                    >
                      <el-option
                        v-for="item in wipGroupData"
                        :key="item.id"
                        :label="item.groupname"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                </div>
              </el-col>
              <el-col :span="2">
                <el-form-item>
                  <el-checkbox
                    v-model="formdata.enabledmark"
                    label="有效"
                    :true-label="1"
                    :false-label="0"
                    size="mini"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <el-divider />
        </div>
        <!-- ==========================表身================================= -->
        <div class="form-body form f-1">
          <alitem
            ref="alitem"
            :lstitem="formdata.item"
            :formdata="formdata"
            :idx="idx"
            style="width: 99%"
            @bindData="bindData"
          />
        </div>
        <!-- ==================================表尾================================= -->

        <el-form :label-width="formLabelWidth" class="footFormContent">
          <el-row style="margin-top: 15px; margin-right: 20px">
            <el-col :span="24">
              <el-form-item
                label="摘  要"
                label-position="right"
                label-width="100px"
              >
                <el-input
                  v-model="formdata.summary"
                  placeholder="请输入摘要"
                  clearable
                  size="small"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="4">
              <el-form-item label="创建人">
                <span v-show="formdata.createby" class="el-form-item__label">{{
                  formdata.createby
                }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="创建日期">
                <span
                  v-show="formdata.createdate"
                  class="el-form-item__label"
                >{{ formdata.createdate | dateFormats }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="制表">
                <span v-show="formdata.lister" class="el-form-item__label">{{
                  formdata.lister
                }}</span>
              </el-form-item>
            </el-col>

            <el-col :span="4">
              <el-form-item label="修改日期">
                <span
                  v-show="formdata.createdate"
                  class="el-form-item__label"
                >{{ formdata.modifydate | dateFormats }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="审核">
                <span class="el-form-item__label">{{ formdata.assessor }}</span>
              </el-form-item>
            </el-col>
            <el-col v-show="formdata.assessor" :span="4">
              <el-form-item label="审核日期">
                <span class="el-form-item__label">{{
                  formdata.assessdate | dateFormats
                }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
    <!-- ===========================组件============================ -->
    <PrintServer
      ref="PrintServer"
      :formdata="formdata"
      :printcode="'D91M02B1Edit'"
      :commonurl="'/e08/E08M14B1/printBillByLayer'"
      :weburl="'/e08/E08M14B1/printWebBillByLayer'"
    />

  </div>
</template>
<script>
import request from '@/utils/request'
import CRUD from '../CRUD.JS'
import alitem from './alitem'
import selGoods from '@/views/modules/E08/E08M01B8/components/selectdetail.vue'
export default {
  name: 'Formedit',
  components: {
    alitem,
    selGoods

  },
  props: ['idx', 'goodsInfo'],
  data() {
    return {
      title: '多层BOM',
      // 表单信息
      formdata: {
        // swagger
        assessdate: new Date(),
        assessor: '',
        bomtype: '', // 物料类别
        createby: JSON.parse(window.localStorage.getItem('getInfo')).realname,
        deletelister: '',
        deletemark: 0,
        enabledmark: 1,
        goodsid: this.goodsInfo ? this.goodsInfo.goodsid : '',
        goodsname: this.goodsInfo ? this.goodsInfo.goodsname : '',
        goodsspec: this.goodsInfo ? this.goodsInfo.goodsspec : '',
        goodsuid: this.goodsInfo ? this.goodsInfo.goodsuid : '',
        goodsunit: this.goodsInfo ? this.goodsInfo.goodsunit : '',
        item: [],
        lister: JSON.parse(window.localStorage.getItem('getInfo')).realname, // 制表
        mintime: 0, // 最小用时
        modifydate: new Date(),
        progroupid: '', // 工艺线路
        quotahour: 0, // 定额工时
        summary: '', // 备注
        versionnum: '' // 版本号
      },
      formRules: {
        // 复杂验证模式
        goodsid: [
          { required: true, trigger: 'blur', message: '货品为必填项' } // change触发多用在选择组件
        ],
        bomtype: [
          { required: true, trigger: 'blur', message: '请选择组件类别' }
        ]
      },
      multi: 0, // 单复选模式
      selVisible: false, // 下拉选择框的显示
      formLabelWidth: '100px',

      // 选择组件数据
      options: [
        {
          value: '厂制',
          label: '厂制'
        },
        {
          value: '委制',
          label: '委制'
        },
        {
          value: '外购',
          label: '外购'
        },
        {
          value: '客供',
          label: '客供'
        }
      ],
      // 生辰制程
      wipGroupData: []
    }
  },
  computed: {
    /** form表头 表身 表尾所在容器的高度 */
    formcontainHeight() {
      return window.innerHeight - 50 - 23 - 50 + 'px'
    }
  },
  watch: {
    idx: function(val, oldVal) {
      this.bindData()
    }
  },
  mounted() {
    this.bindData()
    this.wipGroupBtn()
  },
  methods: {
    bindData() {
      this.$refs.alitem.getColumn()
      if (this.idx != 0) {
        this.listLoading = true
        request
          .get(`/E08M14B1/getBillEntityByLayer?key=${this.idx}`)
          .then((response) => {
            if (response.data.code == 200) {
              this.formdata = response.data.data
            }
            this.listLoading = false
          })
          .catch((error) => {
            this.listLoading = false
            this.$message.error('请求错误')
          })
      }
    },
    wipGroupBtn() {
      var queryParam = {
        PageNum: 1,
        PageSize: 500,
        OrderType: 1,
        SearchType: 1
      }
      request
        .post('/manu/D05M21S2/getPageTh', JSON.stringify(queryParam))
        .then((response) => {
          if (response.data.code == 200) {
            this.wipGroupVisible = true
            this.wipGroupData = response.data.data.list
            if (this.wipGroupData.length != 0) {
              // this.wipGroupModel = this.wipGroupData[0].id;
            }
          } else {
            this.$message.warning(response.data.msg | '获取工艺线路失败')
          }
        })
    },
    submitForm(formdata) {
      this.$refs[formdata].validate((valid) => {
        if (valid) {
          this.saveForm()
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    saveForm() {
      if (this.$refs.alitem.lst.length == 0) {
        this.$message.warning('单据内容不能为空')
        return
      }
      // 创建时 执行 识别货品编码判断
      if (!this.idx) {
        if (!this.$refs.alitem.allowUpload) {
          this.$message.warning('请先识别货品编码')
          return
        }
      }

      this.formdata.item = this.$refs.alitem.lst
      for (var i = 0; i < this.formdata.item.length; i++) {
        var Item = this.formdata.item[i]
        if (Item.mainqty == 0 || Item.subqty == 0) {
          this.$message.warning('配件数量和主件数量不能为0')
          return
        }
      }
      if (this.idx == 0 || this.idx == undefined) {
        request
          .post(
            '/e08/E08M14B1/createBomByLayer',
            JSON.stringify(this.formdata)
          )
          .then((res) => {
            if (res.data.code == 200) {
              this.$message.success('保存成功')
              this.$emit('changeIdx', res.data.data.id)
              this.$emit('checkRowItem', res.data.data)
              this.$emit('bindData')
              this.bindData()
            } else {
              this.$message.warning(res.data.msg || '保存失败')
            }
          })
          .catch((er) => {
            this.$message.error(er || '保存失败')
          })
      } else {
        request
          .post(
            '/e08/E08M14B1/updateBomByLayer',
            JSON.stringify(this.formdata)
          )
          .then((res) => {
            if (res.data.code == 200) {
              this.$message.success('保存成功')
              this.$emit('bindData')
              this.$emit('checkRowItem', res.data.data)
              this.bindData()
            } else {
              this.$message.warning(res.data.msg || '保存失败')
            }
          })
          .catch((er) => {
            this.$message.error(er || '保存失败')
          })
      }
    },
    // 关闭formedit对话框
    closeForm() {
      this.$emit('closeForm')
    },
    // 删除该工单 先询问再删除
    deleteForm(idx) {
      this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$microClose(800)
          CRUD.delete(idx)
            .then(() => {
              this.$message.success('删除成功')
              this.$emit('compForm')
            })
            .catch(() => {
              this.$message.warning('删除失败')
            })
        })
        .catch(() => {})
    },
    // 审核按键
    approval() {
      this.approvalRequest(this.formdata.id)
    },
    // 清空表单错误提示
    cleValidate(val) {
      this.$refs.formdata.clearValidate(val)
    },
    // 反审核
    DeApproval() {
      request
        // 修改项
        .get('/e08/E08M14B1/approval?key=' + this.formdata.id)
        .then((res) => {
          if (res.data.code == 200) {
            this.$message.success('反审核成功')
            this.formdata = res.data.data
          } else {
            this.$message.warning('反审核失败')
          }
        })
    },
    // 调用审核
    async approvalRequest(val) {
      await request
        // 修改项
        .get('/e08/E08M14B1/approval?key=' + this.formdata.id)
        .then((res) => {
          if (res.data.code == 200) {
            this.$message.success('审核成功')
            this.formdata = res.data.data
          } else {
            this.$message.warning('审核失败')
          }
        })
    },
    // 选择一件货品
    selGoods(val) {
      this.formdata.goodsid = val.id // 货品
      this.formdata.itemcode = val.goodsuid // 货品
      this.formdata.itemname = val.goodsname // 货品
      this.formdata.itemspec = val.goodsspec // 货品
      this.formdata.itemunit = val.goodsunit // 货品
      this.cleValidate('goodsid')
      this.$forceUpdate()
    },
    clearGoods() {
      this.formdata.goodsid = '' // 货品
      this.formdata.itemcode = '' // 货品
      this.formdata.itemname = '' // 货品
      this.formdata.itemspec = '' // 货品
      this.formdata.itemunit = '' // 货品
      this.cleValidate('goodsid')
      this.$forceUpdate()
    },

    changeIdx(val) {
      this.idx = val
    },
    // ===================================自定义项==============================================
    // 控制显示选择框
    handleFocus() {
      this.selVisible = true
    },
    handleBlur() {
      this.selVisible = false
    },
    // 下拉选择框事件 可以写在下拉框的@change 如果有联动数据
    selectFuction(data) {
      // data为当前选择的数据
      console.log(data)
      // 在选项中遍历出对应的数据 再赋值给formdata
      const res = this.options.find((v) => v.functionname == data)
      this.formdata.functionid = res.functionid
      this.formdata.functioncode = res.functioncode
      console.log(res)
      // 清空提示
      this.cleValidate('functionname')
      // 选择后赋值
      // this.formdata.functionid
    }
  }
}
</script>
<style  scoped  lang="scss">
//灰色背景
$bg-gray: #e2e2e2;
$bg-component: #f5f5f5;

::v-deep .el-scrollbar__wrap {
  overflow-x: hidden;
}
::v-deep .is-horizontal {
  display: none;
}
::v-deep.custInfo .el-row {
  margin-bottom: -20px;
  display: flex;
  flex-wrap: wrap;
}
/**修改错误提示 */
::v-deep .el-form-item__error {
  top: 1px;
  background: #fff;
  height: 28px;
  margin-top: 5px;
  margin-left: 5px;
  width: 120px;
  display: flex;
  align-items: center;
  // display: flex;
  // flex-wrap: wrap ;
}
// ::v-depp .el-input__inner{

// }
/**美化用  外框阴影*/
.shandow {
  box-shadow: 0px 0px 10px $bg-gray;
}
/**位置css */
.p-r {
  position: relative;
}
.p-a {
  position: absolute;
}
.p-f {
  position: fixed;
}
/**flex css  */
.flex {
  display: flex;
  flex-wrap: wrap;
}
.f-1 {
  flex: 1;
}

.f-d-c {
  flex-direction: column;
}

.j-c {
  justify-content: center;
}

.a-c {
  align-items: center;
}

.j-s {
  justify-content: space-between;
}

.j-start {
  justify-content: flex-start;
}
.j-end {
  justify-content: flex-end;
}
//顶部工具栏
.form_button {
  .button-container {
    background: $bg-gray;
    padding: 10px;
  }
}
//表格边框
.form-border {
  border: 2px solid #dbdbdb;
}
.component-container {
  background: $bg-component;
  height: calc(100vh - 84px);
  .form-container {
    background: #fff;
  }
  .form {
    margin-right: 20px;
    margin-left: 20px;
    width: calc(100% - 40px);
  }
  .form-head {
    //工具栏上下padding 加按键高度 -容器的上padding
    margin-top: calc(10px + 32px - 20px);
    .refNo {
      // margin-right: 30px;
      // width: 300px;
      color: #595959;
      font-size: 14px;
      word-break: break-all;
      line-height: 24px;
      flex-direction: column;
      text-align: left;
      margin-top: 10px;
      .refNo-item {
        z-index: 99;
        display: flex;
        align-items: center;
        margin-bottom: 10px;
      }
    }
  }
}
/**更改 表单label的字体格式 */
::v-deep .el-form-item__label {
  font-size: 12px;
  font-weight: 700;
}
.el-form-item {
  min-width: 180px !important;
}
.formTitle {
  color: #555;
  font-size: 20px;
  margin-bottom: 10px;
  margin-top: 0px;
  margin-left: 20px;
}
</style>
