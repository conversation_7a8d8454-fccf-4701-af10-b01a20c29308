<template>
  <div class="flex f-d-c form" ref="elitem" style="height: 100%">
    <div style="display: flex; justify-content: space-between">
      <el-row>
        <el-button-group>
          <el-button
            type="primary"
            size="mini"
            :disabled="!!formdata.assessor"
            @click.native="getselPwProcess(1)"
            ><i class="el-icon-circle-plus-outline" /> 添 加</el-button
          >
          <el-button
            type="primary"
            size="mini"
            @click="btnImport()"
            :disabled="!!formdata.assessor"
            ><i class="el-icon-download" /> 导 入</el-button
          >
          <el-button
            :disabled="multipleSelection.length != 1"
            type="primary"
            size="mini"
            @click.native="getMoveUp()"
            ><i class="el-icon-top" /> 上 移</el-button
          >
          <el-button
            :disabled="multipleSelection.length != 1"
            type="primary"
            size="mini"
            @click.native="getMoveDown()"
            ><i class="el-icon-bottom" /> 下 移</el-button
          >
          <el-button
            :disabled="multipleSelection.length == 0"
            type="danger"
            size="mini"
            @click.native="delItem()"
            ><i class="el-icon-delete" /> 删 除</el-button
          >
          <el-button
            :disabled="!!formdata.assessor"
            type="primary"
            size="mini"
            @click.native="copyBomVisible = true"
            ><i class="el-icon-document-copy" /> 克 隆</el-button
          >
        </el-button-group>
      </el-row>
      <div style="margin-right: 10px; position: relative">
        <el-button
          size="mini"
          icon="el-icon-refresh-right"
          style="font-weight: bold"
          @click.native="$emit('bindData')"
        ></el-button>
        <el-button
          size="mini"
          icon="el-icon-s-tools"
          @click="openDialog()"
        ></el-button>
        <el-button
          size="mini"
          icon="el-icon-download"
          @click="btnExport"
        ></el-button>
      </div>
    </div>
    <div class="table-container f-1 table-position" style="width: 100%">
      <ve-table
        ref="multipleTable"
        rowKeyFieldName="rowKeys"
        :max-height="tableHeight"
        :scroll-width="tableMinWidth"
        :style="{ 'word-break': 'break-all' }"
        is-horizontal-resize
        :fixed-header="true"
        :columns="columsData"
        :table-data="lst"
        :columnHiddenOption="{ defaultHiddenColumnKeys: columnHidden }"
        border-x
        border-y
        :border-around="true"
        :checkbox-option="checkboxOption"
        :column-width-resize-option="columnWidthResizeOption"
        :editOption="editOption"
        :virtual-scroll-option="virtualScrollOption"
        :footer-data="footerData"
        :cell-autofill-option="cellAutofillOption"
        :contextmenu-body-option="contextmenuBodyOption"
        :clipboard-option="clipboardOption"
      />
    </div>
    <!-- 选择商品对话框 -->
    <el-dialog
      v-if="selGoodsFormVisble"
      title="货品信息"
      :append-to-body="true"
      :visible.sync="selGoodsFormVisble"
      width="60vw"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <selPwProcess
        ref="selPwProcess"
        :multi="multi"
        :goodsid="formdata.goodsid"
        :lstData="lst"
      />
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click.native="selPwProcess()"
          >确 定</el-button
        >
        <el-button @click="selGoodsFormVisble = false">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="货品导入"
      v-if="ExportVisible"
      :visible.sync="ExportVisible"
      :close-on-click-modal="false"
      width="68vw"
      top="8vh"
    >
      <div>
        <Export ref="exportFile" @closeDialog="closeDialog" />
      </div>
      <div slot="footer">
        <el-button type="primary" @click="submitExPort()">确 定</el-button>
        <el-button @click="ExportVisible = false">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-if="copyBomVisible"
      title="BOM克隆"
      :append-to-body="true"
      :visible.sync="copyBomVisible"
      width="60vw"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <copyBom ref="copyBom" :multi="0" :goodsid="formdata.goodsid" />
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click.native="copyRow()">确 定</el-button>
        <el-button @click="copyBomVisible = false">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      width="800px"
      title="列设置"
      v-if="setColumsVisible"
      :append-to-body="true"
      :visible.sync="setColumsVisible"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      top="5vh"
    >
      <Setcolums
        ref="setcolums"
        :code="'D91M02B1Item'"
        :tableForm="tableForm"
        @bindData="$emit('bindData')"
        @closeDialog="setColumsVisible = false"
      ></Setcolums>
    </el-dialog>
  </div>
</template>
<script>
import selPwProcess from "@/views/modules/E08/E08M01B8/components/selectBom.vue";
import NP from "number-precision";
import Export from "./export.vue";
import copyBom from "./select.vue";
import { export_json_to_excel } from "@/components/Excel/Export2Excel.js";
import { tableItem } from "./tablecolums.js";
import Setcolums from "@/components/Setcolums";
import request from "@/utils/request";
import goodsInfo from "@/components/goodsInfo";
export default {
  name: "Elitem",
  components: {
    selPwProcess,
    Export,
    Setcolums,
    copyBom,
    goodsInfo,
  },
  props: ["formdata", "lstitem", "idx"],
  data() {
    return {
      listLoading: false, // 列表加载中
      selGoodsFormVisble: false, // 选择商品对话框显示
      lst: [],
      multi: 0,
      billamount: 0, //
      // =============================
      selected: false,
      tableHeight: 0,
      multipleSelection: [],
      isEditOk: true,
      ExportVisible: false,
      //
      tableForm: tableItem,
      setColumsVisible: false,
      copyBomVisible: false,
      countfiles: ["subqty", "mainqty", "lossrate"],
      //站位数组
      flowcodeData: [],
      //=====================================
      columsData: [],
      // 行样式配置
      rowStyleOption: {
        clickHighlight: false, //是否开启行click 背景高亮
        hoverHighlight: false, //是否开启行hover 背景高亮
        stripe: false, //是否开启斑马纹
      },
      columnHidden: [], //列隐藏
      //行多选配置
      checkboxOption: {
        // 多选
        selectedRowChange: ({ row, isSelected, selectedRowKeys }) => {
          //selectedRowKeys == 时间-rownum
          if (selectedRowKeys.length != 0) {
            this.multipleSelection = [];
            for (var i = 0; i < selectedRowKeys.length; i++) {
              //根据 RowKeys 获取row位置
              var index = this.lst.findIndex(
                (item) => item.rowKeys == selectedRowKeys[i]
              );
              if (index != -1) {
                this.multipleSelection.push(this.lst[index]);
              }
            }
          } else {
            this.multipleSelection = [];
          }
        },
        // 全选
        selectedAllChange: ({ isSelected, selectedRowKeys }) => {
          if (isSelected) {
            this.multipleSelection = JSON.parse(JSON.stringify(this.lst));
          } else {
            this.multipleSelection = [];
          }
        },
      },
      // 列宽拉伸
      columnWidthResizeOption: {
        enable: true,
        minWidth: 10,
        sizeChange: ({ column, differWidth, columnWidth }) => {
          this.columnResizeInfo.column = column;
          this.columnResizeInfo.differWidth = differWidth;
          this.columnResizeInfo.columnWidth = columnWidth;
        },
      },
      columnResizeInfo: {
        column: "",
        differWidth: "",
        columnWidth: "",
      },
      // 单元格编辑
      editOption: {
        // cell value change
        cellValueChange: ({ row, column }) => {
          if (
            column.field == "goodsname" ||
            column.field == "goodsspec" ||
            column.field == "goodsunit" ||
            column.field == "goodsuid" ||
            column.field == "subcount" ||
            column.field == "isType" ||
            column.field == "operation" ||
            column.field == "partid"
          ) {
            //this.$event.preventDefault();
          } else if (column.field == "machuid") {
            this.inputSale(row.rownum);
          } else {
            this.changeInput("", row, column.field);
            // this.setAttributeJson(row, row.rownum);
          }
        },
      },
      // 虚拟滚动
      rowScroll: 0,
      virtualScrollOption: {
        enable: true,
        scrolling: ({ startRowIndex }) => {
          this.rowScroll = startRowIndex;
        },
      },
      // 底部合计行
      footerData: [],
      // 下拉填充
      cellAutofillOption: {
        directionX: false,
        directionY: true,
        beforeAutofill: ({ sourceSelectionData }) => {
          // 关闭下拉填充
          var key = Object.keys(sourceSelectionData[0])[1];
          if (
            key == "goodsname" ||
            key == "goodsspec" ||
            key == "goodsunit" ||
            key == "goodsuid" ||
            key == "groupuid" ||
            key == "subcount" ||
            key == "isType" ||
            key == "operation" ||
            key == "partid"
          ) {
            return false;
          }
        },
        afterAutofill: ({
          sourceSelectionData, //源数据
          targetSelectionData,
        }) => {
          for (var i = 0; i < targetSelectionData.length; i++) {
            var Item = targetSelectionData[i];
            // var num=Item.rowKeys.split('-')[1]
            var index = this.lst.findIndex(
              (item) => item.rowKeys == Item.rowKeys
            );
            if (index != -1) {
              this.changeInput("", this.lst[index], Object.keys(Item)[1]);
              // this.setAttributeJson(this.lst[index], index);
            }
          }
        },
      },
      // 右键菜单
      contextmenuBodyOption: {
        beforeShow: (isWholeRowSelection) => {
          if (!this.copyText) {
            this.contextmenuBodyOption.contextmenus[1].disabled = true;
          } else {
            this.contextmenuBodyOption.contextmenus[1].disabled = false;
          }
          // 添加新选项菜单
          var key = isWholeRowSelection.selectionRangeKeys.startColKey;
          this.addNewMenu(key);
        },
        afterMenuClick: ({
          type,
          selectionRangeKeys,
          selectionRangeIndexes,
        }) => {
          var key = selectionRangeKeys.startColKey;
          var indexLst = selectionRangeIndexes.startRowIndex;
          this.$nextTick(() => {
            if (type == "COPY") {
            } else if (type == "PASTE") {
              if (!!this.copyText) {
                this.lst[indexLst][key] = this.copyText;
              } else {
                this.lst[indexLst][key] = "";
              }
            } else if (type == "EMPTY_CELL") {
            } else {
              // console.log("type", type);
              this.lst[indexLst][key] = type;
              //spu 选择
              // var index = this.customList.findIndex((a) => a.attrkey == key);
              // if (index != -1) {
              //   this.lst[indexLst][key] = type;
              //   // this.setAttributeJson(this.lst[indexLst], indexLst);
              // }
            }
          });
        },
        contextmenus: [
          {
            label: "复制",
            type: "COPY",
          },
          {
            label: "粘贴",
            type: "PASTE",
          },
          {
            type: "SEPARATOR",
          },
          {
            label: "清空单元格",
            type: "EMPTY_CELL",
          },
        ],
      },
      // 剪贴板
      clipboardOption: {
        copy: true,
        paste: true,
        cut: false,
        delete: false,
        afterCopy: ({ data, selectionRangeIndexes, selectionRangeKeys }) => {
          this.copyText = data;
        },
        afterPaste: ({ data, selectionRangeIndexes, selectionRangeKeys }) => {
          var indexLst = selectionRangeIndexes.startRowIndex;
          this.pasteFun(data, indexLst);
        },
      },
      copyText: "", //复制的内容
    };
  },
  computed: {
    tableMinWidth() {
      var tableWidth = "100%";
      if (this.tableForm.item.length != 0) {
        tableWidth = 0;
        for (var i = 0; i < this.tableForm.item.length; i++) {
          var Item = this.tableForm.item[i];
          if (Item.displaymark) {
            tableWidth += Number(Item.minwidth);
          }
        }
      }
      return tableWidth;
    },
  },
  watch: {
    lstitem: function (val, oldVal) {
      this.lst = this.lstitem;
      for (var i = 0; i < this.lst.length; i++) {
        // 判断 替代料 已弃用-操作在getColumn()中创建
        // if (!this.lst[i].parentgoodsid) {
        //   //判断是否为替代料
        //   this.lst[i].isType = "主料";
        // } else {
        //   for (var j = 0; j < this.lst.length; j++) {
        //     if (this.lst[i].parentgoodsid == this.lst[j].goodsid) {
        //       this.lst[i].isType = "替代料";
        //     }
        //   }
        // }
      }
    },
    "formdata.assessor": function (val, oldVal) {
      if (!this.formdata.assessor) {
        this.isEditOk = true;
      } else {
        this.isEditOk = false;
      }
      this.initTable(this.tableForm);
    },
    lst: function (val, oldVal) {
      if (val == undefined) {
        this.lst = [];
      }
      for (var i = 0; i < val.length; i++) {
        if (!val[i].rowKeys) {
          val[i].rowKeys = new Date().getTime() + "-" + i;
        }
        val[i].rownum = i;
      }
    },
  },
  created() {
    this.lst = [];
    this.getflowcodeData();
  },
  mounted() {
    this.catchHight();
    this.getColumn();
  },
  methods: {
    addNewMenu(key) {
      var obj = {
        label: "配置属性",
        type: "choice",
      };
      if (key == "attrcode") {
        var spuArr = ["厂制", "委制", "外购", "客供"];
        obj.children = [];
        for (var i = 0; i < spuArr.length; i++) {
          var item = spuArr[i];
          var objItem = { label: item, type: item };
          obj.children.push(objItem);
        }
      } else {
        obj.disabled = true;
      }
      this.contextmenuBodyOption.contextmenus[4] = obj;
    },
    getColumn() {
      request
        .get("/system/SYSM07B9/getBillEntityByCode?code=D91M02B1Item")
        .then((res) => {
          if (res.data.code == 200) {
            if (res.data.data == null) {
              this.tableForm = tableItem;
              this.initTable(this.tableForm);
              return;
            }
            this.tableForm = res.data.data;
            this.initTable(this.tableForm);
          }
        })
        .catch((error) => {
          this.$message.error("请求出错");
        });
    },
    initTable(data) {
      if (!this.formdata.assessor) {
        this.isEditOk = true;
      } else {
        this.isEditOk = false;
      }
      var customData = [];
      this.columnHidden = [];

      data.item.forEach((item, index) => {
        var obj = {
          field: item.itemcode,
          key: item.itemcode,
          title: item.itemname,
          width: !isNaN(item.minwidth) ? item.minwidth + "px" : item.minwidth,
          displaymark: item.displaymark,
          fixed: item.fixed ? (item.fixed == 1 ? "left" : "right") : false,
          ellipsis: item.overflow ? { showTitle: true } : false,
          align: item.aligntype ? item.aligntype : "center",
          sortBy: item.sortable ? "asc" : false,
          edit: this.isEditOk ? (item.editmark ? true : false) : false,
          resize: true,
          renderBodyCell: ({ row, column, rowIndex }, h) => {
            if (item.itemcode == "goodsuid") {
              var html = (
                <goodsInfo
                  scopeIndex={row.rownum}
                  scopeVal={row[item.itemcode]}
                ></goodsInfo>
              );
              return html;
            } else if (item.itemcode == "isType") {
              var html = <span>{row.isType ? row.isType : "主料"}</span>;
              return html;
            } else if (item.itemcode == "flowcode") {
              var html = "";
              html = (
                <div style="display: flex;justify-content: space-between;align-items: center;padding:0 4px">
                  <div style="flex:1">
                    <span class={row.disannulmark ? "textlinethrough" : ""}>
                      {row[item.itemcode]}
                    </span>
                  </div>
                  <div v-show={this.flowcodeData.length}>
                    <div style="width:0;height:0">
                      <el-select
                        class="wpselect"
                        style="width:0;height:0;opacity: 0;"
                        size="small"
                        id={item.itemcode + row.rownum + tableItem.formcode}
                        v-model={row[item.itemcode]}
                        on-change={(event) => {
                          row[item.itemcode] = event;
                        }}
                      >
                        {this.flowcodeData.map((n, index) => {
                          return (
                            <el-option
                              key={index}
                              label={n.dictvalue}
                              value={n.dictvalue}
                              style="max-width:200px"
                            ></el-option>
                          );
                        })}
                      </el-select>
                    </div>
                    <i
                      v-show={!this.formdata.assessor}
                      class="writePacksn el-icon-arrow-down"
                      style={"font-weight:bold;font-size:14px;"}
                      on-click={() => {
                        document
                          .getElementById(
                            item.itemcode + row.rownum + tableItem.formcode
                          )
                          .click();
                      }}
                    />
                  </div>
                </div>
              );
              return html;
            } else {
              return row[item.itemcode];
            }
          },
        };
        // 判断显示隐藏
        if (!item.displaymark) {
          this.columnHidden.push(item.itemcode);
        }
        customData.push(obj);
      });
      customData.unshift({
        field: "index",
        key: "index",
        title: "ID",
        width: 40,
        align: "center",
        fixed: "left",
        renderBodyCell: ({ row, column, rowIndex }, h) => {
          return rowIndex + this.rowScroll + 1;
        },
      });
      customData.unshift({
        field: "",
        key: "checkbox",
        type: "checkbox",
        title: "",
        width: 50,
        align: "center",
        fixed: "left",
      });
      customData.push({
        field: "operation",
        key: "operation",
        title: "操作",
        width: 80,
        align: "center",
        fixed: "left",
        renderBodyCell: ({ row, column, rowIndex }, h) => {
          if (!row.parentgoodsid) {
            //判断是否为替代料
            row.isType = "主料";
          } else {
            for (var j = 0; j < this.lst.length; j++) {
              if (row.parentgoodsid == row.goodsid) {
                row.isType = "替代料";
              }
            }
          }
          var html = (
            <div>
              <el-button
                size="mini"
                type="text"
                v-show={row.isType == "主料"}
                on-click={() => {
                  this.replaceMaterial(rowIndex, row);
                }}
              >
                替代料
              </el-button>

              <el-button
                size="mini"
                type="text"
                v-show={row.isType != "主料"}
                on-click={() => {
                  this.cancelReplace(rowIndex, row);
                }}
              >
                取消替代料
              </el-button>
            </div>
          );
          return html;
        },
      });
      this.columsData = customData;
      this.$forceUpdate();
    },
    openDialog() {
      this.setColumsVisible = true;
    },
    submitUpdate() {
      this.$refs.setcolums.submitUpdate();
      this.$forceUpdate();
    },

    // 打开添加弹窗 修改项布尔名
    getselPwProcess(val) {
      if (!this.formdata.goodsid) {
        this.$message.warning("请选择货品");
        return;
      }
      this.selGoodsFormVisble = true;
      this.multi = val;
    },
    //先做以货品信息导入
    selPwProcess() {
      const oldiLst = this.$refs.selPwProcess.$refs.selectgoods.selection;
      if (oldiLst.length == 0) {
        this.$message.warning("请选择单据内容");
        return;
      }
      this.selGoodsFormVisble = false;
      for (var i = 0; i < oldiLst.length; i++) {
        var obj = {
          goodsid: oldiLst[i].id,
          goodsuid: oldiLst[i].goodsuid,
          goodsname: oldiLst[i].goodsname,
          goodsunit: oldiLst[i].goodsunit,
          goodsspec: oldiLst[i].goodsspec,
          partid: oldiLst[i].partid,

          bomid: oldiLst[i].bomid,
          //swagger
          attributecode: "",
          flowcode: "",
          lossrate: 0,
          sublossqty: 0,
          mainqty: 1,
          remark: "",
          revision: 0,
          rownum: 0,
          subqty: 1,
          subcount: 0,
          attrcode: "",
          //
          // id:new Date().toLocaleString(),
          isType: "主料",
          parentid: "",
          parentgoodsid: "",
        };
        if (this.idx != 0) {
          obj.pid = this.idx;
        }
        this.lst.push(obj);
      }
    },
    //替代料
    replaceMaterial(index, row) {
      if (index == 0) {
        this.$message.warning("第一个货品不能为替代料");
        return;
      }
      for (var i = 0; i < this.lst.length; i++) {
        if (row.goodsid == this.lst[i].parentgoodsid) {
          this.$message.warning("已有替代料的货品不能成为替代料");
          return;
        }
      }
      for (var i = index - 1; i >= 0; i--) {
        // console.log('this.lst',this.lst[i],'ss',this.lst[i].parentgoodsid)
        if (!this.lst[i].parentgoodsid) {
          //判断上一个是否为替代来料 ,不是，就赋值，跳出循环
          if (!this.lst[i].subcount) {
            this.lst[i].subcount = 0;
          }
          this.lst[i].subcount += 1;

          row.parentid = this.lst[i].id ? this.lst[i].id : "";
          row.parentgoodsid = this.lst[i].goodsid;
          //  this.$set(row, "parentgoodsid",this.lst[i].goodsid);
          row.isType = "替代料";
          // this.$set(row, "isType", "替代料");
          // this.$forceUpdate();
          break;
        }
      }
    },
    cancelReplace(index, row) {
      for (var i = 0; i < this.lst.length; i++) {
        if (row.parentgoodsid == this.lst[i].goodsid) {
          this.lst[i].subcount -= 1;
          if (this.lst[i].subcount < 0) {
            this.lst[i].subcount = 0;
          }
          this.$forceUpdate();
        }
      }
      row.parentid = "";
      row.parentgoodsid = "";
      this.$set(row, "isType", "主料");
      this.$forceUpdate();
    },
    // 移除行数据 修改项 列名
    deleteRows(index, rows) {
      var that = this;
      // 拿到选中的数据；
      var val = this.multipleSelection;
      if (val) {
        val.forEach(function (item, index) {
          that.lst.forEach(function (itemI, indexI) {
            if (
              item.goodsid === itemI.goodsid &&
              item.goodsname === itemI.goodsname &&
              item.lossrate === itemI.lossrate &&
              item.mainqty === itemI.mainqty &&
              item.subqty === itemI.subqty &&
              item.rownum === itemI.rownum
            ) {
              that.lst.splice(indexI, 1);
            }
          });
        });
      }
      that.multipleSelection = [];
      that.$forceUpdate();
      // 清除选中状态
      // this.$refs.multipleTable.clearSelection();
      // this.selected = false;
    },
    //获取站位数组
    getflowcodeData() {
      this.flowcodeData = [];
      this.$request
        .get(
          "/system/SYSM07B1/getBillEntityByDictCode?key=mat_bomitem.flowcode"
        )
        .then((res) => {
          if (res.data.code == 200) {
            if (res.data.data == null) {
              return;
            }
            if (res.data.data.item.length == 0) {
              this.flowcodeData = [];
              return;
            }
            for (var i = 0; i < res.data.data.item.length; i++) {
              var Item = res.data.data.item[i];
              this.flowcodeData.push(Item);
            }
          }
        })
        .catch((error) => {
          this.$message.error("请求出错");
        });
    },
    // ====================通用方法===================
    // 随输入框值变化其他数值
    changeInput(e, row, colname) {
      if (
        colname == "subqty" ||
        colname == "lossrate" ||
        colname == "mainqty"
      ) {
        //计算单件含损量
        row.sublossqty = this.$fomatFloat(
          (row.subqty / row.mainqty) * (1 + row.lossrate / 100),
          4
        );
      }
    },
    pasteFun(data, index) {
      // 获取修改的字段code
      var filekeys = [];
      for (var item in data[0]) {
        filekeys.push(item);
      }
      for (var i = 0; i < data.length; i++) {
        var Item = data[i];
        for (var j = 0; j < filekeys.length; j++) {
          var fileItem = filekeys[j];
          this.lst[index + i][fileItem] = Item[fileItem].replace(
            /^\s*|\s*$/g,
            ""
          );
          // 金额计算
          if (this.countfiles.includes(fileItem)) {
            this.changeInput("", this.lst[index + i], fileItem);
          }
          // spu
          // var spuidx = this.customList.findIndex((a) => a.attrkey == fileItem);
          // if (spuidx != -1) {
          //   this.setAttributeJson(this.lst[index + i], index + i); //spu
          // }
        }
      }
    },
    // 设置table高度
    catchHight() {
      this.$nextTick(() => {
        if (!this.$refs.elitem) return;
        this.tableHeight =
          this.$refs.elitem.getBoundingClientRect().height - 32;
      });
    },

    // 明细删除一项 不过首先要有弹出框询问 确定删除
    delItem() {
      this.$confirm("此操作将永久删除该记录, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // 执行删除的方法
          this.deleteRows();
        })
        .catch(() => {});
    },
    // 导出数据
    btnExport() {
      require.ensure([], () => {
        const tHeader = [];
        const filterVal = [];
        for (var i = 0; i < this.tableForm.item.length; i++) {
          var Item = this.tableForm.item[i];
          if (Item.displaymark) {
            tHeader.push(Item.itemname);
            filterVal.push(Item.itemcode);
          }
        }
        const list = this.lst;
        const data = this.formatJson(filterVal, list);
        export_json_to_excel(
          tHeader,
          data,
          this.formdata.goodsname + "_BoM明细表"
        ); //最后一个是表名字
      });
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map((v) => filterVal.map((j) => v[j]));
    },
    // 导入
    btnImport() {
      this.ExportVisible = true;
    },
    submitExPort() {
      console.log(this.$refs.exportFile.lst);
      if (this.$refs.exportFile.allowUpload) {
        this.lst = this.$refs.exportFile.lst;
        for (var i = 0; i < this.lst.length; i++) {
          this.lst[i].isType = "主料";
          this.lst[i].pid = this.formdata.id;
        }
        this.closeDialog();
      } else {
        this.$message.warning("请先识别编码");
      }
    },
    closeDialog() {
      this.ExportVisible = false;
      //  this.$emit('BindDate')
    },

    copyRow() {
      var itemData = this.$refs.copyBom.selrows;
      if (itemData.item.length == 0) {
        this.$message.warning("请选择BOM主料");
        return;
      }
      itemData.item.forEach((item, index) => {
        var obj = Object.assign({}, item);
        this.$delete(obj, "id");
        this.$delete(obj, "pid");
        this.$delete(obj, "revision");
        this.$delete(obj, "rowKeys");
        if (this.idx != 0) {
          obj.pid = this.idx;
        }
        obj.parentid = "";
        obj.isType = "主料";
        this.lst.push(obj);
      });
      for (var i = 0; i < this.lst.length; i++) {
        if (!this.lst[i].parentgoodsid) {
          this.lst[i].isType = "主料";
        } else {
          for (var j = 0; j < this.lst.length; j++) {
            if ((this.lst[i].parentgoodsid = this.lst[j].goodsid)) {
              this.lst[i].isType = "替代料";
            }
          }
        }
      }
      this.copyBomVisible = false;
    },
    //上移
    getMoveUp() {
      if (this.multipleSelection.length != 1) {
        this.$message.warning("只能选择一行内容！");
        return;
      }
      var row = this.multipleSelection[0];
      var index = this.multipleSelection[0].rownum;
      if (index == 0) {
        this.$message.warning("已经是第一行了！");
        return;
      }
      this.lst.splice(index, 1);
      this.lst.splice(index - 1, 0, row);
      for (var i = 0; i < this.lst.length; i++) {
        this.lst[i].rownum = i;
      }
    },
    //下移
    getMoveDown() {
      if (this.multipleSelection.length != 1) {
        this.$message.warning("只能选择一行内容！");
        return;
      }
      var row = this.multipleSelection[0];
      var index = this.multipleSelection[0].rownum;
      if (index == this.lst.length - 1) {
        this.$message.warning("已经是最后一行了！");
        return;
      }
      this.lst.splice(index, 1);
      this.lst.splice(index + 1, 0, row);
      for (var i = 0; i < this.lst.length; i++) {
        this.lst[i].rownum = i;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
@import "@/styles/mycustom.scss";
::v-deep .writePacksn {
  font-size: 15px;
  color: #409eff;
  cursor: pointer;
  height: 100%;
  line-height: 2;
  padding: 0 4px;
}
// ::v-deep .el-input--suffix .el-input__inner {
//   padding-left: 6px;
// }
.curssor {
  cursor: pointer;
}

/**模拟table的边框 */
.table-container {
  overflow-x: auto;
  overflow-y: hidden;
}

.table-position {
  position: relative;
  overflow: auto;
  height: 80%;
  width: 100%;
  overflow-x: hidden;
}
/**flex css  */
.flex {
  display: flex;
  flex-wrap: wrap;
}
.f-1 {
  flex: 1;
}

.f-d-c {
  flex-direction: column;
}

.j-c {
  justify-content: center;
}

.a-c {
  align-items: center;
}

.j-s {
  justify-content: space-between;
}

.j-start {
  justify-content: flex-start;
}
.j-end {
  justify-content: flex-end;
}
.p-r {
  position: relative;
}
.p-a {
  position: absolute;
}
//模拟table的边框
.table-container {
  overflow: auto;
}
::v-deep .el-table__footer-wrapper {
  display: none;
}
.toolButton {
  line-height: 1;
  font-size: 14px;
  color: #303133;
}
.form {
  margin-right: 20px;
  margin-left: 20px;
  width: calc(100% - 40px);
}
/**修改表单用 */
::v-deep .el-scrollbar__wrap {
  overflow-x: hidden;
}
::v-deep.custInfo .el-row {
  margin-bottom: -20px;
  display: flex;
  flex-wrap: wrap;
}
/**修改错误提示 */
::v-deep .el-form-item__error {
  top: 0;
  background: #fff;
  height: 25px;
  margin-top: 5px;
  margin-left: 5px;
  width: 120px;
  display: flex;
  align-items: center;
  // display: flex;
  // flex-wrap: wrap ;
}
/** */
.addMultiInfo {
  .el-row {
    margin-bottom: 10px;
  }
}
.dialog-body .el-form-item__error {
  top: 0% !important;
  left: 10px !important;
}
::v-deep .el-table__footer-wrapper {
  display: block;
}
::v-deep .el-table__footer-wrapper tbody td {
  color: #555555;
  cursor: pointer;
  font-weight: bold;
}
::v-deep .el-table__footer-wrapper tbody td:first-child {
  color: #555555;
  cursor: auto;
  font-weight: bold;
}
.droupList {
  position: absolute;
  top: 0px;
  right: -10px;
  z-index: 9;
}
::v-deep .ve-table td {
  padding: 0 !important;
}
::v-deep .ve-table-body-tr,
::v-deep .ve-table-footer-tr {
  height: 30px !important;
}
::v-deep .el-input--small .el-input__inner {
  height: 30px;
  line-height: 30px;
}
::v-deep .ve-table-content-wrapper,
::v-deep .ve-table-content {
  height: 100% !important;
}
::v-deep .ve-table-body::after {
  content: "";
}
::v-deep .ve-table-content .ve-table-body tr:nth-last-child(1) td {
  border-bottom: 1px solid #eee !important;
}
</style>
