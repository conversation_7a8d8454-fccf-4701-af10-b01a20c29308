<template>
  <div ref="elitem" class="flex f-d-c form" style="height: 100%">
    <div style="display: flex; justify-content: space-between">
      <el-row>
        <el-button-group>
          <el-button
            type="primary"
            size="mini"
            :disabled="!!formdata.assessor"
            @click="btnImport()"
          ><i class="el-icon-download" />文件导入</el-button>
          <el-button
            type="primary"
            icon="el-icon-brush"
            size="mini"
            :disabled="lst.length == 0"
            @click="checkGoodsUid"
          >
            识别编码
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-brush"
            size="mini"
            :disabled="lst.length == 0"
          >
            BOM检查
          </el-button>
          <el-button
            :disabled="multipleSelection.length == 0"
            type="danger"
            size="mini"
            @click.native="delItem()"
          ><i class="el-icon-delete" /> 删 除</el-button>
          <!-- <el-button
            :disabled="!!formdata.assessor"
            type="primary"
            size="mini"
            @click.native="copyBomVisible = true"
            ><i class="el-icon-document-copy" /> 克 隆</el-button
          > -->
        </el-button-group>
      </el-row>
      <div style="margin-right: 10px; position: relative">
        <el-button
          size="mini"
          icon="el-icon-refresh-right"
          style="font-weight: bold"
          @click.native="$emit('bindData')"
        />
        <el-button
          size="mini"
          icon="el-icon-s-tools"
          @click="openDialog()"
        />
        <el-button
          size="mini"
          icon="el-icon-download"
          @click="btnExport"
        />
      </div>
    </div>
    <div class="table-container f-1 table-position" style="width: 100%">
      <ve-table
        ref="multipleTable"
        row-key-field-name="rowKeys"
        :max-height="tableHeight"
        :scroll-width="tableMinWidth"
        :style="{ 'word-break': 'break-all' }"
        is-horizontal-resize
        :fixed-header="true"
        :columns="columsData"
        :table-data="lst"
        :column-hidden-option="{ defaultHiddenColumnKeys: columnHidden }"
        border-x
        border-y
        :border-around="true"
        :checkbox-option="checkboxOption"
        :column-width-resize-option="columnWidthResizeOption"
        :edit-option="editOption"
        :virtual-scroll-option="virtualScrollOption"
        :footer-data="footerData"
        :cell-autofill-option="cellAutofillOption"
        :contextmenu-body-option="contextmenuBodyOption"
        :clipboard-option="clipboardOption"
      />
    </div>
    <el-dialog
      v-if="importVisble"
      title="文件导入"
      width="400px"
      :visible.sync="importVisble"
      append-to-body
    >
      <div>
        <el-upload
          ref="upload"
          class="upload-demo"
          :drag="true"
          action=""
          :multiple="false"
          :on-change="handleChange"
          :on-remove="handleRemove"
          :on-preview="handlePreview"
          :on-success="handleSuccess"
          :limit="limitUpload"
          :auto-upload="false"
          accept=".xlsx,.xls,.csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">上传文件</div>
          <div slot="tip" class="el-upload__tip">
            <p style="text-align: center">
              只能上传xlsx / xls文件<el-button
                type="text"
                @click.native="modelExport"
              >下载模板</el-button>
            </p>
          </div>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          size="small"
          @click="importf"
        >确 定</el-button>
        <el-button size="small" @click="importVisble = false">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-if="copyBomVisible"
      title="BOM克隆"
      :append-to-body="true"
      :visible.sync="copyBomVisible"
      width="60vw"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <copyBom ref="copyBom" :multi="0" :goodsid="formdata.goodsid" />
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click.native="copyRow()">确 定</el-button>
        <el-button @click="copyBomVisible = false">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 新建货品 -->
    <el-dialog
      v-if="createdGoodsVisible"
      title=""
      :append-to-body="true"
      :visible.sync="createdGoodsVisible"
      width="90vw"
      top="1vh"
      class="goodsDialog"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      :show-close="false"
    >
      <goodsFormedit
        ref="goodsFormedit"
        :is-dialog="true"
        :idx="0"
        @closeDialog="createdGoodsVisible = false"
      />
    </el-dialog>
    <el-dialog
      v-if="setColumsVisible"
      width="800px"
      title="列设置"
      :append-to-body="true"
      :visible.sync="setColumsVisible"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      top="5vh"
    >
      <Setcolums
        ref="setcolums"
        :code="'D91M02B1Item'"
        :table-form="tableForm"
        @bindData="$emit('bindData')"
        @closeDialog="setColumsVisible = false"
      />

    </el-dialog>
  </div>
</template>
<script>
import selPwProcess from '@/views/modules/E08/E08M01B8/components/selectBom.vue'
import NP from 'number-precision'
import Export from './export.vue'
import copyBom from './select.vue'
import { export_json_to_excel } from '@/components/Excel/Export2Excel.js'
import { alItem } from './tablecolums.js'
import Setcolums from '@/components/Setcolums'
import request from '@/utils/request'
import goodsInfo from '@/components/goodsInfo'
import goodsFormedit from '@/views/modules/E08/E08M01B8/components/formEdit.vue'
export default {
  name: 'Elitem',
  components: {
    selPwProcess,
    Export,
    Setcolums,
    copyBom,
    goodsInfo,
    goodsFormedit
  },
  props: ['formdata', 'lstitem', 'idx'],
  data() {
    return {
      listLoading: false, // 列表加载中
      lst: [],
      multi: 0,
      billamount: 0, //
      // =============================
      selected: false,
      tableHeight: 0,
      multipleSelection: [],
      isEditOk: true,
      ExportVisible: false,
      importVisble: false,
      //
      tableForm: alItem,
      setColumsVisible: false,
      copyBomVisible: false,
      limitUpload: 1,
      fileTemp: {},
      allowUpload: false,
      // ==============================
      createdGoodsVisible: false,

      // =====================================
      columsData: [],
      // 行样式配置
      rowStyleOption: {
        clickHighlight: false, // 是否开启行click 背景高亮
        hoverHighlight: false, // 是否开启行hover 背景高亮
        stripe: false // 是否开启斑马纹
      },
      columnHidden: [], // 列隐藏
      // 行多选配置
      checkboxOption: {
        // 多选
        selectedRowChange: ({ row, isSelected, selectedRowKeys }) => {
          // selectedRowKeys == 时间-rownum
          if (selectedRowKeys.length != 0) {
            this.multipleSelection = []
            for (var i = 0; i < selectedRowKeys.length; i++) {
              // 根据 RowKeys 获取row位置
              var index = this.lst.findIndex(
                (item) => item.rowKeys == selectedRowKeys[i]
              )
              if (index != -1) {
                this.multipleSelection.push(this.lst[index])
              }
            }
          } else {
            this.multipleSelection = []
          }
        },
        // 全选
        selectedAllChange: ({ isSelected, selectedRowKeys }) => {
          if (isSelected) {
            this.multipleSelection = JSON.parse(JSON.stringify(this.lst))
          } else {
            this.multipleSelection = []
          }
        }
      },
      // 列宽拉伸
      columnWidthResizeOption: {
        enable: true,
        minWidth: 10,
        sizeChange: ({ column, differWidth, columnWidth }) => {
          this.columnResizeInfo.column = column
          this.columnResizeInfo.differWidth = differWidth
          this.columnResizeInfo.columnWidth = columnWidth
        }
      },
      columnResizeInfo: {
        column: '',
        differWidth: '',
        columnWidth: ''
      },
      // 单元格编辑
      editOption: {
        // cell value change
        cellValueChange: ({ row, column }) => {
          if (
            column.field == 'goodsname' ||
            column.field == 'goodsspec' ||
            column.field == 'goodsunit' ||
            column.field == 'goodsuid' ||
            column.field == 'subcount' ||
            column.field == 'isType' ||
            column.field == 'operation' ||
            column.field == 'partid'
          ) {
            // this.$event.preventDefault();
          } else {
            // this.changeInput("", row, column.field);
            // this.setAttributeJson(row, row.rownum);
          }
        }
      },
      // 虚拟滚动
      virtualScrollOption: {
        enable: true
      },
      // 底部合计行
      footerData: [],
      // 下拉填充
      cellAutofillOption: {
        directionX: false,
        directionY: true,
        beforeAutofill: ({ sourceSelectionData }) => {
          // 关闭下拉填充
          var key = Object.keys(sourceSelectionData[0])[1]
          if (
            key == 'goodsname' ||
            key == 'goodsspec' ||
            key == 'goodsunit' ||
            key == 'goodsuid' ||
            key == 'groupuid' ||
            key == 'subcount' ||
            key == 'isType' ||
            key == 'operation' ||
            key == 'partid'
          ) {
            return false
          }
        },
        afterAutofill: ({
          sourceSelectionData, // 源数据
          targetSelectionData
        }) => {
          for (var i = 0; i < targetSelectionData.length; i++) {
            var Item = targetSelectionData[i]
            // var num=Item.rowKeys.split('-')[1]
            var index = this.lst.findIndex(
              (item) => item.rowKeys == Item.rowKeys
            )
            if (index != -1) {
              // this.changeInput("", this.lst[index], Object.keys(Item)[1]);
              // this.setAttributeJson(this.lst[index], index);
            }
          }
        }
      },
      // 右键菜单
      contextmenuBodyOption: {
        beforeShow: (isWholeRowSelection) => {
          if (!this.copyText) {
            this.contextmenuBodyOption.contextmenus[1].disabled = true
          } else {
            this.contextmenuBodyOption.contextmenus[1].disabled = false
          }
          // 添加新选项菜单
          var key = isWholeRowSelection.selectionRangeKeys.startColKey
          this.addNewMenu(key, isWholeRowSelection)
          console.log(isWholeRowSelection, 'allowUpload')
        },
        afterMenuClick: ({
          type,
          selectionRangeKeys,
          selectionRangeIndexes
        }) => {
          var key = selectionRangeKeys.startColKey
          var indexLst = selectionRangeIndexes.startRowIndex
          this.$nextTick(() => {
            if (type == 'COPY') {
            } else if (type == 'PASTE') {
              if (this.copyText) {
                this.lst[indexLst][key] = this.copyText
              } else {
                this.lst[indexLst][key] = ''
              }
            } else if (type == 'EMPTY_CELL') {
            } else if (type == 'addgoods') {
              this.createdGoods(this.lst[indexLst], indexLst)
            } else {
              // console.log("type", type);
              this.lst[indexLst][key] = type
            }
          })
        },
        contextmenus: [
          {
            label: '复制',
            type: 'COPY'
          },
          {
            label: '粘贴',
            type: 'PASTE'
          },
          {
            type: 'SEPARATOR'
          },
          {
            label: '清空单元格',
            type: 'EMPTY_CELL'
          }
        ]
      },
      // 剪贴板
      clipboardOption: {
        copy: true,
        paste: true,
        cut: false,
        delete: true,
        afterCopy: ({ data, selectionRangeIndexes, selectionRangeKeys }) => {
          this.copyText = data[0][0]
        },
        afterPaste: ({ data, selectionRangeIndexes, selectionRangeKeys }) => {
          // ctrl+v 粘贴
          var indexLst = selectionRangeIndexes.startRowIndex
          var key = selectionRangeKeys.startColKey
          this.lst[indexLst][key] = data[0][key].replace(/^\s*|\s*$/g, '')
          // this.setAttributeJson(this.lst[indexLst], indexLst);
        },
        afterCut: ({ data, selectionRangeIndexes, selectionRangeKeys }) => {
          this.copyText = data[0][0]
        },
        afterDelete: ({ data, selectionRangeIndexes, selectionRangeKeys }) => {
          var indexLst = selectionRangeIndexes.startRowIndex
          // this.setAttributeJson(this.lst[indexLst], indexLst);
        }
      },
      copyText: '' // 复制的内容
    }
  },
  computed: {
    tableMinWidth() {
      var tableWidth = '100%'
      if (this.tableForm.item.length != 0) {
        tableWidth = 0
        for (var i = 0; i < this.tableForm.item.length; i++) {
          var Item = this.tableForm.item[i]
          if (Item.displaymark) {
            tableWidth += Number(Item.minwidth)
          }
        }
      }
      return tableWidth
    }
  },
  watch: {
    lstitem: function(val, oldVal) {
      this.lst = this.lstitem
      for (var i = 0; i < this.lst.length; i++) {
        // 判断 替代料 已弃用-操作在getColumn()中创建
        // if (!this.lst[i].parentgoodsid) {
        //   //判断是否为替代料
        //   this.lst[i].isType = "主料";
        // } else {
        //   for (var j = 0; j < this.lst.length; j++) {
        //     if ((this.lst[i].parentgoodsid = this.lst[j].goodsid)) {
        //       this.lst[i].isType = "替代料";
        //     }
        //   }
        // }
      }
    },
    'formdata.assessor': function(val, oldVal) {
      if (!this.formdata.assessor) {
        this.isEditOk = true
      } else {
        this.isEditOk = false
      }
      this.initTable(this.tableForm)
    },
    lst: function(val, oldVal) {
      console.log(val, 'lst')
      if (val == undefined) {
        this.lst = []
      }
      for (var i = 0; i < val.length; i++) {
        if (!val[i].rowKeys) {
          val[i].rowKeys = new Date().getTime() + '-' + i
        }
        val[i].rownum = i
      }
    }
  },
  created() {
    this.lst = []
  },
  mounted() {
    this.catchHight()
  },
  methods: {
    addNewMenu(key, position) {
      var obj = {
        label: '配置属性',
        type: 'choice'
      }
      if (key == 'attrcode') {
        var spuArr = ['厂制', '委制', '外购', '客供']
        obj.children = []
        for (var i = 0; i < spuArr.length; i++) {
          var item = spuArr[i]
          var objItem = { label: item, type: item }
          obj.children.push(objItem)
        }
      } else {
        obj.disabled = true
      }
      this.contextmenuBodyOption.contextmenus[4] = obj
      this.contextmenuBodyOption.contextmenus[5] = {
        label: '新建货品',
        type: 'addgoods',
        disabled: this.lst[position.selectionRangeIndexes.startRowIndex]
          .goodsuid
          ? true
          : !this.allowUpload
      }
    },
    getColumn() {
      request
        .get('/system/SYSM07B9/getBillEntityByCode?code=D91M02B1Item')
        .then((res) => {
          if (res.data.code == 200) {
            if (res.data.data == null) {
              this.tableForm = alItem
              this.initTable(this.tableForm)
              return
            }
            this.tableForm = res.data.data
            this.initTable(this.tableForm)
          }
        })
        .catch((error) => {
          this.$message.error('请求出错')
        })
    },
    initTable(data) {
      var customData = []
      this.columnHidden = []

      data.item.forEach((item, index) => {
        var obj = {
          field: item.itemcode,
          key: item.itemcode,
          title: item.itemname,
          width: !isNaN(item.minwidth) ? item.minwidth + 'px' : item.minwidth,
          displaymark: item.displaymark,
          fixed: item.fixed ? (item.fixed == 1 ? 'left' : 'right') : false,
          ellipsis: item.overflow ? { showTitle: true } : false,
          align: item.aligntype ? item.aligntype : 'center',
          sortBy: item.sortable ? 'asc' : false,
          edit: this.isEditOk ? (!!item.editmark) : false,
          resize: true,
          renderBodyCell: ({ row, column, rowIndex }, h) => {
            if (item.itemcode == 'goodsuid') {
              var html = (
                <goodsInfo
                  scopeIndex={row.rownum}
                  scopeVal={row[item.itemcode]}
                ></goodsInfo>
              )
              return html
            } else if (item.itemcode == 'isType') {
              var html = <span>{row.isType ? row.isType : '主料'}</span>
              return html
            } else {
              return row[item.itemcode]
            }
          }
        }
        // 判断显示隐藏
        if (!item.displaymark) {
          this.columnHidden.push(item.itemcode)
        }
        if (item.itemcode == 'layer' && item.itemname == '级别号') {
          obj.children = []
          for (var i = 1; i < 7; i++) {
            var childObj = {
              field: 'layer' + [i],
              key: 'layer' + [i],
              title: i,
              width: 50,
              edit: !!this.isEditOk
            }
            obj.children.push(childObj)
          }
        }
        customData.push(obj)
      })

      customData.unshift({
        field: 'index',
        key: 'index',
        title: 'ID',
        width: 40,
        align: 'center',
        fixed: 'left',
        renderBodyCell: ({ row, column, rowIndex }, h) => {
          return ++rowIndex
        }
      })
      customData.unshift({
        field: '',
        key: 'checkbox',
        type: 'checkbox',
        title: '',
        width: 50,
        align: 'center',
        fixed: 'left'
      })
      customData.push({
        field: 'operation',
        key: 'operation',
        title: '操作',
        width: 80,
        align: 'center',
        fixed: 'left',
        renderBodyCell: ({ row, column, rowIndex }, h) => {
          if (!row.parentgoodsid) {
            // 判断是否为替代料
            row.isType = '主料'
          } else {
            for (var j = 0; j < this.lst.length; j++) {
              if (row.parentgoodsid == row.goodsid) {
                row.isType = '替代料'
              }
            }
          }
          var html = (
            <div>
              <el-button
                size='mini'
                type='text'
                v-show={row.isType == '主料'}
                on-click={() => {
                  this.replaceMaterial(rowIndex, row)
                }}
              >
                替代料
              </el-button>

              <el-button
                size='mini'
                type='text'
                v-show={row.isType != '主料'}
                on-click={() => {
                  this.cancelReplace(rowIndex, row)
                }}
              >
                取消替代料
              </el-button>
            </div>
          )
          return html
        }
      })
      this.columsData = customData
      this.$forceUpdate()
    },
    openDialog() {
      this.setColumsVisible = true
    },
    submitUpdate() {
      this.$refs.setcolums.submitUpdate()
      this.$forceUpdate()
    },
    // 替代料
    replaceMaterial(index, row) {
      // console.log(row, "replaceMaterial");
      if (index == 0) {
        this.$message.warning('第一个货品不能为替代料')
        return
      }
      for (var i = 0; i < this.lst.length; i++) {
        if (row.goodsid == null || row.goodsid === this.lst[i].parentgoodsid) {
          this.$message.warning('已有替代料的货品不能成为替代料')
          return
        }
      }
      for (var i = index - 1; i >= 0; i--) {
        if (!this.lst[i].parentgoodsid) {
          // 判断上一个是否为替代来料 ,不是，就赋值，跳出循环
          if (!this.lst[i].subcount) {
            this.lst[i].subcount = 0
          }
          this.lst[i].subcount += 1
          row.parentid = this.lst[i].id ? this.lst[i].id : ''
          // row.parentgoodsid = this.lst[i].goodsid;
          this.$set(row, 'parentgoodsid', this.lst[i].goodsid)
          // row.isType = "替代料";
          this.$set(row, 'isType', '替代料')
          console.log(this.lst[i], row)
          this.$forceUpdate()
          break
        }
      }
    },
    cancelReplace(index, row) {
      // console.log(row, "cancelReplace");
      for (var i = 0; i < this.lst.length; i++) {
        if (row.parentgoodsid == this.lst[i].goodsid) {
          this.lst[i].subcount -= 1
          if (this.lst[i].subcount < 0) {
            this.lst[i].subcount = 0
          }
          this.$forceUpdate()
        }
      }
      row.parentid = ''
      row.parentgoodsid = ''
      this.$set(row, 'isType', '主料')
      this.$forceUpdate()
    },
    // 移除行数据 修改项 列名
    deleteRows(index, rows) {
      var that = this
      var val = this.multipleSelection
      if (val) {
        val.forEach(function(item, index) {
          that.lst.forEach(function(itemI, indexI) {
            if (
              item.goodsid === itemI.goodsid &&
              item.goodsname === itemI.goodsname &&
              item.rownum === itemI.rownum
            ) {
              that.lst.splice(indexI, 1)
            }
          })
        })
      }
      that.multipleSelection = []
      that.$forceUpdate()
      // 清除选中状态
      // this.$refs.multipleTable.clearSelection();
      // this.selected = false;
    },
    async checkGoodsUid() {
      const that = this
      let newGoodsCounter = 0
      const promiseList = []
      that.allowUpload = false
      console.log(this.lst)
      for (var i = 0; i < this.lst.length; i++) {
        // if(!!this.lst[i].matmark||!!this.lst[i].bomid){
        //   // matmark=1 或 bomid 不为空时，跳过识别
        // }else{

        // }
        const promise = new Promise((resolve, reject) => {
          const obj = {
            goodsname: that.lst[i].goodsname,
            goodsspec: that.lst[i].goodsspec,
            partid: that.lst[i].partid ? that.lst[i].partid : ''
          }
          const lstIndex = i
          request
            .post('/e08/E08M13B1/getEntityBynsp', JSON.stringify(obj))
            .then((res) => {
              if (res.data.code == 200) {
                if (res.data.data == null) {
                  newGoodsCounter++
                  reject('暂无货品信息')
                } else {
                  that.lst[lstIndex].goodsid = res.data.data.id
                  that.lst[lstIndex].bomid = res.data.data.bomid
                  that.lst[lstIndex].goodsuid = res.data.data.goodsuid
                  resolve(res.data)
                }
              } else {
                newGoodsCounter++
                reject('查询错误')
              }
            })
            .catch((er) => {
              reject('查询失败')
            })
        })
        promiseList.push(promise)
      }
      await Promise.allSettled(promiseList)
        .then((res) => {
          // console.log(res, "allSettled");
          // that.$message.success("识别编码成功");
          that.allowUpload = true
        })
        .catch((er) => {
          that.$message.warning(er)
          that.allowUpload = false
        })
        .finally(() => {
          that.$message(
            `识别完毕,共${that.lst.length}条,需导入新货品${newGoodsCounter}条`
          )
        })
    },
    // ====================通用方法===================
    createdGoods(row, index) {
      this.createdGoodsVisible = true
      setTimeout(() => {
        this.$refs.goodsFormedit.formdata.goodsname = row.goodsname
        this.$refs.goodsFormedit.formdata.goodsspec = row.goodsspec
        this.$refs.goodsFormedit.formdata.goodsunit = row.goodsunit
        this.$refs.goodsFormedit.formdata.partid = row.partid
      }, 100)
    },
    createbyGoods() {
      console.log(row)
      // 新建货品，货品编码生成-货品分组
      request
        .post('/e08/E08M14B1/create', JSON.stringify(this.goodsForm))
        .then((res) => {
          if (res.data.code == 200) {
            this.$message.success('新建货品成功')
            this.$set(row, 'goodsuid', res.data.data.goodsuid)
          } else {
            this.$message.warning('新建货品失败')
          }
        })
        .catch((er) => {
          this.$message.error('请求错误')
        })
    },
    // 设置table高度
    catchHight() {
      this.$nextTick(() => {
        if (!this.$refs.elitem) return
        this.tableHeight =
          this.$refs.elitem.getBoundingClientRect().height - 32
      })
    },
    // 明细删除一项 不过首先要有弹出框询问 确定删除
    delItem() {
      this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.deleteRows()
        })
        .catch(() => {})
    },
    // 导出数据
    btnExport() {
      require.ensure([], () => {
        const tHeader = []
        const filterVal = []
        for (var i = 0; i < this.tableForm.item.length; i++) {
          var Item = this.tableForm.item[i]
          if (Item.displaymark) {
            tHeader.push(Item.itemname)
            filterVal.push(Item.itemcode)
          }
        }
        const list = this.lst
        const data = this.formatJson(filterVal, list)
        export_json_to_excel(
          tHeader,
          data,
          this.formdata.goodsname + '_BoM明细表'
        ) // 最后一个是表名字
      })
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map((v) => filterVal.map((j) => v[j]))
    },
    // 文件导入
    btnImport() {
      this.importVisble = true
    },
    // 添加文件
    handleChange(file, fileList) {
      this.fileTemp = file.raw
      const fileName = file.raw.name
      const fileType = fileName.substring(fileName.lastIndexOf('.') + 1)
      // 判断上传文件格式
      if (this.fileTemp) {
        if (fileType == 'xlsx' || fileType == 'xls') {
          // this.importf(this.fileTemp);
        } else {
          this.$message.warning('文件格式错误，请删除后重新上传！')
        }
      } else {
        this.$message.warning('请上传文件！')
      }
    },
    // 文件导入
    importf() {
      const _this = this
      const inputDOM = this.$refs.inputer
      var file = this.fileTemp
      var formData = new FormData()
      formData.append('file', file)
      request
        .post('/e08/E08M14B1/importLayerExecl', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })
        .then((res) => {
          if (res.data.code == 200) {
            this.$message.success('文件导入成功')
            // 关闭上传接口
            this.importVisble = false
            this.lst = res.data.data
            for (var i = 0; i < this.lst.length; i++) {
              var item = this.lst[i]
              // item.subcount = 0;
              // item.isType = "主料";
              // item.parentgoodsid = "";
              // item.parentid = "";
              this.$set(item, 'subcount', 0)
              this.$set(item, 'isType', '主料')
              this.$set(item, 'parentgoodsid', '')
              this.$set(item, 'parentid', '')
              if (this.idx) {
                this.$set(item, 'pid', this.idx)
              }
            }
            this.$forceUpdate()
          } else {
            this.$message.warning('文件导入失败，请重试')
          }
        })
    },
    // 导出模板
    modelExport() {
      request
        .get('/e08/E08M14B1/exportLayerModel', { responseType: 'blob' })
        .then((res) => {
          console.log(res)
          const link = document.createElement('a')
          const blob = new Blob([res.data], { type: 'application/vnd.ms-excel' })
          link.style.display = 'none'
          link.href = URL.createObjectURL(blob)
          link.download = 'BOM多层模板.xls'
          document.body.appendChild(link)
          // 模拟点击事件
          link.click()
        })
        .catch((err) => {
          console.log(err)
        })
    },
    handlePreview(file) {
      console.log(file)
    },
    handleSuccess(file, fileList) {
      console.log('handleSuccess', file)
    },
    handleRemove(file, fileList) {
      console.log('handleRemove', file)
    },
    // ==========================================
    copyRow() {
      var itemData = this.$refs.copyBom.selrows
      if (itemData.item.length == 0) {
        this.$message.warning('请选择BOM主料')
        return
      }
      itemData.item.forEach((item, index) => {
        var obj = Object.assign({}, item)
        this.$delete(obj, 'id')
        this.$delete(obj, 'pid')
        this.$delete(obj, 'rowKeys')
        obj.parentid = ''
        obj.isType = '主料'
        this.lst.push(obj)
      })
      for (var i = 0; i < this.lst.length; i++) {
        if (!this.lst[i].parentgoodsid) {
          this.lst[i].isType = '主料'
        } else {
          for (var j = 0; j < this.lst.length; j++) {
            if ((this.lst[i].parentgoodsid = this.lst[j].goodsid)) {
              this.lst[i].isType = '替代料'
            }
          }
        }
      }
      this.copyBomVisible = false
    }
    // ===================================
  }
}
</script>
<style lang="scss" scoped>
@import "@/styles/mycustom.scss";
::v-deep .writePacksn {
  font-size: 15px;
  color: #409eff;
  cursor: pointer;
  height: 100%;
  line-height: 2;
  padding: 0 4px;
}
// ::v-deep .el-input--suffix .el-input__inner {
//   padding-left: 6px;
// }
.curssor {
  cursor: pointer;
}
.goodsDialog ::v-deep .el-dialog__body {
  padding: 10px 20px;
}
/**模拟table的边框 */
.table-container {
  overflow-x: auto;
  overflow-y: hidden;
}

.table-position {
  position: relative;
  overflow: auto;
  height: 80%;
  width: 100%;
  overflow-x: hidden;
}
/**flex css  */
.flex {
  display: flex;
  flex-wrap: wrap;
}
.f-1 {
  flex: 1;
}

.f-d-c {
  flex-direction: column;
}

.j-c {
  justify-content: center;
}

.a-c {
  align-items: center;
}

.j-s {
  justify-content: space-between;
}

.j-start {
  justify-content: flex-start;
}
.j-end {
  justify-content: flex-end;
}
.p-r {
  position: relative;
}
.p-a {
  position: absolute;
}
//模拟table的边框
.table-container {
  overflow: auto;
}
::v-deep .el-table__footer-wrapper {
  display: none;
}
.toolButton {
  line-height: 1;
  font-size: 14px;
  color: #303133;
}
.form {
  margin-right: 20px;
  margin-left: 20px;
  width: calc(100% - 40px);
}
/**修改表单用 */
::v-deep .el-scrollbar__wrap {
  overflow-x: hidden;
}
::v-deep.custInfo .el-row {
  margin-bottom: -20px;
  display: flex;
  flex-wrap: wrap;
}
/**修改错误提示 */
::v-deep .el-form-item__error {
  top: 0;
  background: #fff;
  height: 25px;
  margin-top: 5px;
  margin-left: 5px;
  width: 120px;
  display: flex;
  align-items: center;
  // display: flex;
  // flex-wrap: wrap ;
}
/** */
.addMultiInfo {
  .el-row {
    margin-bottom: 10px;
  }
}
.dialog-body .el-form-item__error {
  top: 0% !important;
  left: 10px !important;
}
::v-deep .el-table__footer-wrapper {
  display: block;
}
::v-deep .el-table__footer-wrapper tbody td {
  color: #555555;
  cursor: pointer;
  font-weight: bold;
}
::v-deep .el-table__footer-wrapper tbody td:first-child {
  color: #555555;
  cursor: auto;
  font-weight: bold;
}
.droupList {
  position: absolute;
  top: 0px;
  right: -10px;
  z-index: 9;
}
::v-deep .ve-table td {
  padding: 0 !important;
}
::v-deep .ve-table-body-tr,
::v-deep .ve-table-footer-tr {
  height: 30px !important;
}
::v-deep .el-input--small .el-input__inner {
  height: 30px;
  line-height: 30px;
}
::v-deep .ve-table-content-wrapper,
::v-deep .ve-table-content {
  height: 100% !important;
}
::v-deep .ve-table-body::after {
  content: "";
}
::v-deep .ve-table-content .ve-table-body tr:nth-last-child(1) td {
  border-bottom: 1px solid #eee !important;
}
</style>
