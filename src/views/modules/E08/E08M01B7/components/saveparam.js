/**
 * 2023-05-05 邓
 * params Mat_Bom 主表参数
 * paramsItem  Mat_BomItem 表参数
 * 
 */

var params = [
    "id",  //id
    "goodsid",  //货品id
    "itemcode",  //产品编码
    "itemname",  //产品名称
    "itemspec",  //产品规格
    "itemunit",  //产品单位
    "quotahour",  //定额工时
    "progroupid",  //工艺线路
    "mintime",  //最小用时
    "summary",  //摘要
    "enabledmark",  //有效
    "versionnum",  //版本号
    // "deletemark",  //删除标识
    // "deletelisterid",  //删除人员id
    // "deletelister",  //删除人员
    // "deletedate",  //删除日期
    // "createby",  //创建者
    // "createbyid",  //创建者id
    // "createdate",  //新建日期
    // "lister",  //制表
    // "listerid",  //制表id
    // "modifydate",  //修改日期
    // "assessor",  //审核员
    // "assessorid",  //审核员id
    // "assessdate",  //审核日期
    // "itemcount",  //item行数
    // "printcount",  //打印次数
    "custom1",  //自定义1
    "custom2",  //自定义2
    "custom3",  //自定义3
    "custom4",  //自定义4
    "custom5",  //自定义5
    "custom6",  //自定义6
    "custom7",  //自定义7
    "custom8",  //自定义8
    "custom9",  //自定义9
    "custom10",  //自定义10
    // "tenantid",  //租户id
    // "tenantname",  //租户名称
    // "revision",  //乐观锁
]
var paramsItem = [
    "id",  //
    "pid",  //pid
    "goodsid",  //商品id
    "itemcode",  //产品编码
    "itemname",  //产品名称
    "itemspec",  //产品规格
    "itemunit",  //产品单位
    "mainqty",  //主件数量
    "subqty",  //子件数量
    "lossrate",  //损耗率
    "attrcode",  //属性 厂制/委制/外购/客供
    "flowcode",  //流程编码
    "description",  //描述
    "itemlabel",  //标签
    "parentid",  //替代主料id
    "parentgoodsid",  //主料商品id
    "rownum",  //行号
    "subcount",  //替代品数
    "remark",  //备注
    "sublossqty",  //单件含损量
    "custom1",  //自定义1
    "custom2",  //自定义2
    "custom3",  //自定义3
    "custom4",  //自定义4
    "custom5",  //自定义5
    "custom6",  //自定义6
    "custom7",  //自定义7
    "custom8",  //自定义8
    "custom9",  //自定义9
    "custom10",  //自定义10
    // "tenantid",  //租户id
    // "revision",  //乐观锁

]
export default { params, paramsItem }