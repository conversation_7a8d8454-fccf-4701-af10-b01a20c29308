export const tableTh = {
  formcode: "D91M02B1Th",
  item: [
    {
      itemcode: "goodsuid",
      itemname: "货品编码",
      minwidth: "60",
      defwidth: "",
      displaymark: 1,
      fixed: 0,
      sortable: 0,
      overflow: 1,
      aligntype: "center",
      datasheet: "Mat_Goods.goodsuid",
    },
    {
      itemcode: "goodsname",
      itemname: "货品名称",
      minwidth: "100",
      displaymark: 1,
      overflow: 1,
      datasheet: "Mat_Goods.goodsname",
    },
    {
      itemcode: "goodsspec",
      itemname: "货品规格",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
      datasheet: "Mat_Goods.goodsspec",
    },
    {
      itemcode: "goodsunit",
      itemname: "单位",
      minwidth: "60",
      displaymark: 1,
      overflow: 1,
      datasheet: "Mat_Goods.goodsunit",
    },
    
    {
      itemcode: "surface",
      itemname: "颜色",
      minwidth: "60",
      displaymark: 1,
      overflow: 1,
      datasheet: "Mat_Goods.surface",
    },

    {
      itemcode: "partid",
      itemname: "外部编码",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
      datasheet: "Mat_Goods.partid",
    },
    {
      itemcode: "itemcount",
      itemname: "款数",
      minwidth: "60",
      sortable: 0,
      displaymark: 1,
      overflow: 1,
      datasheet: "Mat_Access.itemcount",
    },

  ],
}
export const tableItem = {
  formcode: "D91M02B1Item",
  item: [
    {
      itemcode: "goodsuid",
      itemname: "货品编码",
      minwidth: "80",
      defwidth: "",
      displaymark: 1,
      fixed: 0,
      sortable: 0,
      overflow: 1,
      aligntype: "center",
    },
    {
      itemcode: "goodsname",
      itemname: "货品名称",
      minwidth: "120",
      displaymark: 1,
      overflow: 1,
    },
    {
      itemcode: "goodsspec",
      itemname: "规格",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
    },
    {
      itemcode: "partid",
      itemname: "外部编码",
      minwidth: "80",
      displaymark: 0,
      overflow: 1,
    },
    {
      itemcode: "surface",
      itemname: "颜色",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
    },
    {
      itemcode: "goodsunit",
      itemname: "单位",
      minwidth: "60",
      displaymark: 1,
      overflow: 1,
    },
    {
      itemcode: "subqty",
      itemname: "配件数量",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
      editmark: 1,
    },
    {
      itemcode: "mainqty",
      itemname: "主件数量",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
      editmark: 1,
    },
    {
      itemcode: "lossrate",
      itemname: "损耗率%",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
      editmark: 1,
    },
    {
      itemcode: "sublossqty",
      itemname: "单件含损量",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
    },
    {
      itemcode: "attrcode",
      itemname: "物料来源",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
      editmark: 1,
    },
    {
      itemcode: "flowcode",
      itemname: "领用单位",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
      editmark: 1,
    },
    {
      itemcode: "remark",
      itemname: "备注",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
      editmark: 1,
    },
    {
      itemcode: "subcount",
      itemname: "替代品数量",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
    },
    {
      itemcode: "isType",
      itemname: "物料类型",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
      editmark: 1,
    },
    // {
    //   itemcode: "operation",
    //   itemname: "操作",
    //   minwidth: "80",
    //   displaymark: 1,
    //   overflow: 1,
    // },
  ],
}
export const alItem = {
  formcode: "D91M02B1AlItem",
  item: [
    {
      itemcode: "layer",
      itemname: "级别号",
      minwidth: "200",
      displaymark: 1,
      overflow: 1,
    },
    {
      itemcode: "layernum",
      itemname: "层级",
      minwidth: "80",
      defwidth: "",
      displaymark: 1,
      fixed: 0,
      sortable: 0,
      overflow: 1,
      aligntype: "center",
    },
    {
      itemcode: "goodsuid",
      itemname: "货品编码",
      minwidth: "80",
      defwidth: "",
      displaymark: 1,
      fixed: 0,
      sortable: 0,
      overflow: 1,
      aligntype: "center",
    },
    {
      itemcode: "goodsname",
      itemname: "货品名称",
      minwidth: "100",
      displaymark: 1,
      overflow: 1,
    },
    {
      itemcode: "goodsspec",
      itemname: "规格",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
    },
    {
      itemcode: "partid",
      itemname: "外部编码",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
    },
    {
      itemcode: "goodsunit",
      itemname: "单位",
      minwidth: "60",
      displaymark: 1,
      overflow: 1,
    },
    {
      itemcode: "subqty",
      itemname: "配件数量",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
      editmark: 1,
    },
    {
      itemcode: "mainqty",
      itemname: "主件数量",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
      editmark: 1,
    },
    {
      itemcode: "lossrate",
      itemname: "损耗率%",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
      editmark: 1,
    },
    {
      itemcode: "attrcode",
      itemname: "配置属性",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
      editmark: 1,
    },
    {
      itemcode: "flowcode",
      itemname: "站位",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
      editmark: 1,
    },
    {
      itemcode: "remark",
      itemname: "备注",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
      editmark: 1,
    },
    {
      itemcode: "subcount",
      itemname: "替代品数量",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
    },
    {
      itemcode: "isType",
      itemname: "物料类型",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
      editmark: 1,
    },
  ],
}
export const D91M02B1List = {
  formcode: "D91M02B1List",
  item: [
    {
      itemcode: "layerlabel",
      itemname: "层级",
      minwidth: "60",
      displaymark: 1,
      overflow: 1,
    },
    {
      itemcode: "goodsuid",
      itemname: "货品编码",
      minwidth: "100",
      defwidth: "",
      displaymark: 1,
      overflow: 1,
      fixed: 0,
      sortable: 0,
      aligntype: "center",
    },
    {
      itemcode: "goodsname",
      itemname: "货品名称",
      minwidth: "100",
      displaymark: 1,
      overflow: 1,
    },
    {
      itemcode: "goodsspec",
      itemname: "货品规格",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
    },
    {
      itemcode: "goodsunit",
      itemname: "单位",
      minwidth: "70",
      displaymark: 1,
      overflow: 1,
    },

    {
      itemcode: "subqty",
      itemname: "子件数量",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
    },
    {
      itemcode: "mainqty",
      itemname: "主件数量",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
    },
    {
      itemcode: "attrcode",
      itemname: "属性",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
    },
    // {
    //   itemcode: "goodsid",
    //   itemname: "料号",
    //   minwidth: "100",
    //   displaymark: 1,
    //   overflow: 1, 
    // },
    // {
    //   itemcode: "goodsbomid",
    //   itemname: "Bomid",
    //   minwidth: "100",
    //   displaymark: 1,
    //   overflow: 1, 
    // },

  ],
}
export const ParentBomList={
  formcode: "ParentBomList",
  item: [
    {
      itemcode: "goodsstate",
      itemname: "状态",
      minwidth: "100",
      displaymark: 1,
      overflow: 1, 
    },
    {
      itemcode: "goodsuid",
      itemname: "货品编码",
      minwidth: "100",
      defwidth: "",
      displaymark: 1,
      overflow: 1, 
      fixed: 0,
      sortable: 0,
      aligntype: "center",
    },
    
    {
      itemcode: "goodsname",
      itemname: "货品名称",
      minwidth: "100",
      displaymark: 1,
      overflow: 1, 
    },
    {
      itemcode: "goodsspec",
      itemname: "货品规格",
      minwidth: "80",
      displaymark: 1,
      overflow: 1, 
    },
    {
      itemcode: "goodsunit",
      itemname: "单位",
      minwidth: "70",
      displaymark: 1,
      overflow: 1, 
    },
    {
      itemcode: "partid",
      itemname: "外部编码",
      minwidth: "70",
      displaymark: 1,
      overflow: 1, 
    },
  ],
}