<template>
  <div style="height: 100%;">
    <el-table
      ref="selectVal"
      v-loading="listLoading"
      :data="lst"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
      style="overflow: auto;height: 100%;min-height: 380px;"
      :header-cell-style="{
        background: '#F3F4F7',
        color: '#555',
        padding: '3px 0px 3px 0px',
      }"
      :cell-style="{ padding: '4px 0px 4px 0px' }"
    >
      <template v-for="(i, index) in tableForm.item">
        <el-table-column
          v-if="i.displaymark ? true : false"
          :key="index"
          :prop="i.itemcode"
          :column-key="i.itemcode"
          :label="i.itemname"
          :align="i.aligntype ? i.aligntype : 'center'"
          :min-width="i.minwidth"
          :show-overflow-tooltip="i.overflow ? true : false"
          :sortable="i.sortable ? true : false"
        >
          <template slot-scope="scope">
            <span v-if="i.itemcode == 'modifydate'">{{
              scope.row[i.itemcode] | dateFormat
            }}</span>
            <div v-else-if="i.itemcode == 'goodsuid'">
              <el-button
                type="text"
                size="small"
                @click="openFormEdit(scope.row)"
              >{{ scope.row.goodsuid || "货品编码" }}</el-button>
            </div>
            <span v-else>{{ scope.row[i.itemcode] }}</span>
          </template>
        </el-table-column>
      </template>
    </el-table>
  </div>
</template>

<script>
import { ParentBomList } from './tablecolums.js'
export default {
  components: {},
  props: ['dataList'],
  data() {
    return {
      listLoading: true,
      lst: [], //  列表数据
      total: 0,
      tableForm: ParentBomList
    }
  },
  methods: {
    bindData() {
      this.lst = [...this.dataList]
      this.listLoading = false
    },
    openFormEdit(row) {
      var obj = {
        goodsbomid: row.bomid,
        goodsid: row.id,
        goodsname: row.goodsname,
        goodsspec: row.goodsspec,
        goodsunit: row.goodsunit
      }
      this.$emit('editgoodsBom', obj)
      this.$nextTick(() => {
        this.$emit('closeBtn')
      })
    }
  }
}
</script>
  <style lang="scss" scoped>

  </style>
