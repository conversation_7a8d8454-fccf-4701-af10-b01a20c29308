<template>
  <div>
    <div :class="'filter-container flex j-s'">
      <div>
        <el-input
          v-model="strfilter"
          placeholder="请输入查询"
          prefix-icon="el-icon-search"
          style="width: 260px; height: 100%"
          class="filter-item"
          size="mini"
        >
          <el-button
            slot="append"
            class="filter-item"
            size="mini"
            @click="btnSearch"
          >搜索
          </el-button>
        </el-input>
        <el-radio-group
          v-model="goodsType"
          size="small"
          style="margin: 0 10px"
          @change="$emit('getStatus', goodsType)"
        >
          <el-radio-button label="物料" />
          <el-radio-button label="半成品" />
          <el-radio-button label="成品" />
        </el-radio-group>
        <el-button
          class="filter-item"
          type="primary"
          icon="el-icon-edit"
          plain
          size="mini"
          @click="$emit('btnAdd')"
        >
          添加
        </el-button>
        <el-button
          class="filter-item"
          style="margin-left: 10px"
          type="default"
          icon="el-icon-download"
          plain
          size="mini"
          @click="$emit('btnImport')"
        >
          单层导入
        </el-button>
      </div>

      <div class="iShowBtn">
        <el-button
          size="mini"
          icon="el-icon-search"
          title="高级筛选"
          :type="
            $store.state.advancedSearch.modulecode == tableForm.formcode
              ? 'primary'
              : 'default'
          "
          @click="openSearchForm()"
        />

        <el-button
          size="mini"
          icon="el-icon-refresh-right"
          title="刷新"
          @click="bindData"
        />
        <el-button
          size="mini"
          icon="el-icon-s-tools"
          @click="openDialog()"
        />
      </div>
    </div>
    <el-dialog
      v-if="setColumsVisible"
      width="800px"
      title="列设置"
      :append-to-body="true"
      :visible.sync="setColumsVisible"
      :close-on-click-modal="false"
      :destroy-on-close="true"
    >
      <SetColums
        ref="setcolums"
        :code="tableForm.formcode"
        :table-form="tableForm"
        @bindData="$emit('bindColumn')"
        @closeDialog="setColumsVisible = false"
      />
    </el-dialog>
    <el-dialog
      title="高级筛选"
      width="720px"
      :visible.sync="searchVisible"
      :append-to-body="true"
      :close-on-click-modal="false"
      :destroy-on-close="true"
    >
      <SearchForm
        ref="searchForm"
        :code="tableForm.formcode"
        @advancedSearch="advancedSearch"
        @closedDialog="searchVisible = false"
        @bindData="bindData"
      />
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: 'Listheader',
  components: { },
  props: ['tableForm'],
  data() {
    return {
      strfilter: '',
      iShow: false,
      formdata: {},
      setColumsVisible: false,
      goodsType: '成品',
      searchVisible: false,
      thorList: true
    }
  },
  methods: {
    openDialog() {
      this.setColumsVisible = true
    },
    submitUpdate() {
      this.$refs.setcolums.submitUpdate()
    },
    advancedSearch(searchdata) {
      this.$emit('advancedSearch', searchdata)
      this.searchVisible = false
    },
    openSearchForm() {
      this.searchVisible = true
      setTimeout(() => {
        this.$refs.searchForm.getInit()
      }, 100)
    },
    btnSearch() {
      this.$emit('btnSearch', this.strfilter)
    },
    bindData() {
      this.$emit('bindData')
    }
    // ==================================自定义================================
    // 切换显示单层或多层
  }
}
</script>
<style lang="scss" scoped>
.filter-container {
  margin: 0 10px;
  padding: 4px 10px;
  box-sizing: border-box;
  width: 98%;
  position: relative;
  align-items: flex-end;
}
.iShowBtn {
  margin-right: 10px;
  cursor: pointer;
  i:hover,
  i:active {
    color: #409eff;
  }
}
</style>
