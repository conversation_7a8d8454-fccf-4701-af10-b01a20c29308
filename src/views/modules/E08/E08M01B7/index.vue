<template>
  <div>
    <div v-if="formvisible == 'dan'" ref="formedit" class="formedit">
      <formedit
        ref="formedit"
        :idx="idx"
        :goods-info="goodsInfo"
        v-on="{
          compForm: compForm,
          closeForm: closeForm,
          changeIdx: changeIdx,
          bindData: bindData,
        }"
      />
    </div>
    <div v-if="formvisible == 'duo'" ref="formadd" class="formadd">
      <formadd
        ref="formadd"
        :idx="idx"
        :goods-info="goodsInfo"
        v-on="{
          compForm: compForm,
          closeForm: closeForm,
          changeIdx: changeIdx,
          bindData: bindData,
        }"
      />
    </div>
    <!-- index部分 -->
    <div v-show="!formvisible" ref="index" class="index">
      <listheader
        :table-form="tableForm"
        @btnAdd="showForm(0)"
        @btnSearch="search"
        @bindData="bindData"
        @getColumn="getColumn"
        @advancedSearch="advancedSearch"
        @getStatus="getStatus"
        @btnImport="btnImport"
      />
      <div class="page-container">
        <div
          class="flex"
          style="flex-direction: column; height: 100%; flex-wrap: nowrap"
        >
          <div style="flex: 1; width: 100%">
            <ve-table
              ref="tableTh"
              row-key-field-name="id"
              :max-height="240"
              :scroll-width="tableMinWidth"
              :style="{ 'word-break': 'break-all' }"
              is-horizontal-resize
              :fixed-header="true"
              :columns="customData"
              :table-data="lst"
              :column-hidden-option="{ defaultHiddenColumnKeys: columnHidden }"
              border-x
              border-y
              :border-around="true"
              :column-width-resize-option="columnWidthResizeOption"
              :virtual-scroll-option="virtualScrollOption"
              :event-custom-option="eventCustomOption"
              class="BomTableStyle"
            />
            <div v-show="total > 0" class="flex j-s a-c">
              <Pagination
                v-show="total > 0"
                :total="total"
                :page.sync="queryParams.PageNum"
                :limit.sync="queryParams.PageSize"
                :page-sizes="[10, 20, 100, 500]"
                @pagination="getList"
              />
              <div style="margin-right: 40px">
                <Scene
                  ref="scene"
                  :code="tableForm.formcode"
                  :baseparam="'/SaScene'"
                  @bindData="bindData"
                />
              </div>
            </div>
          </div>
          <!--  -->
          <div
            class="timeLine"
            :style="{ height: tableMaxHeight - 240 + 'px' }"
          >
            <!-- <div class="timeLine-header">Bom结构</div> -->
            <div v-if="itemdata.length" class="timeLine-content">
              <el-table
                ref="treeTable"
                :data="itemdata"
                element-loading-text="Loading"
                border
                fit
                highlight-current-row
                size="mini"
                class="tableBox"
                height="100%"
                :header-cell-style="{
                  background: '#F3F4F7',
                  color: '#555',
                  padding: '3px 0px 3px 0px',
                }"
                :row-style="{ height: '20px' }"
                row-key="id"
                :default-expand-all="true"
                :tree-props="{
                  children: 'children',
                  hasChildren: 'hasChildren',
                }"
              >
                <el-table-column
                  label="级数"
                  align="center"
                  min-width="50"
                  type=""
                >
                  <template slot-scope="scope">
                    <span>{{ scope.row.levelnum }}</span>
                  </template>
                </el-table-column>

                <el-table-column
                  label="货品编号"
                  align="center"
                  min-width="120"
                >
                  <template slot-scope="scope">
                    <!-- <span>{{ scope.row.goodsuid }}</span> -->
                    <!-- <el-button type="text" size="small" v-if="!scope.row.goodsbomid"
                        @click="addgoodsBom(scope.row)">{{ scope.row.goodsuid ||
                          "货品编码" }}</el-button>
                      <el-button type="text" size="small" v-else @click="editgoodsBom(scope.row)">{{ scope.row.goodsuid
                        ||
                        "货品编码" }}</el-button> -->

                    <div
                      v-if="!scope.row.goodsbomid"
                      style="display: inline-block"
                    >
                      <span>{{ scope.row.goodsuid }}</span>
                      <i
                        class="el-icon-plus iconStyle"
                        @click="addgoodsBom(scope.row)"
                      />
                    </div>
                    <el-button
                      v-else
                      type="text"
                      size="small"
                      @click="editgoodsBom(scope.row)"
                    >{{ scope.row.goodsuid || "货品编码" }}</el-button>
                  </template>
                </el-table-column>
                <el-table-column
                  label="货品名称"
                  align="center"
                  min-width="150"
                  show-overflow-tooltip
                >
                  <template slot-scope="scope">
                    <div
                      style="
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                      "
                    >
                      <span
                        style="width: calc(100% - 20px); overflow: hidden"
                      >{{ scope.row.goodsname }}</span>
                      <div class="svgIcon" @click="getParentBom(scope.row)">
                        <svg-icon
                          icon-class="D01M01B2"
                          color="#409eff"
                          style="width: 16px; height: 16px; margin-top: 4px"
                        />
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  label="货品规格"
                  align="center"
                  min-width="150"
                  show-overflow-tooltip
                >
                  <template slot-scope="scope">
                    <span>{{ scope.row.goodsspec }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  label="外部编码"
                  align="center"
                  min-width="80"
                  show-overflow-tooltip
                >
                  <template slot-scope="scope">
                    <span>{{ scope.row.partid }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  label="单位"
                  align="center"
                  min-width="60"
                  show-overflow-tooltip
                >
                  <template slot-scope="scope">
                    <span>{{ scope.row.goodsunit }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="配置属性" align="center" width="100">
                  <template slot-scope="scope">
                    <span>{{ scope.row.attrcode }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="站位" align="center" width="100">
                  <template slot-scope="scope">
                    <span>{{ scope.row.flowcode }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="主件数量" align="center" width="100">
                  <template slot-scope="scope">
                    <span>{{ scope.row.mainqty }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="配件数量" align="center" width="100">
                  <template slot-scope="scope">
                    <span>{{ scope.row.subqty }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="损耗率%" align="center" width="100">
                  <template slot-scope="scope">
                    <span>{{ scope.row.lossrate }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="备注" align="center" min-width="100">
                  <template slot-scope="scope">
                    <span>{{ scope.row.remark }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  label="物料类型"
                  align="center"
                  min-width="100"
                >
                  <template slot-scope="scope">
                    <span>{{
                      scope.row.isType ? scope.row.isType : "主料"
                    }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div v-else class="timeLine-noData">暂无数据</div>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      v-if="ExportVisible"
      title="导入文件"
      :visible.sync="ExportVisible"
      :append-to-body="true"
      :close-on-click-modal="false"
      width="600px"
    >
      <inks-export-view
        ref="exportView"
        :code="'D91M02B1'"
        :files-list="['groupname', 'groupid']"
        @saveExport="saveExport"
        @closeDialog="ExportVisible = false"
      />
    </el-dialog>
    <el-dialog
      v-if="parentBomVisible"
      :title="parentBomTitle"
      :visible.sync="parentBomVisible"
      :append-to-body="true"
      :close-on-click-modal="false"
      width="72vw"
    >
      <ParentBomList
        ref="ParentBomList"
        :data-list="parentBomList"
        @editgoodsBom="editgoodsBom"
        @closeBtn="parentBomVisible = false"
      />
    </el-dialog>
  </div>
</template>
<script>
import listheader from './components/listheader.vue'
import request from '@/utils/request'
import formedit from './components/formedit.vue'
import formadd from './components/formadd.vue'
import { tableTh, tableItem } from './components/tablecolums.js'
import mypopover from '@/components/mypopover/index'
import InksExportView from '@/components/common/export.vue'
import ParentBomList from './components/ParentBomList.vue'
export default {
  name: 'D91M02B1', // 修改区
  components: {
    listheader,
    formedit,
    formadd,
    mypopover,
    InksExportView,
    ParentBomList
  },
  data() {
    return {
      // index固定data
      microUrl: '',
      editEntity: null,
      windowParams: {
        key: 'D91M02B1-D91M02B1ED',
        page: {
          microMode: this.$ismicroapp,
          microEntry: '{microUrl}'
        },
        params: {
          pageType: 0, //  list
          openType: 0, //  modal
          lstp: {
            idx: '{idx}',
            select: '{goodsInfo}',
            parent: 'D91M02B1List',
            lifecode: 0
          }
        }
      },
      title: '结构清单',
      lst: [],
      formvisible: false,
      listLoading: false,
      idx: 0,
      total: 0,
      // 查询参数
      searchstr: '',
      queryParams: {
        PageNum: 1,
        PageSize: 20,
        OrderType: 1,
        SearchType: 1
      },
      refreshTable: false,
      isShowAll: false, // 是否展开所有行
      goodsInfo: {}, // 当点击行为新增boom时传给formedit的货品对象
      id: 0,
      itemdata: [],
      tableForm: tableTh,
      showHelp: false,
      mypopoverTable: tableItem,
      mypopoverData: [],
      mypopoverIndex: -1,
      //
      goodsVal: 'p',
      ExportVisible: false,
      selRow: {},
      parentBomTitle: '',
      parentBomVisible: false,
      parentBomList: [],
      // ======================
      rowStyleOption: {
        clickHighlight: false,
        hoverHighlight: false
      },
      columnHidden: [], // 列隐藏
      columnWidthResizeOption: {
        // 默认
        enable: false,
        minWidth: 20,
        sizeChange: ({ column, differWidth, columnWidth }) => {
          this.columnResizeInfo.column = column
          this.columnResizeInfo.differWidth = differWidth
          this.columnResizeInfo.columnWidth = columnWidth
        }
      },
      columnResizeInfo: {
        column: '',
        differWidth: '',
        columnWidth: ''
      },
      // 虚拟滚动
      virtualScrollOption: {
        enable: false
      },
      eventCustomOption: {
        bodyRowEvents: ({ row, rowIndex }) => {
          return {
            click: (event) => {
              this.checkRowItem(row)
            }
          }
        }
      },

      customData: [], // 表格标题
      // 获取选中单元格信息
      cellTotal: 0,
      cellNum: 0
    }
  },
  computed: {
    tableMaxHeight() {
      return window.innerHeight - 170
    },
    tableMinWidth() {
      var tableWidth = 'calc(100vw - 64px)'
      if (this.tableForm.item.length != 0) {
        tableWidth = 0
        for (var i = 0; i < this.tableForm.item.length; i++) {
          var Item = this.tableForm.item[i]
          if (Item.displaymark) {
            tableWidth += Number(Item.minwidth)
          }
        }
      }
      return tableWidth
    }
  },
  watch: {
    lst: function(val, oldval) {
      if (this.lst.length == 0) {
        return
      }
    }
  },
  mounted() {
    this.bindData()
    this.getColumn()
    this.$getMicroEntity('D91M02B1', 1).then(res => {
      this.editEntity = res
    })
  },
  methods: {
    bindData() {
      this.listLoading = true
      if (
        !!this.$refs.scene &&
        this.$refs.scene.radio != -1 &&
        this.total != 0
      ) {
        this.queryParams.scenedata = JSON.parse(
          this.$refs.scene.lst[this.$refs.scene.radio].scenedata
        )
      } else {
        if (this.queryParams.SearchType == 1) {
          this.$delete(this.queryParams, 'scenedata')
        }
        if (this.$refs.scene) {
          this.$refs.scene.radio = -1
        }
      }
      request
        .post(
          '/e08/E08M14B1/getPageTh?state=' + this.goodsVal,
          JSON.stringify(this.queryParams)
        )
        .then((response) => {
          if (response.data.code == 200) {
            this.lst = response.data.data.list
            this.total = response.data.data.total
          }
          this.listLoading = false
        })
        .catch((error) => {
          this.listLoading = false
        })
    },
    getColumn() {
      // this.$refs.tableTh.tableHeight=240;
      var colunmTh = tableTh
      this.$getColumn(this.tableForm.formcode, colunmTh).then((data) => {
        this.tableForm = Object.assign({}, data.colList) // 表头
        this.initTable(this.tableForm)
      })
    },
    initTable(data) {
      var customData = []
      this.columnHidden = []
      data['item'].forEach((item, index) => {
        var obj = {
          field: item.itemcode,
          key: item.itemcode,
          title: item.itemname,
          width: !isNaN(item.minwidth) ? item.minwidth + 'px' : item.minwidth,
          displaymark: item.displaymark,
          fixed: item.fixed ? (item.fixed == 1 ? 'left' : 'right') : false,
          ellipsis: item.overflow ? { showTitle: true } : false,
          align: item.aligntype ? item.aligntype : 'center',
          sortBy: item.sortable ? '' : false,
          renderBodyCell: ({ row, column, rowIndex }, h) => {
            if (item.itemcode == 'goodsuid') {
              var html = (
                <el-button
                  type='text'
                  size='small'
                  style='padding: 4px 15px'
                  on-click={() => this.showForm(row.id)}
                >
                  {row[item.itemcode] ? row[item.itemcode] : '货品编码'}
                </el-button>
              )
              return html
            } else if (item.itemcode == 'itemcount') {
              var html = ''
              html = (
                <el-popover
                  placement='left'
                  trigger='click'
                  ref="'popover-' + scope.$index"
                  title='单据明细'
                >
                  <div
                    style='position: relative; min-height: 100px'
                    v-show={rowIndex == this.mypopoverIndex}
                  >
                    <mypopover
                      style='width:860px;'
                      ref='mypopover'
                      tableForm={this.mypopoverTable}
                      lst={this.mypopoverData}
                    ></mypopover>
                  </div>
                  <span
                    slot='reference'
                    class='textunderline'
                    on-click={() => this.getBillList(row, rowIndex)}
                  >
                    {row[item.itemcode]}
                  </span>
                </el-popover>
              )
              return html
            } else {
              return row[item.itemcode]
            }
          }
        }
        if (!item.displaymark) {
          this.columnHidden.push(item.itemcode)
        }
        customData.push(obj)
      })
      customData.unshift({
        field: 'index',
        key: 'index',
        title: 'ID',
        width: '50px',
        align: 'center',
        fixed: 'left',
        renderBodyCell: ({ row, column, rowIndex }, h) => {
          return ++rowIndex
        }
      })
      this.customData = customData
      this.$forceUpdate()
    },
    getStatus(val) {
      this.itemdata = []
      if (val == '成品') {
        this.goodsVal = 'p'
      } else if (val == '半成品') {
        this.goodsVal = 's'
      } else if (val == '物料') {
        this.goodsVal = 'm'
      }
      this.bindData()
    },
    // ===================================table部分方法======================================
    //  点击行事件
    checkRowItem(row) {
      this.selRow = row
      this.itemdata = []
      request
        .get('/e08/E08M14B1/getBomDetail?key=' + row.id)
        .then((res) => {
          if (res.data.code == 200) {
            for (var i = 0; i < res.data.data.length; i++) {
              var Item = res.data.data[i]
              if (!Item.parentgoodsid) {
                // 判断是否为替代料
                Item.isType = '主料'
              }
              if (Item.parentgoodsid == Item.goodsid) {
                Item.isType = '替代料'
              }
            }
            this.itemdata = this.changeFormat(res.data.data)
          } else {
            this.$message.warning('获取Bom失败')
          }
        })
        .catch((err) => {
          this.$message.error('请求错误')
        })
    },

    // ===================================通用项目======================================
    // 分页组件事件
    getList(data) {
      this.queryParams.PageNum = data.page
      this.queryParams.PageSize = data.limit
      this.bindData()
    },
    // 查询
    search(val) {
      this.$search(this, val)
    },
    // 高级搜索
    advancedSearch(val) {
      this.$advancedSearch(this, val)
    },
    // 打开编码窗口
    async showForm(rowid, istrue) {
      this.idx = rowid
      if (istrue) {
        this.formvisible = 'duo'
      } else {
        if (this.$ismicroapp) {
          if (this.editEntity && this.editEntity.formentry) {
            this.microUrl = this.editEntity.formentry
            console.log('修改microEntity为', this.editEntity.formentry)
          } else {
            const origin = this.$getMicroEntrySer('E08')
            this.microUrl = `${origin}/E08/M01B7ED`
          }
          this.$openMicroWindow(this.windowParams)
          return
        }
        this.formvisible = 'dan'
      }
    },
    // 关闭对话框功能
    closeForm() {
      this.formvisible = false
      this.goodsInfo = {}
      if (this.selRow.id) {
        this.checkRowItem(this.selRow)
      }
    },
    // 完成对话框功能
    compForm() {
      this.bindData()
      this.formvisible = false
      this.goodsInfo = {}
    },
    changeIdx(val) {
      this.idx = val
    },
    // ==============================自定义方法===================
    addgoodsBom(data) {
      this.idx = 0
      this.formvisible = 'dan'
      this.goodsInfo = data
    },
    editgoodsBom(data) {
      console.log(data, 'sss')
      this.idx = data.goodsbomid
      this.formvisible = 'dan'
      this.goodsInfo = data
    },
    showAll() {
      this.isShowAll = !this.isShowAll
      if (this.isShowAll) {
        this.handleOpen()
      } else {
        this.handleClose()
      }
    },
    handleOpen() {
      // 展开
      this.refreshTable = false
      this.isShowAll = true
      this.$nextTick(() => {
        this.refreshTable = true
      })
    },
    handleClose() {
      // 收起
      this.refreshTable = false
      this.isShowAll = false
      this.$nextTick(() => {
        this.refreshTable = true
      })
    },
    changeFormat(data) {
      const result = []
      if (!Array.isArray(data)) {
        return result
      }
      data.forEach((item) => {
        delete item.children
      })
      const map = {}
      data.forEach((item) => {
        map[item.treeid] = item
      })
      data.forEach((item) => {
        const parent = map[item.treeparentid]
        if (parent) {
          (parent.children || (parent.children = [])).push(item)
        } else {
          result.push(item)
        }
      })
      return result
    },

    // ===================================工具项======================================
    getBillList(row, rowIndex) {
      this.mypopoverIndex = rowIndex
      request.get(`/E08M14B1/getBillEntity?key=${row.id}`).then((res) => {
        if (res.data.code == 200) {
          this.mypopoverData = res.data.data.item
          for (var i = 0; i < this.mypopoverData.length; i++) {
            var obj = this.mypopoverData[i]
            if (!obj.parentgoodsid) {
              // 判断是否为替代料
              obj.isType = '主料'
            } else {
              for (var j = 0; j < this.mypopoverData.length; j++) {
                if ((obj.parentgoodsid = this.mypopoverData[j].goodsid)) {
                  obj.isType = '替代料'
                }
              }
            }
          }
        } else {
          this.mypopoverData = []
        }
      })
    },
    getParentBom(row) {
      this.$request
        .get('/e08/E08M14B1/getParentBom?key=' + row.goodsid)
        .then((res) => {
          if (res.data.code == 200) {
            this.parentBomTitle = row.goodsname
            this.parentBomList = res.data.data
            this.parentBomVisible = true
            this.$nextTick(() => {
              this.$refs.ParentBomList.bindData()
            })
          } else {
            this.$message.warning(res.data.msg || '查询失败')
          }
        })
        .catch((er) => {
          this.$message.warning(er || '请求错误')
        })
    },
    btnImport() {
      this.ExportVisible = true
      this.$nextTick(() => {
        this.$refs.exportView.getExlModelData()
      })
    },
    saveExport(list) {
      var param = {
        item: []
      }
      try {
        for (let i = 0; i < list.length; i++) {
          var Item = list[i]
          // if (Item.hasOwnProperty("layer")) {
          //   this.$message.error("未识别到序列")
          //   return
          // }
          if (Item.layer == 0) {
            Item.enabledmark = 1
            param = Object.assign(param, Item)
          } else {
            param.item.push(Item)
          }
        }
        this.$request
          .post('/e08/E08M14B1/create', JSON.stringify(param))
          .then((res) => {
            if (res.data.code == 200) {
              this.$message.success('保存成功')
              this.ExportVisible = false
              this.bindData()
            } else {
              this.$message.error(res.data.message)
            }
          })
      } catch (error) {
        this.$message.error(error || '请求错误')
      }
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .BomTableStyle {
  .ve-table-container {
    height: 240px !important;
  }
}
.svgIcon {
  cursor: pointer;
  width: 20px;
}
::v-deep .textunderline {
  text-decoration: underline;
  cursor: pointer;
}

.tree-line {
  ::v-deep .el-icon-circle-plus-outline:before {
    color: #409eff;
  }

  ::v-deep .el-tree-node__expand-icon.is-leaf::before {
    display: none;
    /** 当控件没有子节点时让图标消失 **/
  }

  ::v-deep .el-tree-node {
    position: relative;
    padding-left: 16px; // 缩进量
  }

  ::v-deep.el-tree-node__children {
    padding-left: 16px; // 缩进量
  }

  // 竖线
  ::v-deep .el-tree-node::before {
    content: "";
    height: 100%;
    width: 1px;
    position: absolute;
    left: -3px;
    top: -26px;
    border-width: 1px;
    border-left: 1px dashed #52627c;
  }

  // 当前层最后一个节点的竖线高度固定
  ::v-deep .el-tree-node:last-child::before {
    height: 38px; // 可以自己调节到合适数值
  }

  // 横线
  ::v-deep .el-tree-node::after {
    content: "";
    width: 24px;
    height: 20px;
    position: absolute;
    left: -3px;
    top: 12px;
    border-width: 1px;
    border-top: 1px dashed #52627c;
  }

  // 去掉最顶层的虚线，放最下面样式才不会被上面的覆盖了
  & > ::v-deep .el-tree-node::after {
    border-top: none;
  }

  & > ::v-deep .el-tree-node::before {
    border-left: none;
  }

  // 展开关闭的icon
  ::v-deep .el-tree-node__expand-icon {
    font-size: 16px;

    // 叶子节点（无子节点）
    &::v-deep .is-leaf {
      color: transparent;
      // display: none; // 也可以去掉
    }
  }
}

.timeLine {
  border-top: 1px solid #ebeef5;
  border-bottom: 1px solid #ebeef5;
  border-right: 1px solid #ebeef5;
  position: relative;

  .timeLine-header {
    font-weight: bold;
    background: rgb(243, 244, 247);
    text-align: center;
    color: rgb(85, 85, 85);
    line-height: 30px;
    //  height: 30px;
    //margin-bottom: 10px;
  }

  .timeLine-content {
    padding: 10px;
    height: 100%;
    min-width: 750px;
    overflow-x: auto;
    // overflow-y: hidden;
  }

  .timeLine-noData {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 30px;
    color: #c0c4cc;
  }

  .timeLine-item {
    font-size: 14px;
    padding: 4px;
    p {
      font-weight: bold;

      span {
        font-weight: 400;
      }
    }
  }
}

.iconStyle {
  margin-left: 10px;
  color: #409eff;
  cursor: pointer;
}

.ellipsis {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-wrap: break-word;
}

//========================index table css=============================
.page-container {
  padding: 0;
  height: calc(100vh - 105px - 37px);
}
.ellipsis {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-wrap: break-word;
}
::v-deep .cab .el-scrollbar__wrap {
  overflow-x: hidden;
}

::v-deep .cab .is-horizontal {
  display: none;
}
</style>
