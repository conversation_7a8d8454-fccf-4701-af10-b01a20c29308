<template>
  <FormEdit
    v-if="visible"
    :idx="idx"
    :goods-info="goodsInfo"
  />
</template>
<script>
import FormEdit from './components/formedit.vue'
export default {
  name: 'EditIndex',
  components: { FormEdit },
  data() {
    return {
      idx: 0,
      goodsInfo: {},
      visible: true
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      if (this.$ismicroapp) {
        const lstp = window.$wujie.props.lstp || {}
        const { idx, select } = lstp
        if (idx) {
          this.idx = idx
        }
        if (select) {
          this.goodsInfo = select
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
