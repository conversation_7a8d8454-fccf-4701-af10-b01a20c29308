import request from '@/utils/request'
const CRUD = {
  // get user info 保存
  add(formdata) {
    return new Promise((resolve, reject) => {
      var params = JSON.stringify(formdata)
      request.post('/e08/E08M14B1/create', params)
        .then((response) => {
          if (response.data.code == 200) {
            resolve(response.data)
          } else {
            reject(response.data.msg)
          }
        })
        .catch((error) => {
          reject(error)
        })
    })
  },
  // 增加item
  addItem(formdata) {
    return new Promise((resolve, reject) => {
      var params = JSON.stringify(formdata)
      request.post('/e08/E08M14B1/createItem', params)
        .then((response) => {
          if (response.data.code == 200) {
            resolve(response.data)
          } else {
            reject(response.data.msg)
          }
        })
        .catch((error) => {
          reject(error)
        })
    })
  },
  // 更新添加
  update(formdata) {
    return new Promise((resolve, reject) => {
      var params = JSON.stringify(formdata)
      request

        .post('/e08/E08M14B1/update', params)
        .then((response) => {
          if (response.data.code == 200) {
            resolve(response.data)
          } else {
            reject(response.data.msg)
          }
        })
        .catch((error) => {
          reject(error)
        })
    })
  },
  // 主子表联删
  delete(idx) {
    return new Promise((resolve, reject) => {
      // 删除执行
      request.get(`/E08M14B1/delete?key=${idx}`)
        .then((response) => {
          if (response.data.code == 200) {
            resolve(response.data)
          } else {
            reject(response.data.msg)
          }
        })
        .catch((error) => {
          reject(error)
        })
    })
  },
  // 子表删除
  deleteItem(idx) {
    return new Promise((resolve, reject) => {
      // 删除执行
      request

        .get(`/E08M14B1/deleteItem?key=${idx}`)
        .then((response) => {
          if (response.data.code == 200) {
            resolve(response.data)
          } else {
            reject(response.data.msg)
          }
        })
        .catch((error) => {
          reject(error)
        })
    })
  },

  // 识别编码是否存在
  checkGoodsUid(rowdata) {
    return new Promise((resolve, reject) => {
      var params = JSON.stringify(rowdata)
      request
        .post('/e08/E08M14B1/getEntityBynsp', params)
        .then((response) => {
          if (response.data.code == 200) {
            resolve(response.data)
          } else {
            reject(response.data.msg)
          }
        })
        .catch((error) => {
          reject(error)
        })
    })
  },
  // 识别外部编码是否存在
  checkPartid(rowdata) {
    return new Promise((resolve, reject) => {
      request
        .get('/e08/E08M14B1/getEntityByPartid?key=' + rowdata)
        .then((response) => {
          if (response.data.code == 200) {
            resolve(response.data)
          } else {
            reject(response.data.msg)
          }
        })
        .catch((error) => {
          reject(error)
        })
    })
  }
}

export default CRUD
