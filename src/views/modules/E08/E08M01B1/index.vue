<template>
  <div class="dashboard">
    <div v-if="showSubDetail" class="project-body">
      <ProductDetail @closeForm="closeForm" />
    </div>
    <div v-show="!showSubDetail">
      <!-- 顶部统计卡片 -->
      <div class="stats-header">
        <h2>{{ $t("welcomeBack") }}</h2>
        <div class="stats-cards">
          <div class="stat-card">
            <div class="stat-icon blue">📄</div>
            <div class="stat-content">
              <div class="stat-label">{{ $t("Number of application projects") }}</div>
              <div class="stat-value">37 <span class="trend up" /></div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon green">📋</div>
            <div class="stat-content">
              <div class="stat-label">{{ $t("completed count") }}</div>
              <div class="stat-value">12 <span class="trend up" /></div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon orange">💰</div>
            <div class="stat-content">
              <div class="stat-label">{{ $t("To be quoted") }}</div>
              <div class="stat-value">10 <span class="trend up" /></div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon purple">📊</div>
            <div class="stat-content">
              <div class="stat-label">{{ $t("Pending approval") }}</div>
              <div class="stat-value">3 <span class="trend up" /></div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon red">⏸️</div>
            <div class="stat-content">
              <div class="stat-label">{{ $t("Discontinued") }}</div>
              <div class="stat-value">2 <span class="trend up" /></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 图表区域 -->
      <div class="charts-section">
        <div class="chart-container">
          <h3>{{ $t("Quote distribution") }}</h3>
          <div ref="barChart" class="chart" />
        </div>
        <div class="chart-container">
          <h3>{{ $t("Complete ratio") }}</h3>
          <div ref="pieChart" class="chart" />
        </div>
      </div>

      <!-- 最近清单 -->
      <div class="recent-section">
        <div class="section-header">
          <h3>{{ $t("Quote list") }}</h3>
          <div class="tabs">
            <span class="tab active">{{ $t("All") }}</span>
            <span class="tab">{{ $t("To be quoted") }}</span>
            <span class="tab">{{ $t("Pending approval") }}</span>
            <span class="tab">{{ $t("Completed") }}</span>
          </div>
          <div class="search-box">
            <input type="text" placeholder="">
            <button>{{ $t("search") }}</button>
          </div>
        </div>

        <div class="items-grid">
          <div v-for="i in 4" :key="i" class="item-card" @click="goToCostAnalysis">
            <div class="item-status completed">已完成</div>
            <div class="item-title">按摩仪(FL101001)</div>
            <div class="item-company">🏢 宏竹科技有限公司</div>
            <div class="item-detail">📍 嘉善县惠民街道102号二楼201</div>
            <div class="item-info">
              <span>📄 264个</span>
              <span>📅 2025-08-14 14:32:12</span>
            </div>
            <div class="item-version">V2</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import ProductDetail from '@/views/modules/E08/E08M01B3/index.vue'
import request from '@/utils/request'

export default {
  name: 'Index',
  components: { ProductDetail },
  data() {
    return {
      showSubDetail: false
    }
  },
  watch: {
    showSubDetail: {
      handler() {
        if (!this.showSubDetail) {
          this.$nextTick(() => {
            const barChart = echarts.getInstanceByDom(this.$refs.barChart)
            const pieChart = echarts.getInstanceByDom(this.$refs.pieChart)
            if (barChart) {
              const offsetWidth = this.$refs.barChart.offsetWidth
              const offsetHeight = this.$refs.barChart.offsetHeight
              barChart.resize(offsetWidth, offsetHeight)
            }
            if (pieChart) {
              const offsetWidth = this.$refs.pieChart.offsetWidth
              const offsetHeight = this.$refs.pieChart.offsetHeight
              pieChart.resize(offsetWidth, offsetHeight)
            }
          })
        }
      }
    }
  },
  mounted() {
    this.bindData()
    this.initCharts()
    const bus = window.$wujie.bus
    bus.$on('updateLanguage', (language) => {
      // 更新图表语言
      this.initCharts()
    })
  },
  methods: {
    bindData() {
      const queryParams = {
        PageNum: 1,
        PageSize: 10,
        OrderType: 1,
        SearchType: 1 // 高级搜索开启否 0为条件and   1为条件或者
      }
      request.post('/e08/E08M01B1/getPageList', JSON.stringify(queryParams)).then(res => {
        console.log(res)
      })
    },
    closeForm() {
      this.showSubDetail = false
    },
    goToCostAnalysis() {
      // 导航到成本分析页面
      this.showSubDetail = true
      // this.$router.push('/M01B4')
    },
    initCharts() {
      this.initBarChart()
      this.initPieChart()
    },
    initBarChart() {
      const chart = echarts.init(this.$refs.barChart)
      const option = {
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        tooltip: {},
        xAxis: {
          type: 'category',
          data: [this.$t('approval'), this.$t('quoted'), this.$t('cancel'), this.$t('discontinued'), this.$t('complete')],
          axisLine: { show: false },
          axisTick: { show: false }
        },
        yAxis: {
          type: 'value',
          axisLine: { show: false },
          axisTick: { show: false },
          splitLine: {
            lineStyle: { color: '#f0f0f0' }
          }
        },
        series: [{
          data: [3, 6, 4, 3.5, 7],
          type: 'bar',
          itemStyle: {
            color: '#2196F3',
            borderRadius: [4, 4, 0, 0]
          },
          barWidth: '40%'
        }]
      }
      chart.setOption(option)
    },
    initPieChart() {
      const chart = echarts.init(this.$refs.pieChart)
      const option = {
        series: [{
          type: 'pie',
          radius: ['40%', '60%'],
          center: ['50%', '45%'],
          data: [
            { value: 30, name: this.$t('Pending'), itemStyle: { color: '#E0E0E0' }},
            { value: 70, name: this.$t('Completed'), itemStyle: { color: '#4CAF50' }}
          ],
          label: { show: false },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }],
        tooltip: {},
        legend: {
          bottom: '10%',
          left: 'center',
          itemWidth: 8,
          itemHeight: 8,
          textStyle: {
            fontSize: 12,
            color: '#666'
          }
        }
      }
      chart.setOption(option)
    }
  }
}
</script>

<style scoped lang="scss">
.dashboard {
  padding: 10px;
  background: #f5f5f5;
  height: 100%;
}
.project-body {
  height: 100%;
}

.stats-header {
  h2 {
    margin-bottom: 20px;
    color: #333;
  }
}

.stats-cards {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  flex: 1;

  .stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;

    &.blue { background: #e3f2fd; }
    &.green { background: #e8f5e8; }
    &.orange { background: #fff3e0; }
    &.purple { background: #f3e5f5; }
    &.red { background: #ffebee; }
  }

  .stat-content {
    .stat-label {
      color: #666;
      font-size: 14px;
      margin-bottom: 5px;
    }
    .stat-value {
      font-size: 24px;
      font-weight: bold;
      color: #333;

      .trend {
        font-size: 16px;
        //color: #4caf50;
      }
    }
  }
}

.charts-section {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
}

.chart-container {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  flex: 1;

  h3 {
    margin-bottom: 20px;
    color: #333;
  }

  .chart {
    width: 100%;
    height: 200px;
  }
}

.recent-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;

  h3 {
    color: #333;
  }

  .tabs {
    display: flex;
    gap: 20px;

    .tab {
      padding: 8px 16px;
      cursor: pointer;
      color: #666;

      &.active {
        color: #2196f3;
        border-bottom: 2px solid #2196f3;
      }
    }
  }

  .search-box {
    display: flex;
    gap: 10px;

    input {
      padding: 8px 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
      width: 200px;
    }

    button {
      padding: 8px 16px;
      background: #2196f3;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
  }
}

.items-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.item-card {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 15px;
  position: relative;

  .item-status {
    position: absolute;
    top: 10px;
    right: 10px;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;

    &.completed {
      background: #e8f5e8;
      color: #4caf50;
    }
  }

  .item-title {
    font-weight: bold;
    margin-bottom: 10px;
    color: #333;
  }

  .item-company, .item-detail {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
  }

  .item-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
    font-size: 12px;
    color: #999;
  }
  .item-version {
    position: absolute;
    bottom: 10px;
    right: 10px;
    font-size: 24px;
    font-weight: bold;
    color: #333;
  }
}
</style>
