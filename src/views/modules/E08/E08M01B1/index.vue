<template>
  <div class="dashboard">
    <!-- 顶部统计卡片 -->
    <div class="stats-header">
      <h2>欢迎回来</h2>
      <div class="stats-cards">
        <div class="stat-card">
          <div class="stat-icon blue">📄</div>
          <div class="stat-content">
            <div class="stat-label">申请项目数</div>
            <div class="stat-value">37 <span class="trend up">个</span></div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon green">📋</div>
          <div class="stat-content">
            <div class="stat-label">完成数</div>
            <div class="stat-value">12 <span class="trend up">个</span></div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon orange">💰</div>
          <div class="stat-content">
            <div class="stat-label">待报价</div>
            <div class="stat-value">10 <span class="trend up">个</span></div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon purple">📊</div>
          <div class="stat-content">
            <div class="stat-label">待审批</div>
            <div class="stat-value">3 <span class="trend up">个</span></div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon red">⏸️</div>
          <div class="stat-content">
            <div class="stat-label">中止</div>
            <div class="stat-value">2 <span class="trend up">个</span></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <div class="chart-container">
        <h3>报价分布</h3>
        <div ref="barChart" class="chart"></div>
      </div>
      <div class="chart-container">
        <h3>完成比</h3>
        <div ref="pieChart" class="chart"></div>
      </div>
    </div>

    <!-- 最近清单 -->
    <div class="recent-section">
      <div class="section-header">
        <h3>报价清单</h3>
        <div class="tabs">
          <span class="tab active">全部</span>
          <span class="tab">待报价</span>
          <span class="tab">待审批</span>
          <span class="tab">已完成</span>
        </div>
        <div class="search-box">
          <input type="text" placeholder="请输入关键词" />
          <button>搜索</button>
        </div>
      </div>

      <div class="items-grid">
        <div class="item-card" v-for="i in 4" :key="i">
          <div class="item-status completed">已完成</div>
          <div class="item-title">按摩仪(FL101001)</div>
          <div class="item-company">🏢 宏竹科技有限公司</div>
          <div class="item-detail">📍 嘉善县惠民街道102号二楼201</div>
          <div class="item-info">
            <span>📄 264个</span>
            <span>📅 2025-08-14 14:32:12</span>
          </div>
          <div class="item-version">V2</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'Index',
  mounted() {
    this.initCharts()
  },
  methods: {
    initCharts() {
      this.initBarChart()
      this.initPieChart()
    },
    initBarChart() {
      const chart = echarts.init(this.$refs.barChart)
      const option = {
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        tooltip: {},
        xAxis: {
          type: 'category',
          data: ['审批', '报价', '取消', '中止', '完成'],
          axisLine: { show: false },
          axisTick: { show: false }
        },
        yAxis: {
          type: 'value',
          axisLine: { show: false },
          axisTick: { show: false },
          splitLine: {
            lineStyle: { color: '#f0f0f0' }
          }
        },
        series: [{
          data: [3, 6, 4, 3.5, 7],
          type: 'bar',
          itemStyle: {
            color: '#2196F3',
            borderRadius: [4, 4, 0, 0]
          },
          barWidth: '40%'
        }]
      }
      chart.setOption(option)
    },
    initPieChart() {
      const chart = echarts.init(this.$refs.pieChart)
      const option = {
        series: [{
          type: 'pie',
          radius: ['40%', '60%'],
          center: ['50%', '45%'],
          data: [
            { value: 30, name: '未完成', itemStyle: { color: '#E0E0E0' }},
            { value: 70, name: '已完成', itemStyle: { color: '#4CAF50' }}
          ],
          label: { show: false },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }],
        tooltip: {},
        legend: {
          bottom: '10%',
          left: 'center',
          itemWidth: 8,
          itemHeight: 8,
          textStyle: {
            fontSize: 12,
            color: '#666'
          }
        }
      }
      chart.setOption(option)
    }
  }
}
</script>

<style scoped lang="scss">
.dashboard {
  padding: 10px;
  background: #f5f5f5;
  height: 100%;
}

.stats-header {
  h2 {
    margin-bottom: 20px;
    color: #333;
  }
}

.stats-cards {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  flex: 1;

  .stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;

    &.blue { background: #e3f2fd; }
    &.green { background: #e8f5e8; }
    &.orange { background: #fff3e0; }
    &.purple { background: #f3e5f5; }
    &.red { background: #ffebee; }
  }

  .stat-content {
    .stat-label {
      color: #666;
      font-size: 14px;
      margin-bottom: 5px;
    }
    .stat-value {
      font-size: 24px;
      font-weight: bold;
      color: #333;

      .trend {
        font-size: 16px;
        //color: #4caf50;
      }
    }
  }
}

.charts-section {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
}

.chart-container {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  flex: 1;

  h3 {
    margin-bottom: 20px;
    color: #333;
  }

  .chart {
    width: 100%;
    height: 200px;
  }
}

.recent-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;

  h3 {
    color: #333;
  }

  .tabs {
    display: flex;
    gap: 20px;

    .tab {
      padding: 8px 16px;
      cursor: pointer;
      color: #666;

      &.active {
        color: #2196f3;
        border-bottom: 2px solid #2196f3;
      }
    }
  }

  .search-box {
    display: flex;
    gap: 10px;

    input {
      padding: 8px 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
      width: 200px;
    }

    button {
      padding: 8px 16px;
      background: #2196f3;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
  }
}

.items-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.item-card {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 15px;
  position: relative;

  .item-status {
    position: absolute;
    top: 10px;
    right: 10px;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;

    &.completed {
      background: #e8f5e8;
      color: #4caf50;
    }
  }

  .item-title {
    font-weight: bold;
    margin-bottom: 10px;
    color: #333;
  }

  .item-company, .item-detail {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
  }

  .item-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
    font-size: 12px;
    color: #999;
  }
  .item-version {
    position: absolute;
    bottom: 10px;
    right: 10px;
    font-size: 24px;
    font-weight: bold;
    color: #333;
  }
}
</style>
