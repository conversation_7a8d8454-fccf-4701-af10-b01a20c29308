<template>
  <div>
    <div :class="'filter-container flex j-s a-c'">
      <div style="display: flex">
        <div class="flex infoForm a-c">
          <span class="infoForm-Title">时间范围</span>
          <range-picker :date-range.sync="dateRange" />
        </div>
        <el-input
          v-model="strfilter"
          placeholder="请输入查询"
          prefix-icon="el-icon-search"
          style="width: 260px; height: 100%"
          class="filter-item"
          size="mini"
          @keyup.enter.native="btnSearch"
        >
          <el-button
            slot="append"
            class="filter-item"
            size="mini"
            @click="btnSearch"
          >搜索
          </el-button>
        </el-input>

        <el-button
          class="filter-item"
          style="margin-left: 10px"
          type="primary"
          icon="el-icon-plus"
          plain
          size="mini"
          @click="btnAdd"
        >
          添加
        </el-button>
        <!-- <el-button
          v-show="!thorList"
          class="filter-item"
          style="margin-left: 10px"
          type="primary"
          icon="el-icon-printer"
          plain
          size="mini"
          @click="$emit('btnPrintList')"
        >
          打印
        </el-button> -->
      </div>
      <div class="iShowBtn">
        <el-button
          size="mini"
          icon="el-icon-search"
          title="高级筛选"
          type="default"
          @click="openSearchForm()"
        />
        <el-button
          size="mini"
          icon="el-icon-refresh-right"
          @click="bindData"
        />
        <el-button
          size="mini"
          icon="el-icon-download"
          title="导出Excel"
          @click="btnExport"
        />
        <!-- <el-button
          size="mini"
          icon="el-icon-printer"
          @click="$emit('pagePrint')"
          title="打印列表"
        ></el-button>
        <el-button
        v-show="thorList"
          size="mini"
          icon="el-icon-document"
          @click="$emit('billPrint')"
          title="打印单据"
        ></el-button> -->
        <el-button
          size="mini"
          icon="el-icon-s-tools"
          @click="setColumsVisible = true"
        />
        <el-button
          size="mini"
          title="帮助"
          icon="el-icon-s-help"
          @click="$emit('btnHelp')"
        />
      </div>
    </div>
    <el-dialog
      v-if="setColumsVisible"
      width="800px"
      title="列设置"
      :append-to-body="true"
      :visible.sync="setColumsVisible"
      :close-on-click-modal="false"
      :destroy-on-close="true"
    >
      <SetColums
        v-if="false"
        ref="setcolums"
        :code="tableForm.formcode"
        :table-form="tableForm"
        @bindData="$emit('bindColumn')"
        @closeDialog="setColumsVisible = false"
      />
    </el-dialog>
    <el-dialog
      title="高级筛选"
      width="720px"
      :visible.sync="searchVisible"
      :append-to-body="true"
      :close-on-click-modal="false"
      :destroy-on-close="true"
    >
      <SearchForm
        v-if="false"
        ref="searchForm"
        :code="tableForm.formcode"
        @advancedSearch="advancedSearch"
        @closedDialog="searchVisible = false"
        @bindData="bindData"
      />
    </el-dialog>
  </div>
</template>
<script>
import { getDate, pickerOptions } from '@/utils/tools.js'
export default {
  name: 'Listheader',
  props: ['tableForm'],
  data() {
    return {
      strfilter: '',
      formdata: {},
      dateRange: getDate(),
      pickerOptions: pickerOptions(),
      thorList: true,
      balance: false,
      setColumsVisible: false,
      searchVisible: false
    }
  },
  methods: {
    advancedSearch(searchdata) {
      var val = {
        dateRange: this.dateRange,
        formdata: searchdata
      }
      console.log('val', val)
      this.$emit('advancedSearch', val)
      this.searchVisible = false
    },
    openSearchForm() {
      this.searchVisible = true
      setTimeout(() => {
        this.$refs.searchForm.getInit()
      }, 100)
    },
    btnAdd() {
      this.$emit('btnAdd')
    },
    btnSearch() {
      var val = {
        dateRange: this.dateRange,
        strfilter: this.strfilter
      }
      this.$emit('btnSearch', val)
    },
    bindData() {
      this.$emit('bindData')
    },
    btnExport() {
      this.$emit('btnExport')
    },
    changeModelUrl() {
      this.$emit('changeModelUrl', this.thorList)
      this.$emit('bindColumn')
    },

    changeBalance(val) {
      var balaceVal = 0
      if (val) {
        balaceVal = 1
      } else {
        balaceVal = 0
      }
      this.$emit('changeBalance', balaceVal)
    }
  }
}
</script>
<style lang="scss" scoped>
.infoForm {
  .infoForm-Title {
    color: #666;
    font-size: 14px;
    min-width: 50px;
    text-align: right;
    margin: 0 10px;
  }
}
.filter-container {
  margin: 0 10px;
  padding: 4px 10px;
  box-sizing: border-box;
  width: 98%;
  position: relative;
  align-items: flex-end;
}
.iShowBtn {
  margin-right: 10px;
}
</style>
