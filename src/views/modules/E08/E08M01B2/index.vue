<template>
  <div class="project-list">
    <ListHeader @btnAdd="openCreateDialog" />

    <div class="container">
      <!-- 项目卡片列表 -->
      <div class="items-grid">
        <div v-for="(item, index) in projectList" :key="index" class="item-card">
          <div class="item-status completed">{{ item.status }}</div>
          <div class="item-title">{{ item.title }}</div>
          <div class="item-company">🏢 {{ item.company }}</div>
          <div class="item-detail">📍 {{ item.address }}</div>
          <div class="item-info">
            <span>📄 {{ item.count }}</span>
            <span>📅 {{ item.date }}</span>
          </div>
          <div class="item-version">{{ item.version }}</div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    <!-- 创建项目弹窗 -->
    <el-dialog
      title="创建项目"
      :visible.sync="createDialogVisible"
      width="800px"
      :close-on-click-modal="false"
    >

      <!-- 步骤条 -->
      <div class="steps-container">
        <div class="step" :class="{ active: currentStep === 1 }">
          <div class="step-number">1</div>
          <div class="step-title">产品信息</div>
        </div>
        <div class="step" :class="{ active: currentStep === 2 }">
          <div class="step-number">2</div>
          <div class="step-title">产品附图</div>
        </div>
        <div class="step" :class="{ active: currentStep === 3 }">
          <div class="step-number">3</div>
          <div class="step-title">完成创建</div>
        </div>
      </div>

      <!-- 步骤1：产品信息 -->
      <div v-if="currentStep === 1" class="step-content">
        <el-form :model="productForm" label-width="100px" class="product-form">
          <el-form-item label="产品名称" required>
            <el-input v-model="productForm.name" placeholder="请输入产品名称" />
          </el-form-item>
          <el-form-item label="产品品牌">
            <el-input v-model="productForm.brand" placeholder="请输入产品品牌" />
          </el-form-item>
          <el-form-item label="生产厂家">
            <el-input v-model="productForm.manufacturer" placeholder="请输入生产厂家" />
          </el-form-item>
          <el-form-item label="CBD版本">
            <el-input v-model="productForm.cbdVersion" placeholder="请输入CBD版本" />
          </el-form-item>
          <el-form-item label="供应商" required>
            <el-select v-model="productForm.supplier" placeholder="请选择供应商" style="width: 100%;">
              <el-option label="供应商A" value="supplierA" />
              <el-option label="供应商B" value="supplierB" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <!-- 步骤2：产品附图 -->
      <div v-if="currentStep === 2" class="step-content">
        <div class="upload-section">
          <div v-for="i in 3" :key="i" class="upload-item">
            <div class="upload-box">
              <i class="el-icon-plus" />
            </div>
          </div>
        </div>
      </div>

      <!-- 步骤3：完成创建 -->
      <div v-if="currentStep === 3" class="step-content">
        <div class="success-content">
          <i class="el-icon-success" style="font-size: 48px; color: #67c23a;" />
          <p>项目创建成功！</p>
        </div>
      </div>

      <!-- 弹窗底部按钮 -->
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button v-if="currentStep < 3" type="primary" @click="nextStep">下一步</el-button>
        <el-button v-if="currentStep === 3" type="primary" @click="handleFinish">完成</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ListHeader from './components/listHeader.vue'
export default {
  name: 'Index',
  components: {
    ListHeader
  },
  data() {
    return {
      dateRange: ['2025-08-05', '2025-08-05'],
      searchKeyword: '',
      currentPage: 1,
      pageSize: 20,
      total: 4,
      createDialogVisible: false,
      currentStep: 1,
      productForm: {
        name: '',
        brand: '',
        manufacturer: '',
        cbdVersion: '',
        supplier: ''
      },
      projectList: [
        {
          title: '按摩仪(FL101001)',
          company: '宏竹科技有限公司',
          address: '嘉善县惠民街道102号二楼201',
          count: '264个',
          date: '2025-08-14 14:32:12',
          version: 'V2',
          status: '已完成',
          statusClass: 'completed'
        },
        {
          title: '按摩仪(FL101001)',
          company: '宏竹科技有限公司',
          address: '嘉善县惠民街道102号二楼201',
          count: '264个',
          date: '2025-08-14 14:32:12',
          version: 'V2',
          status: '已完成',
          statusClass: 'completed'
        },
        {
          title: '按摩仪(FL101001)',
          company: '宏竹科技有限公司',
          address: '嘉善县惠民街道102号二楼201',
          count: '264个',
          date: '2025-08-14 14:32:12',
          version: 'V2',
          status: '已完成',
          statusClass: 'completed'
        },
        {
          title: '按摩仪(FL101001)',
          company: '宏竹科技有限公司',
          address: '嘉善县惠民街道102号二楼201',
          count: '264个',
          date: '2025-08-14 14:32:12',
          version: 'V2',
          status: '已完成',
          statusClass: 'completed'
        },
        {
          title: '按摩仪(FL101001)',
          company: '宏竹科技有限公司',
          address: '嘉善县惠民街道102号二楼201',
          count: '264个',
          date: '2025-08-14 14:32:12',
          version: 'V2',
          status: '已完成',
          statusClass: 'completed'
        },
        {
          title: '按摩仪(FL101001)',
          company: '宏竹科技有限公司',
          address: '嘉善县惠民街道102号二楼201',
          count: '264个',
          date: '2025-08-14 14:32:12',
          version: 'V2',
          status: '已完成',
          statusClass: 'completed'
        }
      ]
    }
  },
  methods: {
    handleSizeChange(val) {
      this.pageSize = val
    },
    handleCurrentChange(val) {
      this.currentPage = val
    },
    openCreateDialog() {
      this.createDialogVisible = true
      this.currentStep = 1
      this.resetForm()
    },
    nextStep() {
      if (this.currentStep < 3) {
        this.currentStep++
      }
    },
    handleCancel() {
      this.createDialogVisible = false
      this.resetForm()
    },
    handleFinish() {
      this.createDialogVisible = false
      this.resetForm()
      this.$message.success('项目创建成功！')
    },
    resetForm() {
      this.productForm = {
        name: '',
        brand: '',
        manufacturer: '',
        cbdVersion: '',
        supplier: ''
      }
    }
  }
}
</script>

<style scoped lang="scss">
.project-list {
  padding: 0;
  background: #f5f5f5;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.container {
  padding: 0 10px 10px 10px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.filter-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 20px;

  .filter-item {
    display: flex;
    align-items: center;
    gap: 8px;

    label {
      color: #333;
      font-size: 14px;
      white-space: nowrap;
    }
  }
}

.items-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 30px;
}

.item-card {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 15px;
  position: relative;
  background: white;

  .item-status {
    position: absolute;
    top: 10px;
    right: 10px;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;

    &.completed {
      background: #e8f5e8;
      color: #4caf50;
    }
  }

  .item-title {
    font-weight: bold;
    margin-bottom: 10px;
    color: #333;
  }

  .item-company, .item-detail {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
  }

  .item-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
    font-size: 12px;
    color: #999;
  }

  .item-version {
    position: absolute;
    bottom: 10px;
    right: 10px;
    font-size: 24px;
    font-weight: bold;
    color: #333;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .items-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 900px) {
  .items-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 600px) {
  .items-grid {
    grid-template-columns: 1fr;
  }
}

.pagination-wrapper {
  background: white;
  padding: 10px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  display: flex;
  //justify-content: center;
}

// 弹窗样式
.steps-container {
  display: flex;
  justify-content: center;
  margin-bottom: 40px;

  .step {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 40px;

    .step-number {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: #e0e0e0;
      color: #999;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      margin-bottom: 8px;
    }

    .step-title {
      font-size: 14px;
      color: #666;
    }

    &.active {
      .step-number {
        background: #409eff;
        color: white;
      }

      .step-title {
        color: #409eff;
      }
    }
  }
}

.step-content {
  min-height: 300px;

  .product-form {
    max-width: 500px;
    margin: 0 auto;
  }

  .upload-section {
    display: flex;
    justify-content: center;
    gap: 20px;

    .upload-box {
      width: 120px;
      height: 120px;
      border: 2px dashed #ddd;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;

      &:hover {
        border-color: #409eff;
      }

      i {
        font-size: 24px;
        color: #999;
      }
    }
  }

  .success-content {
    text-align: center;
    padding: 40px 0;

    p {
      margin-top: 20px;
      font-size: 16px;
      color: #333;
    }
  }
}

.dialog-footer {
  text-align: center;
}
</style>
