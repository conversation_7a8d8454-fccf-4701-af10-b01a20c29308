<template>
  <div class="product-detail">
    <!-- 产品头部信息 -->
    <div class="product-header">
      <div class="product-image">
        <img src="@/assets/product-placeholder.svg" alt="产品图片" />
      </div>
      <div class="product-info">
        <h1 class="product-title">按摩仪(FL10121)</h1>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">供应商:</span>
            <span class="value">宏竹科技有限公司</span>
          </div>
          <div class="info-item">
            <span class="label">品牌:</span>
            <span class="value">FED12556</span>
          </div>
          <div class="info-item">
            <span class="label">CBD版本:</span>
            <span class="value">V2</span>
          </div>
          <div class="info-item">
            <span class="label">主要货币:</span>
            <span class="value">人民币</span>
          </div>
          <div class="info-item">
            <span class="label">生产地:</span>
            <span class="value"></span>
          </div>
        </div>
      </div>
    </div>

    <!-- CBD版本详情表格 -->
    <div class="version-details">
      <h2 class="section-title">CBD版本详情</h2>
      <ve-table
        :columns="tableColumns"
        :table-data="tableData"
        :border-around="true"
        :border-x="true"
        :border-y="true"
        :row-style-option="rowStyleOption"
        :cell-style-option="cellStyleOption"
        :max-height="400"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'ProductDetail',
  data() {
    return {
      tableColumns: [
        {
          field: 'version',
          title: 'CBD版本',
          width: 100,
          align: 'center'
        },
        {
          field: 'updateInfo',
          title: 'CBD主要更新',
          width: 400,
          align: 'left'
        },
        {
          field: 'price',
          title: '出口EXW(人民币)',
          width: 150,
          align: 'center'
        },
        {
          field: 'quarter',
          title: '制作',
          width: 100,
          align: 'center'
        },
        {
          field: 'progress',
          title: '进度',
          width: 150,
          align: 'center',
          renderBodyCell: ({ row, column, rowIndex }, h) => {
            return (
              <div class="progress-container">
                <div class="progress-bar">
                  <div
                    class="progress-fill"
                    style={{
                      width: `${row.progress}%`,
                      backgroundColor: row.progressColor
                    }}
                  ></div>
                </div>
                <span class="progress-text">{row.progress}%</span>
              </div>
            )
          }
        },
        {
          field: 'action',
          title: '操作',
          width: 100,
          align: 'center',
          renderBodyCell: ({ row, column, rowIndex }, h) => {
            return (
              <a
                class="action-btn"
                onClick={() => this.handleAction(row)}
              >
                详情
              </a>
            )
          }
        }
      ],
      tableData: [
        {
          version: 'V2',
          updateInfo: 'Re-schedule the mold structure based on DFM, and Holee self-assemble the battery, both of the two proposals make the EXW cost down from CNY 125.304/pc, so the EXW price is CNY 125.74* transportation cost to Ningbo warehouse CNY 0.60 = total unit price CNY 125.74/pc (without VAT). Mold cost is adjusted from CNY 219,600 to CNY 216,100, cost down CNY 3,500.-',
          price: '147.992',
          quarter: '季四',
          progress: 60,
          progressColor: '#ff9500',
          action: '详情'
        },
        {
          version: 'V1',
          updateInfo: 'According to Diego\'s request, remove USB-C cable from orbital massager, so the EXW price is cost down to CNY 129.044 + transportation cost to Ningbo warehouse CNY 0.60 = total unit price CNY 129.64/pc (without VAT). Mold cost keep the same as V3, please refer below information.',
          price: '129.044',
          quarter: '季四',
          progress: 60,
          progressColor: '#52c41a',
          action: '详情'
        }
      ],
      rowStyleOption: {
        stripe: true,
        hoverHighlight: true
      },
      cellStyleOption: {
        bodyCellClass: ({ row, column, rowIndex }) => {
          return 'custom-cell'
        }
      }
    }
  },
  methods: {
    handleAction(row) {
      console.log('查看详情:', row)
      // 这里可以添加查看详情的逻辑
    }
  }
}
</script>

<style lang="scss" scoped>
.product-detail {
  padding: 20px;
  background: #fff;
  min-height: 100vh;
}

.product-header {
  display: flex;
  gap: 30px;
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background: #fafafa;
}

.product-image {
  flex-shrink: 0;
  width: 120px;
  height: 120px;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
    background: #f0f0f0;
  }
}

.product-info {
  flex: 1;
}

.product-title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin: 0 0 20px 0;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.info-item {
  display: flex;
  align-items: center;

  .label {
    font-weight: 500;
    color: #666;
    margin-right: 10px;
    min-width: 80px;
  }

  .value {
    color: #333;
    font-weight: 400;
  }
}

.version-details {
  .section-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin-bottom: 15px;
  }
}

// vue-easytable 自定义样式
:deep(.ve-table) {
  .ve-table-header-tr {
    background: #f5f5f5;

    th {
      font-weight: 600;
      color: #333;
      border-right: 1px solid #e8e8e8;
    }
  }

  .ve-table-body-tr {
    &:hover {
      background: #f9f9f9;
    }

    td {
      border-right: 1px solid #e8e8e8;
      padding: 12px 8px;

      &.custom-cell {
        vertical-align: top;
      }
    }
  }
}

// 进度条样式
.progress-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-bar {
  flex: 1;
  height: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;

  .progress-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 0.3s ease;
  }
}

.progress-text {
  font-size: 12px;
  color: #666;
  min-width: 30px;
}

.action-btn {
  color: #1890ff;
  cursor: pointer;
  text-decoration: none;

  &:hover {
    text-decoration: underline;
  }
}
</style>
