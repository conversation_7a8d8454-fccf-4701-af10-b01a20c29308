<template>
  <div class="product-detail">
    <div class="button-container p-a" style="top: 0px; right: 30px">
      <EditGroupBtns
        ref="EditGroupBtns"
        :showcontent="[]"
        :formdata="{}"
        :operate-bar="[]"
        :process-bar="[]"
        @closeForm="closeForm"
      />
    </div>
    <!-- 产品头部信息 -->
    <div class="product-header">
      <div class="product-image">
        <img src="@/assets/product/product-placeholder.png" alt="产品图片">
      </div>
      <div class="product-info">
        <h1 class="product-title">按摩仪(FL10121)</h1>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">供应商:</span>
            <span class="value">宏竹科技有限公司</span>
          </div>
          <div class="info-item">
            <span class="label">品牌:</span>
            <span class="value">FED12556</span>
          </div>
          <div class="info-item">
            <span class="label">CBD版本:</span>
            <span class="value">V2</span>
          </div>
          <div class="info-item">
            <span class="label">主要货币:</span>
            <span class="value">人民币</span>
          </div>
          <div class="info-item">
            <span class="label">生产地:</span>
            <span class="value" />
          </div>
        </div>
      </div>
    </div>

    <!-- CBD版本详情表格 -->
    <div class="version-details">
      <h2 class="section-title">CBD版本详情</h2>
      <ve-table
        :key="keynum"
        border-x
        border-y
        :border-around="true"
        :columns="tableColumns"
        :table-data="tableData"
      />
    </div>

    <el-dialog
      v-if="detailVisible"
      title="报价详情"
      :append-to-body="true"
      top="5vh"
      :visible.sync="detailVisible"
      width="1500px"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <DetailProgress />
    </el-dialog>
    <el-dialog
      v-if="progressVisible"
      title="报价进度"
      :append-to-body="true"
      top="5vh"
      :visible.sync="progressVisible"
      width="1500px"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <ProgressProgress />
    </el-dialog>
  </div>
</template>

<script>
import DetailProgress from '@/views/modules/E08/E08M01B4/index.vue'
import ProgressProgress from '@/views/modules/E08/E08M01B5/index.vue'
export default {
  name: 'ProductDetail',
  components: { DetailProgress, ProgressProgress },
  data() {
    return {
      detailVisible: false,
      progressVisible: false,
      keynum: 0,
      tableColumns: [
        {
          field: 'version',
          key: 'version',
          title: 'CBD版本',
          width: 100,
          align: 'center'
        },
        {
          field: 'updateInfo',
          key: 'updateInfo',
          title: 'CBD主要更新',
          width: 400,
          align: 'left'
        },
        {
          field: 'price',
          key: 'price',
          title: '出口EXW(人民币)',
          width: 150,
          align: 'center'
        },
        {
          field: 'quarter',
          key: 'quarter',
          title: '制作',
          width: 100,
          align: 'center'
        },
        {
          field: 'progress',
          key: 'progress',
          title: '进度',
          width: 150,
          align: 'center'
        },
        {
          field: 'action',
          key: 'action',
          title: '操作',
          width: 100,
          align: 'center'
        }
      ],
      tableData: [
        {
          version: 'V2',
          updateInfo: 'Re-schedule the mold structure based on DFM, and Holee self-assemble the battery, both of the two proposals make the EXW cost down from CNY 125.304/pc, so the EXW price is CNY 125.74* transportation cost to Ningbo warehouse CNY 0.60 = total unit price CNY 125.74/pc (without VAT). Mold cost is adjusted from CNY 219,600 to CNY 216,100, cost down CNY 3,500.-',
          price: '147.992',
          quarter: '李四',
          progress: 60,
          progressColor: '#ff9500',
          action: '详情'
        },
        {
          version: 'V1',
          updateInfo: 'According to Diego\'s request, remove USB-C cable from orbital massager, so the EXW price is cost down to CNY 129.044 + transportation cost to Ningbo warehouse CNY 0.60 = total unit price CNY 129.64/pc (without VAT). Mold cost keep the same as V3, please refer below information.',
          price: '129.044',
          quarter: '李四',
          progress: 60,
          progressColor: '#52c41a',
          action: '详情'
        }
      ],
      rowStyleOption: {
        stripe: true,
        hoverHighlight: true
      },
      cellStyleOption: {
        bodyCellClass: ({ row, column, rowIndex }) => {
          return 'custom-cell'
        }
      }
    }
  },
  created() {
    this.tableColumns = this.tableColumns.map(item => {
      return {
        ...item,
        renderBodyCell: ({ row, column, rowIndex }, h) => {
          if (column.field === 'progress') {
            const html = (
              <div on-click={() => this.showProgress()} style='padding: 0 20px'>
                <el-progress text-inside={true} stroke-width={20} percentage={row[column.field]}></el-progress>
              </div>
            )
            return html
          } else if (column.field === 'action') {
            const html = (
              <div>
                <el-button type='text' on-click={() => this.showDetail()}>
                  详情
                </el-button>
              </div>
            )
            return html
          } else {
            return row[column.field]
          }
        }
      }
    })
    this.keynum++
  },
  methods: {
    showDetail() {
      this.detailVisible = true
    },
    showProgress() {
      this.progressVisible = true
    },
    closeForm() {
      if (this.$ismicroapp) {
        const { close } = window.$wujie.props
        // 状态为弹窗的场景下执行关闭弹窗
        if (close) {
          close()
          console.log('v-window 弹窗环境 关闭弹窗')
          return
        }
      }
      this.$emit('closeForm')
    },
    handleAction(row) {
      console.log('查看详情:', row)
      // 这里可以添加查看详情的逻辑
    }
  }
}
</script>

<style lang="scss" scoped>
.product-detail {
  padding: 20px;
  background: #fff;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.product-header {
  display: flex;
  gap: 30px;
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background: #fafafa;
}

.product-image {
  flex-shrink: 0;
  width: 120px;
  height: 120px;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
    background: #f0f0f0;
  }
}

.product-info {
  flex: 1;
}

.product-title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin: 0 0 20px 0;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.info-item {
  display: flex;
  align-items: center;

  .label {
    font-weight: 500;
    color: #666;
    margin-right: 10px;
    min-width: 80px;
  }

  .value {
    color: #333;
    font-weight: 400;
  }
}

.version-details {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  .section-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin-bottom: 15px;
  }
  .vue-table-root {
    flex: 1;
    .ve-table {
      height: 100% !important;
    }
  }
  ::v-deep .ve-table-container {
    height: 100% !important;
  }
}

// 进度条样式
.progress-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-bar {
  flex: 1;
  height: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;

  .progress-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 0.3s ease;
  }
}

.progress-text {
  font-size: 12px;
  color: #666;
  min-width: 30px;
}

.action-btn {
  color: #1890ff;
  cursor: pointer;
  text-decoration: none;

  &:hover {
    text-decoration: underline;
  }
}
//灰色背景
$bg-gray: #e2e2e2;
.button-container {
  background: $bg-gray;
  padding: 10px;
}
</style>
