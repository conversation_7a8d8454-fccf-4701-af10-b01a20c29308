import Vue from 'vue'
import VueI18n from 'vue-i18n'
import request from '@/utils/request'
Vue.use(VueI18n)

// 创建 i18n 实例
const i18n = new VueI18n({
  locale: localStorage.getItem('language'), // 默认语言
  messages: {},
  globalInjection: true // 全局注册$t函数
})

const convertLangToMap = (list) => {
  const map = {}
  list.forEach((item) => {
    map[item.key] = item.value
  })
  return map
}

export function syncMicroLanguage(code, language, messages) {
  return new Promise((resolve) => {
    request.post(`/system/SYSM06B6/getWebLocalMsg?code=${code}&langtag=${language}`, messages).then(res => {
      if (res.data.code === 200) {
        const convert = convertLangToMap(res.data.data)
        i18n.setLocaleMessage(language, convert)
        resolve(convert)
      }
    })
  })
}

export default i18n
