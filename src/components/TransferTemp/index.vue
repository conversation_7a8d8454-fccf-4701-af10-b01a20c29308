<template>
  <div class="transfertemp">
    <el-transfer
      style="text-align: left; display: inline-block"
      v-model="transferVal"
      filterable
      :titles="titles"
      target-order="push"
      :left-default-checked="leftCheckArr"
      :right-default-checked="rightCheckArr"
      @left-check-change="leftCheck"
      @right-check-change="rightCheck"
      :data="data"
    >
      <!-- @change="handleChange" -->
      <!-- {{ option.key }} -  -->
      <span slot-scope="{ option }">{{ option.label }}</span>
      <!-- <el-button
        class="transfer-footer"
        slot="left-footer"
        size="mini"
        :disabled="leftCheckArr.length != 1"
        @click="getMoveUp('left')"
        >上移</el-button
      >
      <el-button
        class="transfer-footer"
        slot="left-footer"
        size="mini"
        :disabled="leftCheckArr.length != 1"
        @click="getMoveDown('left')"
        >下移</el-button
      > -->
      <el-button
        class="transfer-footer"
        slot="right-footer"
        size="mini"
        :disabled="rightCheckArr.length != 1"
        @click="getMoveUp('right')"
        >上移</el-button
      >
      <el-button
        class="transfer-footer"
        slot="right-footer"
        size="mini"
        :disabled="rightCheckArr.length != 1"
        @click="getMoveDown('right')"
        >下移</el-button
      >
      <el-button
        class="transfer-footer"
        slot="right-footer"
        size="mini"
        type="primary"
        @click="handleChange()"
        >确认</el-button
      >
    </el-transfer>
  </div>
</template>
<script>
export default {
  props: {
    titles: {
      type: Array,
      default: () => ["列表1", "列表2"],
    },
    data: {
      type: Array,
      default: () => [],
    },
    // 暂未用到 弃用
    leftVal: {
      type: Array,
      default: () => [],
    },
    //  transferVal初始化数据来源
    rightVal: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      transferVal: [],
      // leftVal:[],
      // rightVal:[]
      leftCheckArr: [],
      rightCheckArr: [],
    };
  },
  mounted() {
    this.bindData();
  },
  methods: {
    bindData() {
      this.transferVal = [];
      for (var i = 0; i < this.rightVal.length; i++) {
        var Item = this.rightVal[i];
        // this.transferVal.push(Item.key);
        this.transferVal.push(Item.key);
      }
    },
    handleChange(value, direction, movedKeys) {
      var obj = [];
      for (var i = 0; i < this.transferVal.length; i++) {
        var Item = this.transferVal[i];
        this.data.forEach((item, index) => {
          if (item.key == Item) {
            obj.push(item);
          }
        });
      }
      this.$emit("savetranser", obj);
    },
    leftCheck(val) {
      // console.log("leftCheck", val);
      this.leftCheckArr = val;
    },
    rightCheck(val) {
      // console.log("rightCheck", val);
      this.rightCheckArr = val;
    },
    getMoveUp(type) {
      // console.log(this.leftVal)
      //   console.log(this.leftCheckArr);
      if (type == "left") {
        var index = this.transferVal.findIndex(
          (a) => a == this.leftCheckArr[0]
        );
      } else {
        var index = this.transferVal.findIndex(
          (a) => a == this.rightCheckArr[0]
        );
      }
      console.log(index);
      if (index != -1) {
        if (index == 0) {
          this.$message.warning("已经是第一行了！");
          return;
        }
        var row = this.transferVal[index];
        this.transferVal.splice(index, 1);
        this.transferVal.splice(index - 1, 0, row);
      }
    },
    getMoveDown(type) {
      if (type == "left") {
        var index = this.transferVal.findIndex(
          (a) => a == this.leftCheckArr[0]
        );
      } else {
        var index = this.transferVal.findIndex(
          (a) => a == this.rightCheckArr[0]
        );
      }

      if (index != -1) {
        if (index == this.transferVal.length - 1) {
          this.$message.warning("已经是最后一行了！");
          return;
        }
        var row = this.transferVal[index];
        this.transferVal.splice(index, 1);
        this.transferVal.splice(index + 1, 0, row);
      }
    },
  },
};
</script>
<style scoped lang="scss">
.transfertemp {
  text-align: center;
  ::v-deep .el-transfer__buttons {
    button {
      display: block;
      margin: 10px 0;
      padding: 8px 20px;
    }
  }
  ::v-deep .el-transfer-panel__footer {
    text-align: center;
  }
  ::v-deep .el-transfer-panel {
    height: 500px;
  }
  ::v-deep .el-transfer-panel__list.is-filterable {
    height: 400px;
  }
}
</style>