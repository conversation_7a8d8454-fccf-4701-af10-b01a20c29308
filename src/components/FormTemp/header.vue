<template>
  <el-form
    ref="formdata"
    :model="formdata"
    :label-width="'100px'"
    class="custInfo"
    style="margin-top: 20px"
  >
    <p class="formTitle">{{ title ? title : "单据标题" }}</p>
    <template v-for="(a, anum) in formtemplate">
      <div :key="anum">
        <el-divider
          v-if="a.type == 'divider'"
          :content-position="a.center ? a.center : 'left'"
          >{{ a.label }}</el-divider
        >
        <el-row v-else>
          <template v-for="(b, bnum) in a.rowitem">
            <el-col
              :key="bnum"
              :span="b.col"
              v-if="!b.show ? !b.show : returnEval(b.show)"
            >
              <div @click="cleValidate(b.code)">
                <el-form-item
                  :label="b.type == 'checkbox' ? '' : b.label"
                  :prop="b.code"
                  :label-width="b.labelwidth ? b.labelwidth : '100px'"
                  :rules="[
                    {
                      required: b.required ? b.required : false,
                      trigger: 'blur',
                      message: b.label + '为必填项',
                    },
                  ]"
                >
                  <!-- 输入框 -->
                  <el-input
                    v-if="b.type == 'input'"
                    v-model="formdata[b.code]"
                    :placeholder="'请输入' + b.label"
                    clearable
                    :size="b.size ? b.size : 'small'"
                    :disabled="b.disabled ? returnEval(b.disabled) : false"
                    :readonly="b.readonly ? returnEval(b.readonly) : false"
                  >
                    <template v-if="!!b.iconbtn">
                      <i
                        v-show="returnEval(b.iconbtn.show, 1)"
                        slot="suffix"
                        :class="b.iconbtn.icon"
                        class="getNextCode"
                        @click="
                          clickMethods(b.iconbtn.methods, b.iconbtn.param)
                        "
                      ></i>
                    </template>
                  </el-input>
                  <!-- 下拉框 -->

                  <el-select
                    v-else-if="b.type == 'select'"
                    v-model="formdata[b.code]"
                    :placeholder="'请选择' + b.label"
                    style="width: 100%"
                    :size="b.size ? b.size : 'small'"
                    :disabled="b.disabled ? returnEval(b.disabled) : false"
                    :readonly="b.readonly ? returnEval(b.readonly) : false"
                    @change="clickMethods(b.methods, b.param)"
                  >
                    <el-option
                      v-for="(c, cnum) in selectform.findIndex(
                        (a) => a.code == b.code
                      ) != -1
                        ? selectform[
                            selectform.findIndex((a) => a.code == b.code)
                          ].data
                        : b.options"
                      :key="cnum"
                      :label="c.label"
                      :value="c.value"
                    ></el-option>
                  </el-select>
                  <!-- 选择框checkbox -->
                  <div v-else-if="b.type == 'checkbox'">
                    <el-checkbox
                      v-model="formdata[b.code]"
                      :label="b.label"
                      :true-label="1"
                      :false-label="0"
                      :disabled="b.disabled ? returnEval(b.disabled) : false"
                    />
                  </div>
                  <!-- 数字 -->
                  <div v-else-if="b.type == 'number'">
                    <el-input-number
                      v-model="formdata[b.code]"
                      :controls="true"
                      type="number"
                      :min="0"
                      controls-position="right"
                      :size="b.size ? b.size : 'small'"
                      :disabled="b.disabled ? returnEval(b.disabled) : false"
                      :readonly="b.readonly ? returnEval(b.readonly) : false"
                    />
                  </div>
                  <!-- 日期 -->
                  <div v-else-if="b.type == 'date'">
                    <el-date-picker
                      v-model="formdata[b.code]"
                      type="date"
                      :size="b.size ? b.size : 'small'"
                      clearable
                      placeholder="选择日期"
                      style="width: 100%"
                      :disabled="b.disabled ? returnEval(b.disabled) : false"
                      :readonly="b.readonly ? returnEval(b.readonly) : false"
                    />
                  </div>
                  <!-- 字典 -->
                  <div v-else-if="b.type == 'dictionary'">
                    <el-popover
                      :ref="b.code + 'PropRef'"
                      placement="bottom"
                      trigger="click"
                      @show="$refs[b.code + 'Ref'][0].bindData()"
                    >
                      <selDictionaries
                        :ref="b.code + 'Ref'"
                        :multi="0"
                        :billcode="b.billcode"
                        style="width: 200px"
                        @singleSel="
                          formdata[b.code] = $event.dictvalue;
                          $refs[b.code + 'PropRef'][0].doClose();
                        "
                        @closedic="$refs[b.code + 'PropRef'][0].doClose()"
                      />
                      <div slot="reference">
                        <el-input
                          v-model="formdata[b.code]"
                          :placeholder="'请选择' + b.label"
                          clearable
                          :size="b.size ? b.size : 'small'"
                          :disabled="
                            b.disabled ? returnEval(b.disabled) : false
                          "
                          :readonly="
                            b.readonly ? returnEval(b.readonly) : false
                          "
                        >
                        </el-input>
                      </div>
                    </el-popover>
                  </div>
                  <!-- 文本域 -->
                  <div v-else-if="b.type == 'textarea'">
                    <el-input
                      v-model="formdata[b.code]"
                      :placeholder="'请输入' + b.label"
                      clearable
                      type="textarea"
                      :size="b.size ? b.size : 'small'"
                      :autosize="b.autosize?b.autosize:{ minRows: 2, maxRows: 4 }"
                      :disabled="b.disabled ? returnEval(b.disabled) : false"
                      :readonly="b.readonly ? returnEval(b.readonly) : false"
                    />
                  </div>
                  <!-- 文本 -->
                  <div v-else-if="b.type == 'text'">
                    <span style="font-size: 18px; color: #666">{{
                      formdata[b.code]
                    }}</span>
                  </div>
                  <!-- autocomplete -->
                  <div v-else-if="b.type == 'autocomplete'">
                    <!-- 客户 -->
                    <div v-if="b.searchtype == 'customer'">
                      <GroupAutoComplete
                        :size="b.size"
                        :value="formdata[b.code]"
                        :baseurl="'/sale/D01M01B1/getOnlinePageList'"
                        :type="'客户'"
                        @setRow="
                          $emit('getGroupName', $event);
                          cleValidate(b.code);
                        "
                        @autoClear="$emit('autoClear')"
                        :isdisabled="b.disabled ? returnEval(b.disabled) : false"
                      ></GroupAutoComplete>
                    </div>
                    <!-- 供应商 -->
                    <div v-else-if="b.searchtype == 'supplier'">
                      <GroupAutoComplete
                        :size="b.size"
                        :value="formdata[b.code]"
                        :baseurl="'/sale/D01M01B2/getOnlinePageList'"
                        :type="'供应商'"
                        @setRow="
                          $emit('getSuppGroupName', $event);
                          cleValidate(b.code);
                        "
                        @autoClear="$emit('autoClear')"
                        :isdisabled="b.disabled ? returnEval(b.disabled) : false"
                      ></GroupAutoComplete>
                    </div>
                    <!-- 生产车间 -->
                    <div v-else-if="b.searchtype == 'workshop'">
                      <GroupAutoComplete
                        :size="b.size"
                        :value="formdata[b.code]"
                        :baseurl="'/sale/D01M01B3/getPageList'"
                        :type="'生产车间'"
                        @setRow="
                          $emit('getWorkGroupName', $event);
                          cleValidate(b.code);
                        "
                        @autoClear="$emit('autoClear')"
                        :isdisabled="b.disabled ? returnEval(b.disabled) : false"
                      ></GroupAutoComplete>
                    </div>
                    <!-- 外协厂商 -->
                    <div v-else-if="b.searchtype == 'factory'">
                      <GroupAutoComplete
                        :size="b.size"
                        :value="formdata[b.code]"
                        :baseurl="'/sale/D01M01B4/getPageList'"
                        :type="'外协厂商'"
                        @setRow="
                          $emit('getFactGroupName', $event);
                          cleValidate(b.code);
                        "
                        @autoClear="$emit('autoClear')"
                        :isdisabled="b.disabled ? returnEval(b.disabled) : false"
                      ></GroupAutoComplete>
                    </div>
                    <!-- 部门 -->
                    <div v-else-if="b.searchtype == 'branch'">
                      <GroupAutoComplete
                        :size="b.size"
                        :value="formdata[b.code]"
                        :isdisabled="b.disabled ? returnEval(b.disabled) : false"
                        :baseurl="'/sale/D01M01B5/getPageList'"
                        :type="'部门'"
                        @setRow="
                          $emit('getBranGroupName', $event);
                          cleValidate(b.code);
                        "
                        @autoClear="$emit('autoClear')"
                      ></GroupAutoComplete>
                    </div>
                    <!-- 潜在客户 -->
                    <div v-else-if="b.searchtype == 'prospects'">
                      <GroupAutoComplete
                        :size="b.size"
                        :value="formdata[b.code]"
                        :isdisabled="b.disabled ? returnEval(b.disabled) : false"
                        :baseurl="'/sale/D01M01B6/getPageList'"
                        :type="'潜在客户'"
                        @setRow="
                          $emit('getProsGroupName', $event);
                          cleValidate(b.code);
                        "
                        @autoClear="$emit('autoClear')"
                      ></GroupAutoComplete>
                    </div>
                    <!-- D01M06R1 -->
                    <div v-else-if="b.searchtype == 'group'">
                      <GroupAutoComplete
                        :size="b.size"
                        :value="formdata[b.code]"
                        :isdisabled="b.disabled ? returnEval(b.disabled) : false"
                        @setRow="
                          $emit('getAllGroupName', $event);
                          cleValidate(b.code);
                        "
                        @autoClear="$emit('autoClear')"
                      ></GroupAutoComplete>
                    </div>
                    <!-- 仓库 -->
                    <div v-else-if="b.searchtype == 'store'">
                      <StoreAutoComplete
                        :size="b.size"
                        :value="formdata[b.code]"
                        :isdisabled="b.disabled ? returnEval(b.disabled) : false"
                        @setRow="
                          $emit('getStoreName', $event,b.code);
                          cleValidate(b.code);
                        "
                        @autoClear="$emit('autoStoreClear',b.code)"
                      ></StoreAutoComplete>
                    </div>
                    <!-- 工序 -->
                    <div v-else-if="b.searchtype == 'procedure'">
                      <ProcAutoComplete
                        :size="b.size"
                        :value="formdata[b.code]"
                        :isdisabled="b.disabled ? returnEval(b.disabled) : false"
                        @setRow="
                          $emit('getProcName', $event);
                          cleValidate(b.code);
                        "
                        @autoClear="$emit('autoProcClear')"
                      ></ProcAutoComplete>
                    </div>
                    <!-- 角色工序 -->
                    <div v-else-if="b.searchtype == 'roleproc'">
                      <RoleProcAutoComplete
                        :size="b.size"
                        :value="formdata[b.code]"
                        :isdisabled="b.disabled ? returnEval(b.disabled) : false"
                        @setRow="
                          $emit('getRoleProcName', $event);
                          cleValidate(b.code);
                        "
                        @autoClear="$emit('autoRoleProcClear')"
                      ></RoleProcAutoComplete>
                    </div>
                    <!-- 货品 -->
                    <div v-else-if="b.searchtype == 'goods'">
                      <GoodsAutoComplete
                        :size="b.size"
                        :value="formdata[b.code]"
                        :isdisabled="b.disabled ? returnEval(b.disabled) : false"
                        @setRow="
                          $emit('getGoodsName', $event);
                          cleValidate(b.code);
                        "
                        @autoClear="$emit('autoGoodsClear')"
                      ></GoodsAutoComplete>
                    </div>
                  </div>
                </el-form-item>
              </div>
            </el-col>
          </template>
        </el-row>
      </div>
    </template>
  </el-form>
</template>
<script>
import selDictionaries from "@/views/modules/SYS/SYSM07B1/components/select.vue";
export default {
  components: {
    selDictionaries,
    // elitem:()=>import('@/views/modules/D01/D01M04B1/components/elitem')
  },
  props: {
    title: {
      type: String,
      default: "单据标题",
    },
    formdata: {
      type: Object,
      default: {
        lister: JSON.parse(window.localStorage.getItem("getInfo")).realname,
        createby: JSON.parse(window.localStorage.getItem("getInfo")).realname,
      },
    },
    formtemplate: {
      type: [Array],
      default: [],
    },
    selectform: {
      type: [Array],
      default: function () {
        return [];
      },
    },
  },
  mounted() {
    //     Vue.directive('exchangeHtml',{
    //     bind: function(el,foo){
    //       console.log(el,foo)
    //       el.innerHTML=''
    //       el.innerHTML += foo.value
    //       this.$forceUpdate()
    //   }
    // })
    // console.log(this.selectform, "formdata");
  },
  data() {
    return {};
  },
  methods: {
    returnEval(data, itrue) {
      if (typeof data == "string") {
        return eval(data);
      } else {
        return data;
      }
      // return !!itrue ? eval(data) : data;
    },
    clickMethods(meth, param) {
      if (!meth) {
        return;
      }
      var obj = { meth, param };
      this.$emit("clickMethods", obj);
    },
    cleValidate(val) {
      this.$refs.formdata.clearValidate(val);
    },
    //
    // b.searchtype == 'customer' ||
    // b.searchtype == 'supplier' ||
    // b.searchtype == 'workshop' ||
    // b.searchtype == 'factory' ||
    // b.searchtype == 'branch' ||
    // b.searchtype == 'prospects'
    //
  },
};
</script>
<style lang="scss" scoped>
.editFormStyle {
  //   display: flex;
  //   flex-wrap: wrap;
  ::v-deep .el-form-item {
    margin-bottom: 8px;
  }
}
::v-deep.custInfo .el-row {
  margin-bottom: -20px;
  display: flex;
  flex-wrap: wrap;
}
.getNextCode {
  font-size: 15px;
  color: #409eff;
  cursor: pointer;
  border-left: 1px solid #dcdfe6;
  padding: 7px 10px;
}
</style>