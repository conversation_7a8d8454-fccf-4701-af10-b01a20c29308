<template>
  <div>
    <div v-if="$slots.Header">
      <slot name="Header" ></slot>
    </div>
    <div v-else style="min-height:150px">
      <Header
        ref="formHeader"
        :formdata="formdata"
        :formtemplate="formtemplate.header.content"
        :title="formtemplate.header.title"
        :selectform="selectform"
        @clickMethods="$emit('clickMethods',$event)"
        @cleValidate="$emit('cleValidate',$event)"
        @getAllGroupName="$emit('getAllGroupName',$event)"
        @getGroupName="$emit('getGroupName',$event)"
        @getSuppGroupName="$emit('getSuppGroupName',$event)"
        @getFactGroupName="$emit('getFactGroupName',$event)"
        @getWorkGroupName="$emit('getWorkGroupName',$event)"
        @getBranGroupName="$emit('getBranGroupName',$event)"
        @getProsGroupName="$emit('getProsGroupName',$event)"
        @getStoreName="getStoreName"
        @getProcName="$emit('getProcName',$event)"
        @getRoleProcName="$emit('getRoleProcName',$event)"
        @getGoodsName="$emit('getGoodsName',$event)"
        @getFlawName="$emit('getFlawName',$event)"
        @autoClear="$emit('autoClear')"
        @autoStoreClear="$emit('autoStoreClear',$event)"
        @autoProcClear="$emit('autoProcClear')"
        @autoRoleProcClear="$emit('autoRoleProcClear')"
        @autoFlawClear="$emit('autoFlawClear')"
        @autoGoodsClear="$emit('autoGoodsClear')"
      ></Header>
    </div>
    <slot name="Item"></slot>
    <!-- <Item v-else> </Item> -->
    <div v-if="$slots.Footer">
      <slot name="Footer"></slot>
    </div>
    <div v-else>
      <Footer
        :formdata="formdata"
        :formtemplate="formtemplate.footer.content"
      ></Footer>
    </div>
  </div>
</template>
<script>
import Header from "./header";
import Footer from "./footer";
import Item from "./item";
export default {
  components: {
    Header,
    Item,
    Footer,
  },
  props: {
    formdata: {
      type: Object,
    },
    formtemplate: {
      type: Object,
    },
    selectform: {
      type: [Object,Array],
    },
  },
  methods:{
    getStoreName(data,file){
      this.$emit('getStoreName',data,file)
    }
  },
};
</script>