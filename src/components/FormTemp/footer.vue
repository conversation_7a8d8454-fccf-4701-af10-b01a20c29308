<template>
  <div style="margin-top:15px">
    <el-form ref="formdata" :label-width="'100px'" class="footFormContent">
      <template v-for="(a, anum) in tempData">
        <div :key="anum">
          <el-divider
            v-if="a.type == 'divider'"
            :content-position="a.center ? a.center : 'left'"
            >{{ a.label }}</el-divider
          >
          <el-row v-else-if="a.type == 'foot'">
            <template v-for="(b, bnum) in a.rowitem">
              <el-col
                :key="bnum"
                :span="b.col"
                v-show="b.code == 'assessdate' ? formdata.assessor : true"
              >
                <el-form-item :label="b.label" :prop="b.code" >
                  <div
                    v-if="
                      b.code == 'assessdate' ||
                      b.code == 'modifydate' ||
                      b.code == 'createdate'
                    "
                  >
                    <span
                      v-show="formdata[b.code]"
                      class="el-form-item__label"
                      style="white-space:nowrap"
                      >{{ formdata[b.code] | dateFormats }}</span
                    >
                  </div>
                  <div v-else>
                    <span
                      v-show="formdata[b.code]"
                      class="el-form-item__label"
                      >{{ formdata[b.code] }}</span
                    >
                  </div>
                </el-form-item>
              </el-col>
            </template>
          </el-row>
          <el-row v-else>
            <template v-for="(b, bnum) in a.rowitem">
              <el-col
                :key="bnum"
                :span="b.col"
                v-if="!b.show ? !b.show : returnEval(b.show)"
              >
                <div @click="cleValidate(b.code)">
                  <el-form-item
                    :label="b.type == 'checkbox' ? '' : b.label"
                    :prop="b.code"
                  >
                    <!-- 输入框 -->
                    <el-input
                      v-if="b.type == 'input'"
                      v-model="formdata[b.code]"
                      :placeholder="'请输入' + b.label"
                      clearable
                      :size="b.size ? b.size : 'small'"
                      :disabled="b.disabled ? returnEval(b.disabled) : false"
                      :readonly="b.readonly ? returnEval(b.readonly) : false"
                    />
                    <!-- 下拉框 -->
                    <el-select
                      v-else-if="b.type == 'select'"
                      v-model="formdata[b.code]"
                      :placeholder="'请选择' + b.label"
                      style="width: 100%"
                      :size="b.size ? b.size : 'small'"
                      :disabled="b.disabled ? returnEval(b.disabled) : false"
                      :readonly="b.readonly ? returnEval(b.readonly) : false"
                      @change="clickMethods(b.methods, b.param)"
                    >
                      <el-option
                        v-for="(c, cnum) in b.options"
                        :key="cnum"
                        :label="c.label"
                        :value="c.value"
                      ></el-option>
                    </el-select>

                    <!-- 选择框checkbox -->
                    <div v-else-if="b.type == 'checkbox'">
                      <el-checkbox
                        v-model="formdata[b.code]"
                        :label="b.label"
                        :true-label="0"
                        :false-label="1"
                        :size="b.size ? b.size : 'mini'"
                        @change="clickMethods(b.methods, b.param)"
                        :disabled="b.disabled ? returnEval(b.disabled) : false"
                        :readonly="b.readonly ? returnEval(b.readonly) : false"
                      />
                    </div>
                    <!-- 数字 -->
                    <div v-else-if="b.type == 'number'">
                      <el-input-number
                        v-model="formdata[b.code]"
                        :controls="true"
                        type="number"
                        :min="0"
                        controls-position="right"
                        :size="b.size ? b.size : 'small'"
                        @change="clickMethods(b.methods, b.param)"
                        :disabled="b.disabled ? returnEval(b.disabled) : false"
                        :readonly="b.readonly ? returnEval(b.readonly) : false"
                      />
                    </div>
                    <!-- 日期 -->
                    <div v-else-if="b.type == 'date'">
                      <el-date-picker
                        v-model="formdata[b.code]"
                        type="date"
                        :size="b.size ? b.size : 'small'"
                        clearable
                        placeholder="选择日期"
                        style="width: 100%"
                        :disabled="b.disabled ? returnEval(b.disabled) : false"
                        :readonly="b.readonly ? returnEval(b.readonly) : false"
                      />
                    </div>
                    <!-- 字典 -->
                    <div v-else-if="b.type == 'dictionary'">
                      <el-popover
                        :ref="b.code + 'PropRef'"
                        placement="bottom"
                        trigger="click"
                        @show="$refs[b.code + 'Ref'][0].bindData()"
                      >
                        <selDictionaries
                          :ref="b.code + 'Ref'"
                          :multi="0"
                          :billcode="b.billcode"
                          style="width: 200px"
                          @singleSel="
                            formdata[b.code] = $event.dictvalue;
                            $refs[b.code + 'PropRef'][0].doClose();
                          "
                          @closedic="$refs[b.code + 'PropRef'][0].doClose()"
                        />
                        <div slot="reference">
                          <el-input
                            v-model="formdata[b.code]"
                            :placeholder="'请选择' + b.label"
                            clearable
                            :size="b.size ? b.size : 'small'"
                            :disabled="
                              b.disabled ? returnEval(b.disabled) : false
                            "
                            :readonly="
                              b.readonly ? returnEval(b.readonly) : false
                            "
                          >
                          </el-input>
                        </div>
                      </el-popover>
                    </div>
                    <!-- 文本域 -->
                    <div v-else-if="b.type == 'textarea'">
                      <el-input
                        v-model="formdata[b.code]"
                        :placeholder="'请输入' + b.label"
                        clearable
                        type="textarea"
                        :size="b.size ? b.size : 'small'"
                        :autosize="{ minRows: 2, maxRows: 4 }"
                        :disabled="b.disabled ? returnEval(b.disabled) : false"
                        :readonly="b.readonly ? returnEval(b.readonly) : false"
                      />
                    </div>
                    <!-- 文本 -->
                    <div v-else-if="b.type == 'text'">
                      <span style="font-size: 18px; color: #606266">{{
                        formdata[b.code]
                      }}</span>
                    </div>
                    <!-- autocomplete -->
                    <div v-else-if="b.type == 'autocomplete'">
                      <!-- 客户 -->
                    <div v-if="b.searchtype == 'customer'">
                      <GroupAutoComplete
                        :size="b.size"
                        :value="formdata[b.code]"
                        :baseurl="'/sale/D01M01B1/getOnlinePageList'"
                        :type="'客户'"
                        @setRow="
                          $emit('getGroupName', $event);
                          cleValidate(b.code);
                        "
                        @autoClear="$emit('autoClear')"
                        :isdisabled="b.disabled ? returnEval(b.disabled) : false"
                      ></GroupAutoComplete>
                    </div>
                    <!-- 供应商 -->
                    <div v-else-if="b.searchtype == 'supplier'">
                      <GroupAutoComplete
                        :size="b.size"
                        :value="formdata[b.code]"
                        :baseurl="'/sale/D01M01B2/getOnlinePageList'"
                        :type="'供应商'"
                        @setRow="
                          $emit('getSuppGroupName', $event);
                          cleValidate(b.code);
                        "
                        @autoClear="$emit('autoClear')"
                        :isdisabled="b.disabled ? returnEval(b.disabled) : false"
                      ></GroupAutoComplete>
                    </div>
                    <!-- 生产车间 -->
                    <div v-else-if="b.searchtype == 'workshop'">
                      <GroupAutoComplete
                        :size="b.size"
                        :value="formdata[b.code]"
                        :baseurl="'/sale/D01M01B3/getPageList'"
                        :type="'生产车间'"
                        @setRow="
                          $emit('getWorkGroupName', $event);
                          cleValidate(b.code);
                        "
                        @autoClear="$emit('autoClear')"
                        :isdisabled="b.disabled ? returnEval(b.disabled) : false"
                      ></GroupAutoComplete>
                    </div>
                    <!-- 外协厂商 -->
                    <div v-else-if="b.searchtype == 'factory'">
                      <GroupAutoComplete
                        :size="b.size"
                        :value="formdata[b.code]"
                        :baseurl="'/sale/D01M01B4/getPageList'"
                        :type="'外协厂商'"
                        @setRow="
                          $emit('getFactGroupName', $event);
                          cleValidate(b.code);
                        "
                        @autoClear="$emit('autoClear')"
                        :isdisabled="b.disabled ? returnEval(b.disabled) : false"
                      ></GroupAutoComplete>
                    </div>
                    <!-- 部门 -->
                    <div v-else-if="b.searchtype == 'branch'">
                      <GroupAutoComplete
                        :size="b.size"
                        :value="formdata[b.code]"
                        :isdisabled="b.disabled ? returnEval(b.disabled) : false"
                        :baseurl="'/sale/D01M01B5/getPageList'"
                        :type="'部门'"
                        @setRow="
                          $emit('getBranGroupName', $event);
                          cleValidate(b.code);
                        "
                        @autoClear="$emit('autoClear')"
                      ></GroupAutoComplete>
                    </div>
                    <!-- 潜在客户 -->
                    <div v-else-if="b.searchtype == 'prospects'">
                      <GroupAutoComplete
                        :size="b.size"
                        :value="formdata[b.code]"
                        :isdisabled="b.disabled ? returnEval(b.disabled) : false"
                        :baseurl="'/sale/D01M01B6/getPageList'"
                        :type="'潜在客户'"
                        @setRow="
                          $emit('getProsGroupName', $event);
                          cleValidate(b.code);
                        "
                        @autoClear="$emit('autoClear')"
                      ></GroupAutoComplete>
                    </div>
                    <!-- D01M06R1 -->
                    <div v-else-if="b.searchtype == 'group'">
                      <GroupAutoComplete
                        :size="b.size"
                        :value="formdata[b.code]"
                        :isdisabled="b.disabled ? returnEval(b.disabled) : false"
                        @setRow="
                          $emit('getAllGroupName', $event);
                          cleValidate(b.code);
                        "
                        @autoClear="$emit('autoClear')"
                      ></GroupAutoComplete>
                    </div>
                      <!-- 仓库 -->
                      <div v-else-if="b.searchtype == 'store'">
                        <StoreAutoComplete
                          :size="b.size"
                          :value="formdata[b.code]"
                          :isdisabled="
                            b.disabled ? returnEval(b.disabled) : false
                          "
                          @setRow="$emit('getStoreName', $event)"
                          @autoClear="$emit('autoClear')"
                        ></StoreAutoComplete>
                      </div>
                      <!-- 工序 -->
                      <div v-else-if="b.searchtype == 'proc'">
                        <ProcAutoComplete
                          :size="b.size"
                          :value="formdata[b.code]"
                          :isdisabled="
                            b.disabled ? returnEval(b.disabled) : false
                          "
                          @setRow="$emit('getProcName', $event)"
                          @autoClear="$emit('autoClear')"
                        ></ProcAutoComplete>
                      </div>
                      <!-- 角色工序 -->
                      <div v-else-if="b.searchtype == 'roleproc'">
                        <RoleProcAutoComplete
                          :size="b.size"
                          :value="formdata[b.code]"
                          :isdisabled="
                            b.disabled ? returnEval(b.disabled) : false
                          "
                          @setRow="$emit('getRoleProcName', $event)"
                          @autoClear="$emit('autoClear')"
                        ></RoleProcAutoComplete>
                      </div>
                      <!-- 货品 -->
                      <div v-else-if="b.searchtype == 'goods'">
                        <GoodsAutoComplete
                          :size="b.size"
                          :value="formdata[b.code]"
                          :isdisabled="
                            b.disabled ? returnEval(b.disabled) : false
                          "
                          @setRow="$emit('getGoodsName', $event)"
                          @autoClear="$emit('autoClear')"
                        ></GoodsAutoComplete>
                      </div>
                    </div>
                  </el-form-item>
                </div>
              </el-col>
            </template>
          </el-row>
        </div>
      </template>
    </el-form>
    <!-- -->
  </div>
</template>
<script>
// import selDictionaries from "@/views/modules/SYS/SYSM07B1/components/select.vue";
export default {
  components: {
    // selDictionaries,
  },
  props: {
    title: {
      type: String,
      default: "单据标题",
    },
    formdata: {
      type: Object,
      default: {
        lister: JSON.parse(window.localStorage.getItem("getInfo")).realname,
        createby: JSON.parse(window.localStorage.getItem("getInfo")).realname,
      },
    },
    formtemplate: {
      type: [Array],
      default: function () {
        return [];
      },
    },
  },
  data() {
    return {
      tempData: this.formtemplate,
    };
  },
  watch: {
    formtemplate: function (val, oldVal) {
      console.log("foot", val);
      if (!this.formtemplate.length) {
        this.tempData = [
          {
            type: "divider",
            label: "",
            center: "",
          },
          {
            type: "form",
            item: [
              {
                col: 23,
                type: "input",
                code: "summary",
                label: "摘  要",
              },
            ],
          },
          {
            type: "foot",
            item: [
              {
                col: 4,
                type: "text",
                code: "createby",
                label: "创建人",
              },
              {
                col: 4,
                type: "text",
                code: "createdate",
                label: "创建日期",
              },
              {
                col: 4,
                type: "text",
                code: "lister",
                label: "制表",
              },
              {
                col: 4,
                type: "text",
                code: "modifydate",
                label: "修改日期",
              },
              {
                col: 4,
                type: "text",
                code: "assessor",
                label: "审核",
              },
              {
                col: 4,
                type: "text",
                code: "assessdate",
                label: "审核日期",
              },
            ],
          },
        ];
      } else {
        this.tempData = this.formtemplate;
      }
    },
  },
  mounted() {
    // console.log(this.tempData, "tempData");
  },
  methods: {
    returnEval(data, itrue) {
      if (typeof data == "string") {
        return eval(data);
      } else {
        return data;
      }
      // return !!itrue ? eval(data) : data;
    },
    cleValidate(val) {
      this.$refs.formdata.clearValidate(val);
    },
    clickMethods(meth, param) {
      var obj = { meth, param };
      this.$emit("clickMethods", obj);
    },
  },
};
</script>
<style lang="scss" scoped>
.editFormStyle {
  //   display: flex;
  //   flex-wrap: wrap;
  ::v-deep .el-form-item {
    margin-bottom: 8px;
  }
}
</style>
