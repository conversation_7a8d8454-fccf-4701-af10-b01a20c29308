<template>
  <div class="mypopver">
    <div class="mypopver-card">
      <el-table
        ref="itemTable"
        v-loading="listLoading"
        :data="lst"
        border
        height="220px"
        fit
        highlight-current-row
        size="small"
        class="tb-edit tableBox"
        :header-cell-style="{
          background: '#F3F4F7',
          color: '#555',
          padding: '4px 0px 4px 0px',
        }"
        :cell-style="{ padding: '4px 0px' }"
        :row-style="{ height: '20px' }"
      >
        <el-table-column align="center" label="ID" min-width="40" fixed>
          <template slot-scope="scope">
            <span>{{ scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="当前工序"
          align="center"
          min-width="100"
          show-overflow-tooltip
          fixed
          v-if="lst.length != 0 && lst[0].hasOwnProperty('wkwpname')"
        >
          <!-- lst.length!=0&&lst[0].wkwpname?true:false -->
          <template slot-scope="scope">
            <div
              :class="scope.row.wkwpname ? 'textborder' : ''"
              :style="{
                background: getBackGroundColor(scope.row, 'wkwpid'),
                color: getColor(scope.row, 'wkwpid'),
              }"
            >
              {{ scope.row.wkwpname }}
            </div>
          </template>
        </el-table-column>
        <template v-for="i in tableForm.item">
          <el-table-column
            :key="i.id"
            :prop="i.itemcode"
            :columnKey="i.itemcode"
            :label="i.itemname"
            :align="i.aligntype ? i.aligntype : 'center'"
            :min-width="i.minwidth"
            :show-overflow-tooltip="i.overflow ? true : false"
            :sortable="i.sortable ? true : false"
            v-if="i.displaymark ? true : false"
            :fixed="i.fixed ? true : false"
          >
            <template slot-scope="scope">
              <div
                v-if="
                  i.itemcode == 'itemorgdate' ||
                  i.itemcode == 'itemplandate' ||
                  i.itemcode == 'plandate' ||
                  i.itemcode == 'startdate' ||
                  i.itemcode == 'enddate' ||
                  i.itemcode == 'billdate'
                "
              >
                <span>{{ scope.row[i.itemcode] | dateFormat }}</span>
              </div>
              <div v-else-if="i.itemcode == 'bomtype'">
                <span>{{ scope.row.bomtype | bomtypeFormate }}</span>
              </div>

              <div v-else-if="i.itemcode == 'wpname'">
                <div
                  :class="scope.row.wpname ? 'textborder' : ''"
                  :style="{
                    background: getBackGroundColor(scope.row, 'wpid'),
                    color: getColor(scope.row, 'wpid'),
                  }"
                >
                  {{ scope.row.wpname }}
                </div>
              </div>

              <div
                v-else-if="i.itemcode == 'requisite' || i.itemcode == 'stonly'"
              >
                <!--D05M21S1Item 必要项目requisite   只选择stonly-->
                <el-checkbox
                  v-model="scope.row[i.itemcode]"
                  :true-label="1"
                  :false-label="0"
                  size="mini"
                />
              </div>
              <div v-else-if="i.itemcode == 'matstatus'">
                <div v-if="scope.row.matcode">
                  <el-tag v-if="scope.row.matused" effect="dark" size="small"
                    >已领
                  </el-tag>
                  <el-tag v-else effect="plain" size="small">未领 </el-tag>
                </div>
              </div>
              <!-- 结余 D03M03B1\B2 采购收货-->
              <div v-else-if="i.itemcode == 'compcost'">
                <span
                  v-if="scope.row.quantity - scope.row.finishqty < 0"
                  style="color: #f56c6c"
                  >{{ scope.row.quantity - scope.row.finishqty }}</span
                >
                <span
                  v-else-if="scope.row.quantity - scope.row.finishqty == 0"
                  style="color: #67c23a"
                  >{{ scope.row.quantity - scope.row.finishqty }}</span
                >
                <span
                  v-else-if="
                    scope.row.quantity - scope.row.finishqty ==
                    scope.row.quantity
                  "
                  >{{ scope.row.quantity - scope.row.finishqty }}</span
                >
                <span v-else style="color: #409eff">{{
                  scope.row.quantity - scope.row.finishqty
                }}</span>
              </div>
              <span v-else>
                <span>{{ scope.row[i.itemcode] }}</span>
              </span>
            </template>
          </el-table-column>
        </template>
      </el-table>
    </div>
  </div>
</template>
<script>
import request from "@/utils/request";
export default {
  props: {
    tableForm: {
      type: Object,
      default() {
        return {
          item: [],
        };
      },
    },
    lst: {
      type: Array,
      default() {
        return [];
      },
    },
    progressData: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      //   lst: [],
      listLoading: false,
      // progressData: [], //工序参数
    };
  },
  created() {
    // this.getprogressData();
  },
  updated() {
    this.$nextTick(() => {
      this.$refs.itemTable.doLayout();
    });
  },
  watch: {
    lst: function (val, oldval) {
      if (this.lst.length == 0) {
        return;
      }
      if (!this.lst[0].hasOwnProperty("wkwpname")) {
        return;
      }
      // this.getprogressData();
    },
  },
  methods: {
    getprogressData() {
      var queryParams = {
        PageNum: 1,
        PageSize: 1000,
        OrderType: 0,
        SearchType: 1,
        OrderBy: "rownum",
        //SearchPojo:{}
      };
      request
        .post("/manu/D05M21S1/getPageTh", JSON.stringify(queryParams))
        .then((response) => {
          if (response.data.code == 200) {
            this.progressData = response.data.data.list;
            // console.log("progressData", this.progressData);
          }
        });
    },
    getBackGroundColor(row, widx) {
      var backgroundColor = "#FFF";
      if (this.progressData.length == 0) {
        return backgroundColor;
      }
      var index = this.progressData.findIndex((item) => item.id == row[widx]);
      if (index != -1) {
        backgroundColor = !!this.progressData[index].backcolorargb
          ? this.progressData[index].backcolorargb
          : "#FFF";
      } 
       return backgroundColor;
    },
    getColor(row, widx) {
      var Color = "#000";
      if (this.progressData.length == 0) {
        return Color;
      }
      var index = this.progressData.findIndex((item) => item.id == row[widx]);
      if (index != -1) {
        Color = !!this.progressData[index].forecolorargb
          ? this.progressData[index].forecolorargb
          : "#000";
      } 
      return Color;
    },
  },
  filters: {
    dateFormat(dataStr) {
      if (!dataStr) {
        return;
      }
      var dt = new Date(dataStr);
      var y = dt.getFullYear();
      var m = (dt.getMonth() + 1).toString().padStart(2, "0");
      var d = dt.getDate().toString().padStart(2, "0");
      return `${y}-${m}-${d}`;
    },
    bomtypeFormate(dataStr) {
      if (dataStr == 0) {
        return "未选";
      } else if (dataStr == 1) {
        return "标准Bom";
      } else if (dataStr == 2) {
        return "订单Bom";
      } else if (dataStr == 3) {
        return "销售Bom";
      }
    },
  },
};
</script>
<style lang="scss" scoped>
@import "@/styles/mycustom.scss";
.mypopver {
  z-index: 999;
  max-width: 1200px;
  .mypopver-card {
    background: #fff;
    // padding: 20px 10px;
    // box-shadow: 1px 1px 10px rgb(0 0 0 / 30%);
  }
  .textborder {
    border: 1px solid;
    color: #969696;
    padding: 2px 10px;
    background: #fcfcfc;
    font-size: 14px;
    border-radius: 3px;
    display: inline-block;
    line-height: 20px;
  }
}
</style>