<template>
  <div>
    <div class="">
      <div style="border-bottom: 1px solid #ccc" @click="goBack">
        <div class="goBack">
          <div v-if="lastValue">
            <span class="iconfont el-icon-arrow-left"></span>
          </div>
          <div style="margin-left: 10px; text-align: left">
            {{ lastValue ? lastValue.tgvalue : "暂无上级" }}
          </div>
        </div>
      </div>
      <div class="dictionaries_ul myscrollbar">
        <ul class="" v-if="lst.length != 0">
          <li
            v-for="(item, index) in lst"
            :key="index"
            :class="radio == index ? 'active' : ''"
            @click="
              getCurrentRow(item);
              bindData('children', item.id);
              radio = index;
            "
          >
            <span>{{ item.tgvalue }}</span>
          </li>
        </ul>
        <div class="noData" v-else>暂无数据</div>
      </div>
      <div class="foots">
        <span
          style="border-right: 1px solid #dcdfe6"
          @click="SYSM07B1Visible = true"
          >编辑</span
        >
        <span @click="$emit('closedic')">关闭</span>
      </div>
    </div>
    <el-dialog
      width="450px"
      :title="editTitle"
      :append-to-body="true"
      :visible.sync="SYSM07B1Visible"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      top="20vh"
      style="z-index: 10"
    >
      <div class="dialog-body customDialog">
        <div class="left">
          <p
            @click="changeLi('lst', index)"
            :class="item.isActive ? 'isActive' : ''"
            v-for="(item, index) in lst"
            :key="index"
          >
            {{ item.tgvalue }}
          </p>
        </div>
        <div class="right" style="width: 100px">
          <el-button
            type="primary"
            size="mini"
            style="margin-left: 10px"
            @click="addInput()"
            >添 加</el-button
          >
          <el-button
            type="primary"
            size="mini"
            @click="editInput()"
            :disabled="ActiveIndex == -1"
            >编 辑</el-button
          >
          <el-button
            type="danger"
            size="mini"
            @click="delItem()"
            :disabled="ActiveIndex == -1"
            >删 除</el-button
          >
          <el-button
            type="primary"
            size="mini"
            @click="getMoveUp()"
            :disabled="ActiveIndex == -1"
            >上 移</el-button
          >
          <el-button
            type="primary"
            size="mini"
            @click="getMoveDown()"
            :disabled="ActiveIndex == -1"
            >下 移</el-button
          >
        </div>
      </div>
    </el-dialog>
    <el-dialog
      title="新增"
      :visible.sync="dialogVisible"
      width="30%"
      style="z-index: 9999"
      append-to-body
      top="20vh"
    >
      <el-form
        ref="formdata"
        :model="formdata"
        :label-width="'130px'"
        class="custInfo"
        auto-complete="off"
        :rules="rules"
      >
        <el-row>
          <el-col :span="24">
            <el-form-item label="上级名称">
              <el-input
                v-model="selrows.tgvalue"
                placeholder="上级名称"
                clearable
                size="small"
                style="width: 100%; min-width: 140px"
                :disabled="!selrows.tgvalue"
                readonly
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div @click="clearValidt('tgvalue')">
              <el-form-item label="名称" prop="tgvalue">
                <el-input
                  v-model="formdata.tgvalue"
                  placeholder="名称"
                  clearable
                  size="small"
                  style="width: 100%; min-width: 140px"
                />
              </el-form-item>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="前缀">
              <el-select
                v-model="selTgValue"
                placeholder="请选择"
                @change="changeTgcode"
                size="small"
                style="width: 100%; min-width: 140px"
                :disabled="!selrows.tgvalue"
              >
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeDialog" size="small">取 消</el-button>
        <el-button type="primary" @click="submitForm" size="small"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
<script>
import request from "@/utils/request";
let pinyin = require("js-pinyin");
export default {
  components: {},
  props: ["multi", "billcode", "selectValue"],
  data() {
    return {
      title: "数据字典",
      lst: [],
      total: 0,
      radio: -1,
      selrows: "", // 选择的内容 单选变量
      SYSM07B1Visible: false,
      ActiveIndex: -1,
      queryParams: {
        PageNum: 1,
        PageSize: 2000,
        OrderType: 0,
        SearchType: 1,
        OrderBy: "rownum",
      },
      selectIdList: [],
      newVal: [],
      idx: "",
      dialogVisible: false,
      formdata: {
        parentid: "",
        tgvalue: "",
        tgcode: "",
        tglevel: 0,
        tgbillcode: this.billcode,
        lister: JSON.parse(window.localStorage.getItem("getInfo")).realname,
        createby: JSON.parse(window.localStorage.getItem("getInfo")).realname,
      },
      rules: {
        tgvalue: [
          { required: true, message: "请输入货品名称", trigger: "blur" },
        ],
      },
      options: [
        {
          value: "/",
          label: "/",
        },
        {
          value: "-",
          label: "-",
        },
        {
          value: "_",
          label: "_",
        },
      ],
      selTgValue: "",
      selectId: "",
      changeLiItem: {},
      isEdit: false,
    };
  },
  watch: {
    lst: function (val, oldVal) {
      if (val == undefined) {
        this.lst = [];
      }
      for (var i = 0; i < val.length; i++) {
        val[i].rownum = i;
      }
    },
    selectValue(val, oldVal) {
      this.newVal = val;
    },
  },
  computed: {
    lastValue() {
      if (this.newVal.length > 0) {
        return this.newVal[this.newVal.length - 1];
      } else {
        return "";
      }
    },
    editTitle() {
      if (this.selectValue.length > 0) {
        let str = "";
        for (let i = 0; i < this.selectValue.length; i++) {
          str += ` ${this.selectValue[i].tgcode} ${this.selectValue[i].tgvalue}`;
        }
        return str + " 分类";
      } else {
        return "新增一级分类";
      }
    },
  },
  methods: {
    //提交创建
    submitForm() {
      this.$refs.formdata.validate((valid) => {
        if (valid) {
          this.isEdit ? this.submitUpdate() : this.formsave();
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    formsave() {
      console.log(this.formdata, "添加");
      var that = this;
      if (this.idx == 0) {
        request
          .post("/system/SYSM07B1TG/create", that.formdata)
          .then((res) => {
            if (res.data.code == 200) {
              this.$message.success("保存成功");
              this.dialogVisible = false;
              this.formdata.parentid = "";
              this.formdata.tgvalue = "";
              this.formdata.tgcode = "";
              this.selTgValue = "";
              this.formdata.id = "";
              this.bindData("projectData", "");
            }
          })
          .catch((er) => {
            this.$message.warning("保存失败");
          });
      } else if (this.idx != 0) {
        that.formdata.parentid = this.idx;
        request
          .post("/system/SYSM07B1TG/create", that.formdata)
          .then((res) => {
            if (res.data.code == 200) {
              this.$message.success("保存成功");
              this.dialogVisible = false;
              this.formdata.parentid = "";
              this.formdata.tgvalue = "";
              this.formdata.tgcode = "";
              this.formdata.id = "";
              this.selTgValue = "";
              this.bindData("create", this.idx);
            }
          })
          .catch((er) => {
            this.$message.warning("保存失败");
          });
      }
    },
    changeTgcode(val) {
      this.formdata.tgcode = val;
    },
    closeDialog() {
      this.formdata.parentid = "";
      this.formdata.tgvalue = "";
      this.formdata.tgcode = "";
      this.formdata.id = "";
      this.selTgValue = "";
      this.dialogVisible = false;
      this.isEdit = false;
      this.$refs.formdata.resetFields();
    },
    //  单选列方法 07/26修改
    getCurrentRow(row) {
      this.selrows = row ? row : {};
      this.idx = row.id;
      this.$forceUpdate();
      this.$emit("singleSel", row, this.billcode);
    },
    // 加载列表
    bindData(type, paramVal) {
      this.lst = [];
      var scenedata = [
        {
          field: "CiTextGenerator.parentid",
          fieldtype: 4,
          math: "like",
          value: paramVal,
        },
      ];
      if (!!paramVal) {
        this.queryParams.scenedata = scenedata;
        let index = this.selectIdList.findIndex((item) => item === paramVal);
        if (index < 0) {
          this.selectIdList.push(paramVal);
        }
      } else {
        this.$delete(this.queryParams, "scenedata");
        this.queryParams.scenedata = [
          {
            field: "CiTextGenerator.tgbillcode",
            fieldtype: 4,
            math: "like",
            value: this.billcode,
          },
        ];
      }
      request
        .post(
          "/system/SYSM07B1TG/getPageList",
          JSON.stringify(this.queryParams)
        )
        .then((response) => {
          if (response.data.code == 200) {
            if (type === "projectData") {
              this.lst = response.data.data.list.filter(
                (item) => item.parentid === ""
              );
            } else {
              this.lst = response.data.data.list;
            }
            for (var i = 0; i < this.lst.length; i++) {
              this.lst[i].isActive = false;
            }
          }
          this.ActiveIndex = -1;
        })
        .catch((err) => {
          this.$message.warning("获取字典数据失败");
        });
    },
    //返回上一级
    goBack() {
      this.selectIdList.pop();
      this.$emit("deleteItemVal", this.billcode);
      if (this.selectIdList.length === 0) {
        this.bindData(
          "projectData",
          this.selectIdList[this.selectIdList.length - 1]
        );
        this.$message.warning("已经是第一级");
        return;
      } else {
        this.bindData(
          "gooback",
          this.selectIdList[this.selectIdList.length - 1]
        );
      }
    },
    // 查询
    rowIndex({ row, rowIndex }) {
      row.row_index = rowIndex;
    },
    rowClick(row) {
      this.radio = row.row_index;
      this.getCurrentRow(row);
    },
    changeLi(type, index) {
      this.ActiveIndex = index;
      for (var i = 0; i < this[type].length; i++) {
        this[type][i].isActive = false;
      }
      this[type][index].isActive = true;
      this.changeLiItem = this[type][index];
      this.$forceUpdate();
    },
    // ===============================================
    addInput() {
      console.log(this.formdata, "formdata");
      this.dialogVisible = true;
      this.formdata.tgvalue = "";
      this.formdata.tgcode = "";
      this.formdata.parentid = this.selrows.id;
      this.selTgValue = "";
      this.formdata.id = "";
    },
    editInput() {
      console.log(this.formdata, "123");
      this.dialogVisible = true;
      this.isEdit = true;
      this.formdata.tgvalue = this.changeLiItem.tgvalue;
      this.selTgValue = this.changeLiItem.tgcode;
    },
    delItem() {
      this.$confirm("此操作将永久删除该记录, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        request
          .get(`/system/SYSM07B1TG/delete?key=${this.changeLiItem.id}`) // API 接口
          .then((response) => {
            if (response.data.code == 200) {
              this.$message.success("删除成功");
              this.bindData(
                this.changeLiItem.parentid ? "delItem" : "projectData",
                this.selrows.id
              );
            } else {
              this.$message.warning(response.data.msg | "删除失败");
            }
          });
      });
    },
    getMoveUp() {
      if (this.ActiveIndex == 0) {
        this.$message.warning("已经是第一行了！");
        return;
      }
      var row = this.lst[this.ActiveIndex];
      var index = this.lst[this.ActiveIndex].rownum;
      this.lst.splice(index, 1);
      this.lst.splice(index - 1, 0, row);
      this.ActiveIndex -= 1;
      for (var i = 0; i < this.lst.length; i++) {
        this.lst[i].rownum = i;
      }
      let idList = this.lst.map((item) => item.id);
      this.$request
        .post("/system/SYSM07B1TG/orderByRowNum", idList)
        .then((res) => {
          if (res.data.code === 200) {
          } else {
            this.$message.warning("排序失败");
          }
        });
    },
    getMoveDown() {
      if (this.ActiveIndex == this.lst.length - 1) {
        this.$message.warning("已经是最后一行了！");
        return;
      }
      var row = this.lst[this.ActiveIndex];
      var index = this.lst[this.ActiveIndex].rownum;
      this.lst.splice(index, 1);
      this.lst.splice(index + 1, 0, row);
      this.ActiveIndex += 1;
      for (var i = 0; i < this.lst.length; i++) {
        this.lst[i].rownum = i;
      }
      let idList = this.lst.map((item) => item.id);
      this.$request
        .post("/system/SYSM07B1TG/orderByRowNum", idList)
        .then((res) => {
          if (res.data.code === 200) {
          } else {
            this.$message.warning("排序失败");
          }
        });
    },
    submitUpdate() {
      this.formdata.id = this.changeLiItem.id;
      this.formdata.tgcode = this.selTgValue;
      this.formdata.parentid = this.changeLiItem.parentid;
      request
        .post("/system/SYSM07B1TG/update", JSON.stringify(this.formdata))
        .then((res) => {
          if (res.data.code == 200) {
            this.$message.success("保存成功");
            this.dialogVisible = false;
            this.formdata.tgvalue = "";
            this.formdata.tgcode = "";
            this.formdata.id = "";
            if (!this.selrows.tgvalue) {
              this.bindData("projectData", "");
            } else {
              this.bindData("updataItem", this.changeLiItem.parentid);
            }
          }
        })
        .catch((er) => {
          this.$message.warning("保存失败");
        });
      this.isEdit = false;
    },
    clearValidt(val) {
      this.$refs.formdata.clearValidate(val);
    },
    //=================================================
  },
};
</script>
<style lang="scss" scoped>
.goBack {
  width: 150px;
  min-height: 20px;
  padding: 3px 10px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  border-radius: 5px;
  font-size: 14px;
  color: #ccc;
  cursor: pointer;
}
.goBack:hover {
  color: #4ba3eb;
}
.noData {
  text-align: center;
  font-size: 16px;
  color: #9e9e9e;
  transform: translate(0, 30px);
}
.dictionaries_ul {
  padding: 0;
  margin: 0;
  padding-bottom: 4px;
  margin: 0 -12px 30px -12px;
  height: 164px;
  overflow: auto;
  position: relative;
  ul {
    padding: 0;
    margin: 0;
  }
  li {
    list-style: none;
    padding: 6px 10px;
    cursor: pointer;
    span {
      padding-left: 6px;
    }
  }
  li:hover {
    background: #eceff4;
    color: #409eff;
  }
  .active {
    // background: #eceff4;
    color: #409eff;
    font-weight: bold;
  }
}
.foots {
  display: flex;
  justify-content: space-around;
  border-top: 1px solid #dcdfe6;
  box-sizing: border-box;
  cursor: pointer;
  margin: 0 -12px 0px -12px;
  line-height: 34px;
  position: absolute;
  bottom: 0;
  width: 100%;
  span {
    user-select: none;
    flex: 1;
    text-align: center;
    background: #f7f7f7;
    // color: #409eff;
  }
  span:hover {
    color: #409eff;
  }
}
.current-row {
  background: blue;
}
.customDialog {
  display: flex;
  justify-content: space-around;
  .left {
    flex: 1;
    padding: 10px;
    max-height: 200px;
    overflow: auto;
    p {
      text-align: center;
      cursor: pointer;
      padding: 8px 0;
      margin: 0;
      margin-bottom: 4px;
      border-radius: 4px;
    }
    p.isActive {
      background: #409eff;
      color: #fff;
    }
  }
  .right {
    width: 25%;
    display: flex;
    flex-direction: column;

    button {
      margin-bottom: 10px;
    }
  }
}
.myscrollbar::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 6px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 8px;
}

.myscrollbar::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 10px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.3);
  background: #ccc;
}

.myscrollbar::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 3px;
  background: rgba(255, 255, 255, 1);
}
</style>
