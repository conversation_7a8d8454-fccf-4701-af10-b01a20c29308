<template>
  <div>
    <el-dialog
      :title="printTitle ? printTitle : '打印模板'"
      :append-to-body="true"
      width="400px"
      :visible.sync="ReportVisible"
      :close-on-click-modal="false"
      @close="$emit('closePrint')"
    >
      <el-select
        ref="reportModelRef"
        v-model="reportModel"
        placeholder="请选择打印模板"
        style="width: 100%; margin-bottom: 10px"
        @change="changeReport"
      >
        <el-option
          v-for="item in ReportData"
          :key="item.id"
          :label="item.rptname"
          :value="item.id"
        >
          <div class="select_option">
            <div>{{ item.rptname }}</div>
            <i
              v-if="$store.state.user.userinfo.isadmin ? true : false"
              class="el-icon-edit"
              @click.stop="openEdit(item)"
            ></i>
          </div>
        </el-option>
        <div
          class="select_footer"
          v-if="$store.state.user.userinfo.isadmin ? true : false"
        >
          <div @click="openCreate()">新建</div>
          <div @click="$refs.reportModelRef.blur()">关闭</div>
        </div>
      </el-select>
      <div slot="footer" class="dialog-footer">
        <el-button-group style="float: left" v-show="showRemote">
          <el-button
            :type="printType == 'print' ? 'primary' : 'default'"
            :disabled="hasgrfdata"
            size="small"
            icon="el-icon-printer"
            @click.native="
              printType = 'print';
              submitRemoteReport(0);
            "
            >打印</el-button
          >
          <el-button
            :type="printType == 'preview' ? 'primary' : 'default'"
            :disabled="hasgrfdata"
            size="small"
            icon="el-icon-view"
            @click.native="
              printType = 'preview';
              submitRemoteReport(1);
            "
            >预览</el-button
          >
        </el-button-group>
        <el-button
          v-show="showBtn"
          type="primary"
          size="small"
          icon="el-icon-document"
          @click="pdfway === 1 ? submitRemoteReport(2) : submitReport()"
          :disabled="hasrptdata"
          >PDF</el-button
        >
        <el-button size="small" @click="ReportVisible = false">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      :append-to-body="true"
      width="80%"
      :visible.sync="isViewPdf20"
      top="2vh"
      class="pdfDialog"
      :close-on-click-modal="false"
    >
      <iframe
        :src="pdfUrl"
        frameborder="0"
        style="width: 100%; height: 90vh"
      ></iframe>
    </el-dialog>
    <!-- 编辑模板对话框 -->
    <el-dialog
      :visible.sync="modelListVisible"
      :append-to-body="true"
      title="编辑模板"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="editStage" label-width="60px">
        <el-form-item label="名称:" style="font-weight: bold">
          <el-input v-model="editStage.rptname" size="default" />
        </el-form-item>
        <el-form-item label="模板:" style="font-weight: bold">
          <div class="flex a-c" style="flex-wrap: nowrap">
            <el-input
              v-model="editStage.modulecode"
              size="default"
              style="margin-right: 10px"
              readonly
            />
            <el-button
              type="primary"
              size="small"
              @click="openSearchModelList()"
              >克隆</el-button
            >
            <el-button type="primary" size="small" @click="exportImport()"
              >导入</el-button
            >
            <el-button
              type="primary"
              size="small"
              @click="editModuleItem(editStage)"
              >编辑</el-button
            >
          </div>
        </el-form-item>
        <div class="flex a-c j-s">
          <div>
            <el-button-group style="margin-left: 10px" v-if="editStage.id">
              <el-button type="primary" size="small" @click="designBtn()"
                >设计</el-button
              >
              <el-button
                type="danger"
                size="small"
                @click="deleteModuleItem(editStage)"
              >
                删除
              </el-button>
            </el-button-group>
          </div>
          <div>
            <el-button
              type="primary"
              size="small"
              style="width: 80px"
              @click="submitModuleItem(editStage)"
              >确认</el-button
            >
            <el-button
              type="default"
              size="small"
              style="width: 80px"
              @click="modelListVisible = false"
              >取消</el-button
            >
          </div>
        </div>
      </el-form>
    </el-dialog>
    <!-- 编辑模板内容对话框 -->
    <el-dialog
      :visible.sync="editModuleItemVissable"
      title="编辑模板"
      width="50vw"
      :close-on-click-modal="false"
      :append-to-body="true"
    >
      <el-form v-if="editModuleItemVissable">
        <el-form-item label="">
          <el-input
            v-model="grfdataData"
            type="textarea"
            :rows="18"
            placeholder="请输入模板数据"
          />
        </el-form-item>
      </el-form>
      <template slot="footer">
        <span class="dialog-footer">
          <el-button
            type="primary"
            @click="
              editStage.grfdata = grfdataData;
              editModuleItemVissable = false;
            "
            >确认</el-button
          >
          <el-button
            @click="
              grfdataData = '';
              editModuleItemVissable = false;
            "
            >取消</el-button
          >
        </span>
      </template>
    </el-dialog>
    <!-- 模板列表对话框 -->
    <el-dialog
      :visible.sync="searchModelListVisible"
      title="模板列表"
      width="500px"
      :close-on-click-modal="false"
      :append-to-body="true"
    >
      <div v-for="(item, index) in modelList" :key="index">
        <div
          class="modelListItem"
          :class="{ active: modelListActive === index }"
          @click="modelListActive = index"
        >
          <div>
            <span>{{ index + 1 }}、</span>
            <span>{{ item.rptname }}</span>
          </div>
          <div>{{ item.createdate | dateFormat }}</div>
        </div>
      </div>
      <template slot="footer">
        <span class="dialog-footer">
          <el-button type="primary" @click="searchModelListConfirm()"
            >确认</el-button
          >
          <el-button @click="searchModelListVisible = false">取消</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import { Base64 } from "js-base64";
import axios from "axios";
export default {
  name: "PrintServer",
  props: {
    //数据
    formdata: {
      type: [Object, Array],
    },
    //分页参数
    queryParams: {
      type: Object,
    },
    // way：3 勾选的数据
    selectList: {
      type: Array,
    },
    //code
    printcode: {
      type: String,
      required: true,
    },
    //打印接口
    commonurl: {
      type: String,
    },
    //云打印接口
    weburl: {
      type: String,
    },
    // 打印模板接口
    modelurl: {
      type: String,
    },
    // 标题
    printTitle: {
      type: String,
    },
    // 是否显示云打印按钮
    showRemote: {
      type: Boolean,
      default: true,
    },
    showBtn: {
      type: Boolean,
      default: true,
    },
    wsUrl: {
      type: String,
      default: "ws://127.0.0.1:18800",
    },
    pdfFwUrl: {
      type: String,
      default: "/utils/D96M07B1/getGrfReport",
    },
    // 报表中心
    modelCode: {
      type: String,
      default: "system/SYSM07B3",
    },
  },
  data() {
    return {
      // 本组件其他变量
      ReportVisible: false,
      ReportData: [],
      reportModel: "",
      pdfUrl: "",
      isViewPdf20: false,
      printType: "print",
      hasgrfdata: true,
      hasrptdata: true,
      printway: 0, //打印方式
      pdfway: 0, //云打印pdf
      //
      modelListVisible: false,
      editModuleItemVissable: false,
      searchModelListVisible: false,
      modelList: [],
      modelListActive: 0,
      grfdataData: "",
      editStage: {
        rptname: "",
        id: "",
        grfdata: "",
      },
    };
  },
  methods: {
    /**
     * 打印组件弹框显示，打印模板获取
     * way：默认0，1 分页参数打印；2 勾选打印；3、item打印明细；4、formdata表单信息
     * pdfway：取值0，1，兼容submitReport打印
     */
    printButton(way, pdfway) {
      if (!!way) {
        this.printway = way;
        // 判断是否有数据
        if (this.formdata.length == 0) {
          this.$message.warning("没有单据内容可打印");
          return;
        }
      } else {
        this.printway = 0;
      }
      if (!!pdfway) {
        this.pdfway = pdfway;
      } else {
        this.pdfway = 0;
      }
      var printModelUrl = "/system/SYSM07B3/getListByModuleCode";
      if (this.modelurl) {
        printModelUrl = this.modelurl;
      }
      this.$request
        .get(printModelUrl + "?code=" + this.printcode)
        .then((response) => {
          this.ReportData = response.data.data;
          if (this.ReportData.length != 0) {
            this.reportModel = this.ReportData[0].id;
            if (this.pdfway == 1) {
              this.hasgrfdata = !this.ReportData[0].grfdata;
              this.hasrptdata = !this.ReportData[0].grfdata;
            } else {
              this.hasgrfdata = !this.ReportData[0].grfdata;
              this.hasrptdata = !this.ReportData[0].rptdata;
            }
          }
          this.ReportVisible = true;
        });
    },
    //修改打印模板
    changeReport(val) {
      for (var i = 0; i < this.ReportData.length; i++) {
        if (val == this.ReportData[i].id) {
          if (this.pdfway == 1) {
            this.hasgrfdata = !this.ReportData[i].grfdata;
            this.hasrptdata = !this.ReportData[i].grfdata;
          } else {
            this.hasgrfdata = !this.ReportData[i].grfdata;
            this.hasrptdata = !this.ReportData[i].rptdata;
          }
        }
      }
      this.$forceUpdate();
    },
    /**
     * webPDF打印
     * this.printway：1 分页参数打印；2 勾选打印；3、item打印明细；4、formdata表单信息
     */
    submitReport() {
      if (this.reportModel == "") {
        this.$message.warning("打印模板不能为空!");
        return;
      }
      var requestHeader = null;
      if (this.printway == 1) {
        if (!this.queryParams.DateRange) {
          this.queryParams.DateRange = {
            StartDate: getDate()[0],
            EndDate: getDate()[1],
          };
        }
        requestHeader = this.$request.post(
          this.commonurl + "?ptid=" + this.reportModel,
          JSON.stringify(this.queryParams),
          { responseType: "blob" }
        );
      } else if (this.printway == 2) {
        var obj = [];
        for (var i = 0; i < this.formdata.length; i++) {
          var Item = this.formdata[i];
          obj.push(Item.id);
        }
        requestHeader = this.$request.post(
          this.commonurl + "?ptid=" + this.reportModel,
          JSON.stringify(obj),
          { responseType: "blob" }
        );
      } else if (this.printway == 3) {
        var obj = {
          ids: "",
        };
        for (var i = 0; i < this.selectList.length; i++) {
          obj.ids += `'${this.selectList[i].id}',`;
        }
        var reg = /,$/gi;
        obj.ids = obj.ids.replace(reg, "");
        requestHeader = this.$request.post(
          this.commonurl +
            "?key=" +
            this.formdata.id +
            "&ptid=" +
            this.reportModel,
          JSON.stringify(obj),
          { responseType: "blob" }
        );
      } else if (this.printway == 4) {
        requestHeader = this.$request.post(
          this.commonurl + "?ptid=" + this.reportModel,
          JSON.stringify(this.formdata),
          { responseType: "blob" }
        );
      } else {
        requestHeader = this.$request.get(
          this.commonurl +
            "?key=" +
            this.formdata.id +
            "&ptid=" +
            this.reportModel,
          { responseType: "blob" }
        );
      }
      requestHeader.then((response) => {
        if (this.printway == 0) {
          this.formdata.printcount += 1;
        }

        this.ReportVisible = false;
        const binaryData = [];
        binaryData.push(response.data);
        const pdfUrl = window.URL.createObjectURL(
          new Blob(binaryData, { type: "application/pdf" })
        );
        this.pdfUrl = pdfUrl;
        this.isViewPdf20 = true;
      });
    },
    /**
     * 云打印、云预览方法
     * btnnum：打印按钮参数0，1，2，对应云打印、云预览、打印三个按钮
     * this.printway：1 分页参数打印；2 勾选打印；3、item打印明细；4、formdata表单信息
     */
    submitRemoteReport(btnnum) {
      if (this.reportModel == "") {
        this.$message.warning("打印模板不能为空!");
        return;
      }
      var baseUrl = "";
      var requestHeader = null;
      if (this.printway == 1) {
        baseUrl = this.weburl + "?ptid=" + this.reportModel + "&redis=1";
        if (this.printType == "preview") {
          baseUrl = baseUrl + "&cmd=1";
        }
        requestHeader = this.$request.post(
          baseUrl,
          JSON.stringify(this.queryParams)
        );
      } else if (this.printway == 2) {
        baseUrl = this.weburl + "?ptid=" + this.reportModel + "&redis=1";
        if (this.printType == "preview") {
          baseUrl = baseUrl + "&cmd=1";
        }
        var obj = [];
        for (var i = 0; i < this.formdata.length; i++) {
          var Item = this.formdata[i];
          obj.push(Item.id);
        }
        requestHeader = this.$request.post(baseUrl, JSON.stringify(obj));
      } else if (this.printway == 3) {
        var obj = {
          ids: "",
        };
        for (var i = 0; i < this.selectList.length; i++) {
          obj.ids += `'${this.selectList[i].id}',`;
        }
        var reg = /,$/gi;
        obj.ids = obj.ids.replace(reg, "");
        baseUrl =
          this.weburl +
          "?key=" +
          this.formdata.id +
          "&ptid=" +
          this.reportModel +
          "&redis=1";
        if (this.printType == "preview") {
          baseUrl = baseUrl + "&cmd=1";
        }
        requestHeader = this.$request.post(baseUrl, JSON.stringify(obj));
      }else if (this.printway == 5) {
        baseUrl = this.weburl + "?ptid=" + this.reportModel + "&redis=1";
        if (this.printType == "preview") {
          baseUrl = baseUrl + "&cmd=1";
        }
        var obj = [];
        for (var i = 0; i < this.formdata.length; i++) {
          var Item = this.formdata[i];
          obj.push(Item);
        }
        requestHeader = this.$request.post(baseUrl, JSON.stringify(obj));
      } else if (this.printway == 4) {
        baseUrl = this.weburl + "?ptid=" + this.reportModel + "&redis=1";
        if (this.printType == "preview") {
          baseUrl = baseUrl + "&cmd=1";
        }
        requestHeader = this.$request.post(
          baseUrl,
          JSON.stringify(this.formdata)
        );
      } else {
        baseUrl =
          this.weburl +
          "?key=" +
          this.formdata.id +
          "&ptid=" +
          this.reportModel +
          "&redis=1";
        if (this.printType == "preview") {
          baseUrl = baseUrl + "&cmd=1";
        }
        requestHeader = this.$request.get(baseUrl);
      }
      requestHeader.then((response) => {
        if (response.data.code == 200) {
          if (response.data.data == null || response.data.data == "") {
            this.$message.warning(response.data.msg || "打印失败");
          } else {
            // 打开连接WS
            if (btnnum === 0 || btnnum === 1) {
              const webSocketState =
                this.$store.state.webSocketMsg.webSocketState;
              const storedWsUrl = this.$store.state.webSocketMsg.wsurl || "ws://127.0.0.1:18800";
              if (webSocketState != "连接成功" || storedWsUrl != this.wsUrl) {
                this.$setWs.initWebSocket(`${this.wsUrl}`);
                setTimeout(() => {
                  this.remoteprinting(response.data.data);
                }, 500);
                // const waitForConnection = () => {
                //   if (webSocketState == "连接成功") {
                //     this.remoteprinting(response.data.data);
                //   } else {
                //     setTimeout(waitForConnection, 2000); // 每100ms检查一次
                //   }
                // };
              } else {
                setTimeout(() => {
                  this.remoteprinting(response.data.data);
                }, 100);
              }
            } else if (btnnum === 2) {
              this.remoteReport(response.data.data);
            }
          }
        } else {
          this.$message.warning(response.data.msg || "打印失败");
        }
      });
    },
    /**
     * 远程WS打印
     * val：submitRemoteReport数据请求的结果
     */
    remoteprinting(val) {
      if (this.$store.state.webSocketMsg.webSocketState == "连接成功") {
        if (this.printway == 0) {
          this.formdata.printcount += 1;
        }
        this.$setWs.wsSend(val); //发送内容
        this.ReportVisible = false;
      } else {
        this.$message.warning("连接打印服务器失败");
      }
    },
    /**
     * 远程PDF打印
     * val：submitRemoteReport数据请求的结果
     */
    remoteReport(val) {
      this.$request
        .post(this.pdfFwUrl, val, {
          responseType: "blob",
        })
        .then((res) => {
          if (res.status === 200) {
            const binaryData = [];
            binaryData.push(res.data);
            const pdfUrl = window.URL.createObjectURL(
              new Blob(binaryData, { type: "application/pdf" })
            );
            this.pdfUrl = pdfUrl;
            this.isViewPdf20 = true;
          } else {
            this.$message.warning(res.msg || "打印失败");
          }
        });
    },
    // 新建
    openCreate() {
      this.editStage.rptname = "";
      this.editStage.id = "";
      this.editStage.grfdata = "";
      this.editStage.modulecode = this.printcode;
      this.modelListVisible = true;
    },
    // 编辑模板
    openEdit(val, event) {
      if (event) {
        event.preventDefault();
      }
      console.log(val);
      // 清空旧数据
      this.editStage.rptname = "";
      this.editStage.id = "";
      this.editStage.grfdata = "";
      try {
        let baseurl = "system/SYSM07B3/getEntity";
        if (this.modelCode) {
          baseurl = this.modelCode + "/getEntity";
        }
        // 发起请求（注意：确保 request 已经全局引入或通过 import 引入）
        this.$request
          .get(baseurl + "?key=" + val.id)
          .then((res) => {
            if (res.data.code === 200) {
              let resdata = res.data.data;
              this.editStage.rptname = resdata.rptname;
              this.editStage.id = resdata.id;
              this.editStage.modulecode = resdata.modulecode;
              this.editStage.grfdata = resdata.grfdata;
              this.modelListVisible = true;
            } else {
              // 使用 Element UI 的 Message 组件（如果你全局配置了 this.$message）
              this.$message({
                type: "warning",
                message: res.data.msg || "获取失败",
              });
            }
          })
          .catch((error) => {
            this.$message.error(error.message || "请求错误");
          });
      } catch (error) {
        this.$message.error(error.message || "请求错误");
      }
    },
    // 编辑
    editModuleItem(val) {
      this.editModuleItemVissable = true;
      this.editStage.rptname = val.rptname;
      this.editStage.id = val.id;
      this.editStage.modulecode = val.modulecode;
      this.editStage.grfdata = val.grfdata;
      this.grfdataData = val.grfdata;
    },
    // 克隆
    openSearchModelList() {
      try {
        let baseurl = "system/SYSM07B3/getListByModuleCode";
        if (this.modelCode) {
          baseurl = this.modelCode + "/getListByModuleCode";
        }
        this.$request
          .get(baseurl + "?code=" + this.printcode)
          .then((res) => {
            if (res.data.code === 200) {
              const resdata = res.data.data;
              this.modelList = resdata;
              this.searchModelListVisible = true;
            } else {
              this.$message.warning(res.data.msg || "获取失败");
            }
          })
          .catch((error) => {
            this.$message.error(error.message || "请求错误");
          });
      } catch (error) {
        this.$message.error(error.message || "请求错误");
      }
    },
    searchModelListConfirm() {
      try {
        let baseurl = "system/SYSM07B3/getEntity";
        if (this.modelCode) {
          baseurl = this.modelCode + "/getEntity";
        }
        this.$request
          .get(baseurl + "?key=" + this.modelList[this.modelListActive].id)
          .then((res) => {
            if (res.data.code === 200) {
              const resdata = res.data.data;
              this.editStage.grfdata = resdata.grfdata;
              this.searchModelListVisible = false;
            } else {
              this.$message.warning(res.data.msg || "获取失败");
            }
          })
          .catch((error) => {
            this.$message.error(error.message || "请求错误");
          });
      } catch (error) {
        this.$message.error(error.message || "请求错误");
      }
    },
    //添加
    submitModuleItem(row) {
      if (!this.editStage.rptname) {
        this.$message.warning("模板名称不能为空!");
        return;
      }
      if (!this.editStage.grfdata) {
        this.$message.warning("模板内容不能为空!");
        return;
      }

      try {
        console.log("editStage", this.editStage);
        var baseurl =
          "system/SYSM07B3" + (this.editStage.id ? "/update" : "/create");
        if (this.modelCode) {
          baseurl =
            this.modelCode + (this.editStage.id ? "/update" : "/create");
        }
        // return
        var paramObj = Object.assign({}, this.editStage);
        if (!this.editStage.id) {
          delete paramObj.id;
        }
        paramObj.grfdata = Base64.encode(this.editStage.grfdata);
        this.$request.post(baseurl, JSON.stringify(paramObj)).then((res) => {
          if (res.data.code == 200) {
            this.$message.success("保存成功");
            this.printButton(this.printway, this.pdfway);
          } else {
            this.$message.warning(res.data.msg || "保存失败");
          }
        });
      } catch (error) {
        this.$message.error(error.message || "请求错误");
      }
    },
    // 导入
    exportImport() {
      this.$prompt("", "远程导入", {
        confirmButtonText: "确认",
        cancelButtonText: "关闭",
        closeOnClickModal: false,
        inputPlaceholder: "请输入远程地址",
        inputValidator: (value) => {
          // 输入验证函数
          if (!value) {
            return "内容不能为空";
          }
          return true;
        },
        inputErrorMessage: "内容不能为空",
      })
        .then(async ({ value }) => {
          try {
            const res = await axios.get(value);
            if (res.data.code == 200) {
              this.editStage.grfdata = res.data.data;
            }
          } catch (error) {
            this.$message.warning(error || "请求失败");
          }
        })
        .catch(() => {});
    },
    // 删除
    deleteModuleItem(val) {
      this.$confirm(
        "是否确认删除【" + val.rptname + "】，该操作不可逆？",
        "提示",
        {
          confirmButtonText: "确认",
          cancelButtonText: "关闭",
          closeOnClickModal: false,
          type: "warning",
        }
      )
        .then(() => {
          var baseurl = "system/SYSM07B3/delete";
          if (this.modelCode) {
            baseurl = this.modelCode + "/delete";
          }
          this.$request.get(baseurl + "?key=" + val.id).then((res) => {
            if (res.data.code == 200) {
              this.$message.success(res.data.msg || "删除成功");
              this.modelListVisible = false;
              this.printButton(this.printway, this.pdfway);
            } else {
              this.$message.warning(res.data.msg || "删除失败");
            }
          });
        })
        .catch(() => {});
    },
    //设计
    designBtn() {
      // 打开 连接
      // this.$alert("开发中！", "提示");
       let baseurl = "system/SYSM07B3/getGrfDesign";
        if (this.modelCode) {
          baseurl = this.modelCode + "/getGrfDesign";
        }
        baseurl +="?key="+this.editStage.id +"&redis=1";
        this.$request.get(baseurl).then(res=>{
          if(res.data.code==200){
            this.sendPrintWebBill(JSON.stringify(res.data.data))
            // this.$setWs.wsSend(JSON.stringify(res.data.data));
          }else{
            this.$message.warning(res.data.msg||"获取报表设计数据失败")
          }
        })
    
    },
    sendPrintWebBill(val) {
        let webSocketState = this.$store.state.webSocketMsg.webSocketState;
        let storedWsUrl = this.$store.state.webSocketMsg.wsurl || "ws://127.0.0.1:18800";
        if (webSocketState != "连接成功" || storedWsUrl != this.wsUrl) {
        this.$setWs.initWebSocket(`${this.wsUrl}`);
        const waitForConnection = () => {
          if (this.$store.state.webSocketMsg.webSocketState == "连接成功") {
           this.$setWs.wsSend(val);
          } else {
            setTimeout(waitForConnection, 1000); // 每1000ms检查一次
          }
        };
         waitForConnection()
      } else {
        setTimeout(() => {
         this.$setWs.wsSend(val);
        }, 100);
      }
    },
  },
  // 销毁
  destroyed() {
    this.$setWs.wsClose(); //关闭连接
  },
};
</script>
<style scoped lang="scss">
.select_footer {
  display: flex;
  justify-content: space-around;
  font-size: 16px;
  cursor: pointer;
  width: 100%;
  border-top: 1px solid #ccc;
  margin-top: 10px;
}
.select_footer div {
  width: 50%;
  text-align: center;
  padding: 10px 0;
  color: #101010;
}
.select_footer div:first-child {
  border-right: 1px solid #ccc;
}
.select_option {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.modelListItem {
  border: 1px solid #ccc;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
  cursor: pointer;
}
.active {
  border: 1px solid #409eff;
  color: #409eff;
}
</style>
