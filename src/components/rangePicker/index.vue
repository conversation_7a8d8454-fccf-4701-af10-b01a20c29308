<template>
  <div class="single-date-picker">
    <el-date-picker
      v-model="range[0]"
      class="start-picker"
      size="mini"
      :clearable="false"
      value-format="yyyy-MM-dd HH:mm:ss"
      :default-time="'00:00:00'"
      type="date"
      placeholder="开始日期"
      :picker-options="pickerOptions"
      popper-class="singe-date-popper"
      @change="change"
    />
    <span class="separator">~</span>
    <el-date-picker
      v-model="range[1]"
      class="end-picker"
      size="mini"
      value-format="yyyy-MM-dd HH:mm:ss"
      popper-class="singe-date-popper"
      :default-time="'23:59:59'"
      :picker-options="pickerOptions"
      type="date"
      placeholder="结束日期"
      @change="change"
    />
  </div>
</template>
<script>
export default {
  props: {
    dateRange: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      range: this.dateRange,
      pickerOptions: {
        shortcuts: [{
          text: '今天',
          onClick(picker) {
            picker.$emit('pick', new Date())
          }
        }, {
          text: '昨天',
          onClick(picker) {
            const date = new Date()
            date.setTime(date.getTime() - 3600 * 1000 * 24)
            picker.$emit('pick', date)
          }
        }, {
          text: '一周前',
          onClick(picker) {
            const date = new Date()
            date.setTime(date.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', date)
          }
        }]
      }
    }
  },
  methods: {
    change(v) {
      if (!v) {
        this.range = []
      }
      this.$emit('update:dateRange', this.range)
      this.$emit('change', this.range)
    }
  }
}
</script>
<style lang="scss">
.single-date-picker {
  position: relative;
  width: 250px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  .el-date-editor {
    flex: 1;
  }
  .start-picker {
    .el-input__inner {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
      border-right: none;
    }
  }
  .end-picker {
    .el-input__inner {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
      border-left: none;
    }
    .el-input__prefix {
      display: none;
    }
  }
  .separator {
    position: absolute;
    z-index: 1;
    top: 0;
    height: 100%;
    display: flex;
    align-items: center;
  }
}
.singe-date-popper {
  &.has-sidebar {
    width: 324px !important;
  }
  .el-picker-panel__body-wrapper {
    display: flex;
    flex-direction: column;
    .el-picker-panel__sidebar {
      display: flex;
      flex-direction: row;
      order: 2;
      position: relative !important;
      width: 100% !important;
      border-top: 1px solid #e4e4e4;
      padding-top: 0 !important;
      .el-picker-panel__shortcut {
        text-align: center;
        border-color: transparent;
        color: #409eff;
        background: transparent;
        padding-left: 0;
        padding-right: 0;
        &:hover {
          color: #66b1ff;
          border-color: transparent;
          background-color: transparent;
        }
      }
    }
    .el-picker-panel__body {
      margin-left: 0 !important;
    }
  }
}
</style>
