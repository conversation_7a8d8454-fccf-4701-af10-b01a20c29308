<template>
  <div>
    <el-autocomplete
      style="width: 100%"
      :size="size"
      v-model.trim="itemVal"
      :fetch-suggestions="querySearchAsync"
      :trigger-on-focus="true"
      :placeholder="'请选择' + type"
      value-key="name"
      @select="handleSelect"
      @focus="$event.currentTarget.select()"
      clearable
      @clear="blurForBug()"
      :disabled="isdisabled"
    >
      <template slot-scope="{ item }">
        <div>
          <span
            class="groupnameSty"
            :title="item.name"
            :style="{ 'max-width': item.other ? '75%' : '100%' }"
          >
            {{ item.name }}</span
          >
          <span
            v-if="item.other"
            class="selectSpan"
            style="
              float: right;
              text-align: right;
              color: #8492a6;
              font-size: 13px;
            "
            >{{ item.other }}</span
          >
        </div>
      </template>
    </el-autocomplete>
  </div>
</template>
<script>
import { methods } from "jexcel";

export default {
  name: "AutoComplete",
  components: {},
  props: {
    value: {
      type: String,
      default: "",
    },
    type: {
      type: String,
      default: "内容",
    },
    isdisabled: {
      type: Boolean,
      default: false,
    },
    size: {
      type: String,
      default: "small",
    },
    baseurl: {
      type: String,
    },
    method: {
      type: String,
      default: "post",
    },
    params: {
      type: Object,
    },
  },
  data() {
    return {
      lst: [],
      itemVal: "",
      queryParams: {
        PageNum: 1,
        PageSize: 200,
        OrderType: 1,
        SearchType: 1,
      },
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.itemVal = this.value;
      this.$forceUpdate();
    });
  },
  watch: {
    value: function (old, oldVal) {
      this.itemVal = this.value;
    },
  },
  methods: {
    querySearchAsync(queryString, cb) {
      if (queryString != "") {
        this.queryParams.SearchPojo = {};
        for (var key in this.params) {
          console.log(this.params[key]);
          this.queryParams.SearchPojo[this.params[key]] = queryString;
        }
      } else {
        this.$delete(this.queryParams, "SearchPojo");
      }
      var restaurants = [];
      var baseUrl = "/manu/D05M21S12/getPageList";
      if (!!this.baseurl) {
        baseUrl = this.baseurl;
      }
      var param = this.method == "get" ? "" : JSON.stringify(this.queryParams);
      this.$request[this.method](baseUrl, param)
        .then((res) => {
          if (res.data.code == 200) {
            var resdata =
              this.method == "get" ? res.data.data : res.data.data.list;
            this.lst = resdata;
            for (var i = 0; i < resdata.length; i++) {
              var Item = resdata[i];
              var obj = Object.assign({}, Item);
              var obj = {
                value: Item.id,
                name: this.params.name ? Item[this.params.name] : "",
                other: this.params.other ? Item[this.params.other] : "",
              };
              restaurants.push(obj);
            }
          }

          cb(restaurants);
        })
        .catch((er) => {
          cb([]);
        });
    },
    handleSelect(item) {
      var index = this.lst.findIndex((a) => item.value == a.id);
      if (index != -1) {
        this.$emit("setRow", this.lst[index]);
      }
    },
    blurForBug() {
      document.activeElement.blur();
      this.$emit("autoClear");
    },
  },
};
</script>
<style lang="scss" scoped>
.groupnameSty {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-wrap: break-word;
  max-width: 75%;
  display: inline-block;
  line-height: 12px;
}
</style>
