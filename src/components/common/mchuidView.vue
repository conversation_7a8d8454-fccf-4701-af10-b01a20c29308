<template>
  <div>
    <el-popover
      placement="left"
      trigger="click"
      :ref="'popovers'"
      width="200"
      :title="title"
    >
      <div style="position: relative; min-height: 60px">
        <div>
          <div class="ellipsis">
            <i class="el-icon-postcard text-blue"></i> {{ value }}
          </div>
          <div class="ellipsis" title="客户">
            <i class="el-icon-user-solid text-orange"></i>
            {{ formdata.groupname || "-" }}
          </div>
          <!-- <div class="ellipsis" title="制表">
            <i class="el-icon-collection-tag text-peru"></i>
            {{ formdata.lister || "-" }}
          </div> -->
          <div
            class="ellipsis"
            title="附件"
            :class="fileList.length ? 'hasFlie' : ''"
            @click="showFileView"
          >
            <i class="el-icon-paperclip text-green"></i>
            {{ fileList.length || 0 }}
          </div>
        </div>
      </div>
      <span slot="reference" class="textunderline" @click="getInfoVal()">{{
        value
      }}</span>
    </el-popover>
    <el-dialog
      v-if="filevisible"
      title="附件中心"
      :append-to-body="true"
      :visible.sync="filevisible"
      width="60vw"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <div class="material-content">
        <div
          style="
            display: flex;
            flex-wrap: wrap;
            flex: 1;
            align-content: flex-start;
            justify-content: flex-start;
          "
          v-if="fileList.length != 0"
        >
          <div class="img-item" v-for="(i, index) in fileList" :key="index">
            <div class="imgcountent">
              <img
                v-if="i.filesuffix"
                :src="
                  require('@/assets/knows_images/' +
                    getFileType(i.filesuffix) +
                    '.png')
                "
                alt=""
              />
              <img v-else src="@/assets/knows_images/other.png" alt="" />
            </div>
            <div class="imgTitle ellipsis">
              <div
                style="
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                "
              >
                <span
                  class="ellipsis"
                  style="font-weight: bold; display: inline-block; flex: 1"
                  :title="i.fileoriname"
                  >{{ i.fileoriname }}
                </span>
              </div>

              <span>大小：{{ getFileSize(i.filesize) }} </span>
              <span>类型：{{ i.filesuffix }} </span>
              <div style="text-align: right" class="toolBtn">
                <i
                  class="el-icon-picture-outline text-blue"
                  v-if="
                    ['png', 'jpg', 'jpeg', 'bmp', 'gif', 'webp'].includes(
                      i.filesuffix.toLowerCase()
                    )
                  "
                  @click.stop="checkPower(i, 'show')"
                  >预览</i
                >
                <i
                  class="el-icon-download text-blue"
                  @click.stop="checkPower(i, 'download')"
                  >下载</i
                >
              </div>
            </div>
          </div>
        </div>
        <div v-else class="noData">暂无文件内容</div>
      </div>
    </el-dialog>
    <el-image-viewer
      ref="ImageViewerRef"
      v-if="imageViewVisible"
      :visible.sync="imageViewVisible"
      append-to-body
      :on-close="closeViwer"
      :url-list="[ImageUrlList]"
    />
  </div>
</template>
  <script>
import ElImageViewer from "element-ui/packages/image/src/image-viewer";
export default {
  name: "MchuidView",
  components: {
    ElImageViewer,
  },
  props: {
    value: {
      type: String,
      required: true,
    },
    searchUrl: {
      type: String,
    },
    title: {
      type: String,
      default: "销售订单",
    },
    rowInfo: {
      type: Object,
    },
  },
  data() {
    return {
      formdata: {},
      fileList: [],
      filevisible: false,
      ImageUrlList: "",
      imageViewVisible: "",
    };
  },
  methods: {
    async getInfoVal() {
      console.log(this.rowInfo, "rowInfo");
      this.formdata = this.rowInfo;
      this.fileList = [];
      if (this.rowInfo.modulecode) {
        const resdata = await this.$apiCache.request(
          `File_${this.rowInfo.modulecode}_${this.rowInfo.module}`,
          async () => {
            const query = [
              this.rowInfo.modulecode &&
                `modulecode=${this.rowInfo.modulecode}`,
              this.rowInfo.module && `module=${this.rowInfo.module}`,
            ]
              .filter(Boolean)
              .join("&");
            const baseUrl = `/utils/D96M03B1/getList${
              query ? `?${query}` : ""
            }`;
            const response = await this.$request.post(baseUrl);
            return response.data.code === 200 ? response.data.data : [];
          }
        );
        this.fileList = resdata || [];
      } else {
        console.log("查询附件失败");
      }

      // this.$request.get(baseurl).then((res) => {
      //   this.formdata = res.data.data;
      // });
    },
    async checkPower(row, type) {
      const { dirname, filename, fileoriname } = row;
      const token = this.$store.getters.token;
      const baseurl = `/utils/File/proxy/${dirname}/${filename}?sec=${token}`;

      try {
        const res = await this.$request.get(baseurl, { responseType: "blob" });
        const pdfUrl = window.URL.createObjectURL(
          new Blob([res.data], { type: "application/pdf" })
        );

        if (type === "show") {
          this.handlePreview(pdfUrl);
        } else if (type === "download") {
          this.handleDownload(pdfUrl, fileoriname);
        }

        // 统一释放URL对象（防止内存泄漏）
        setTimeout(() => window.URL.revokeObjectURL(pdfUrl), 1000);
      } catch (err) {
        this.$message.error(err?.message || "文件操作失败");
      }
    },

    // 抽离预览逻辑
    handlePreview(pdfUrl) {
      this.ImageUrlList = pdfUrl;
      this.imageViewVisible = true;
      this.$nextTick(() => {
        this.$refs.ImageViewerRef?.$el?.style.setProperty("z-index", "9999");
      });
    },

    // 抽离下载逻辑
    handleDownload(pdfUrl, filename) {
      const link = document.createElement("a");
      link.href = pdfUrl;
      link.download = filename;
      link.style.display = "none";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link); // 清理DOM
    },
    closeViwer() {
      this.ImageUrlList = "";
      this.imageViewVisible = false;
    },
    showFileView() {
      if (!this.fileList.length) return;
      this.filevisible = true;
    },
    // 获取文件大小
    getFileSize(data) {
      var size = Number(data / 1024).toFixed(2) + "KB";
      return size;
    },

    getFileType(fileName) {
      // 根据后缀判断文件类型
      let fileSuffix = "";
      // 结果
      let result = "";
      try {
        let flieArr = fileName.split(".");
        fileSuffix = flieArr[flieArr.length - 1];
      } catch (err) {
        fileSuffix = "";
      }
      // fileName无后缀返回 false
      if (!fileSuffix) {
        result = false;
        return result;
      }

      // 匹配txt
      let txtlist = ["txt"];
      result = txtlist.some(function (item) {
        return item == fileSuffix;
      });
      if (result) {
        result = "txt";
        return result;
      }
      // 匹配 excel
      let excelist = ["xls", "xlsx"];
      result = excelist.some(function (item) {
        return item == fileSuffix;
      });
      if (result) {
        result = "excel";
        return result;
      }
      // 匹配 word
      let wordlist = ["doc", "docx"];
      result = wordlist.some(function (item) {
        return item == fileSuffix;
      });
      if (result) {
        result = "word";
        return result;
      }
      // 匹配 pdf
      let pdflist = ["pdf"];
      result = pdflist.some(function (item) {
        return item == fileSuffix;
      });
      if (result) {
        result = "pdf";
        return result;
      }
      // 匹配 ppt
      let pptlist = ["ppt", "pptx"];
      result = pptlist.some(function (item) {
        return item == fileSuffix;
      });
      if (result) {
        result = "ppt";
        return result;
      }
      // 图片格式
      let imglist = ["png", "jpg", "jpeg", "bmp", "gif", "webp"];
      // 进行图片匹配
      result = imglist.some(function (item) {
        return item == fileSuffix.toLowerCase();
      });
      if (result) {
        result = "image";
        return result;
      }
      result = "other";
      return result;
    },
  },
};
</script>
  <style lang="scss" scoped>
@import "@/styles/mycustom.scss";
.ellipsis {
  cursor: default;
}
.material-content {
  flex: 1;
  background: #fff;
  display: flex;
  flex-wrap: wrap;
  padding: 20px;
  overflow: auto;
  position: relative;
  .img-item {
    //  flex: 1;
    width: 310px;
    height: 120px;
    margin-right: 15px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    border: 1px solid #969696;
    border-radius: 5px;
    position: relative;
    .imgcountent {
      width: 75px;
      height: 80px;
      // margin: 0 auto;
      padding: 8px;
      margin-right: 8px;
      box-sizing: border-box;
      text-align: center;
      // position: relative;
      img {
        // width: 100%;
        width: auto;
        height: 100%;
        max-width: 100%;
        min-width: 60px;
      }
    }
    .imgTitle {
      width: 210px;
      // margin: 15px auto;
      font-size: 14px;
      color: #69696d;
      position: relative;
      span {
        display: block;
        max-width: 176px;
        line-height: 20px;
        cursor: default;
      }
      .delIcon {
        // float: right;
        color: #9e9e9e;
        cursor: pointer;
        font-weight: bold;
      }
      .toolBtn {
        margin-top: 4px;
        i {
          margin-left: 10px;
          cursor: pointer;
        }
      }
    }
    .bgMask {
      position: absolute;
      top: 0;
      left: 0;
      // display: none;
      opacity: 0;
      background: rgba(0, 0, 0, 0.514);
      transition: all 0.3s;
      width: 100%;
      height: 0%;
      .bgMask-btns {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 100%;
        height: 100%;
        line-height: 100px;
        text-align: center;
        z-index: 999;
      }
      i {
        color: #fff;
        cursor: pointer;
        font-size: 30px;
        padding: 0 10px;
      }
      i:hover {
        color: #409eff;
      }
    }
  }
  .bgMask:hover {
    opacity: 1;
    height: 100%;
  }
  .imgcountent:hover + .bgMask {
    opacity: 1;
    height: 100%;
    // display: block;
  }
}
.hasFlie:hover {
  text-decoration: underline;
  cursor: pointer;
}
</style>