<template>
  <div style="height: 100%">
    <div class="flex j-s" style="flex-wrap: nowrap">
      <div class="left">
        <div>
          <el-select
            v-model="exlmodel"
            placeholder="请选择导入模板"
            style="width: 100%; margin-bottom: 10px"
            @change="changeModelRow"
          >
            <template v-for="(i, index) in exlmodeldata">
              <el-option
                :label="i.filename"
                :value="i.id"
                :key="index"
              ></el-option>
            </template>
          </el-select>
        </div>
        <el-button
          type="success"
          icon="el-icon-download"
          :disabled="!exlmodel"
          @click="btnExport"
        >
          下载模板
        </el-button>

        <el-button
          type="primary"
          icon="el-icon-upload"
          :disabled="Object.keys(fileTemp).length == 0"
          @click="btnUpload"
        >
          导入模板
        </el-button>
      </div>
      <div class="center"></div>
      <div class="right">
        <el-upload
          class="upload-demo"
          ref="upload"
          :drag="true"
          action=""
          :multiple="false"
          :on-change="handleChange"
          :on-remove="handleRemove"
          :on-success="handleSuccess"
          :limit="limitUpload"
          :auto-upload="false"
          accept=".xlsx,.xls,.csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">上传文件</div>
        </el-upload>
      </div>
    </div>
    <el-dialog
      title="文件导入"
      width="64vw"
      v-if="importVisble"
      :visible.sync="importVisble"
      append-to-body
      :close-on-click-modal="false"
    >
      <div>
        <div class="flex j-s">
          <el-button-group>
            <el-button
              type="primary"
              icon="el-icon-brush"
              size="mini"
              @click="checkGoodsUid"
              :disabled="lst.length == 0"
            >
              识别编码
            </el-button>
          </el-button-group>
          <div>
            <!-- @change="changeOnline" -->
            <el-checkbox
              v-model="online"
              label="未识别"
              size="mini"
              border
              style="margin-right: 10px; line-height: 20px"
            ></el-checkbox>
            <el-button
              type="primary"
              icon="el-icon-plus"
              size="mini"
              @click="addGoods"
              :disabled="!allowUpload || !selectList.length"
            >
              新增货品
            </el-button>
          </div>
        </div>
        <ve-table
          ref="multipleTable"
          :key="keynum"
          rowKeyFieldName="rowKeys"
          :max-height="'50vh'"
          :scroll-width="tableMinWidth"
          :style="{ 'word-break': 'break-all' }"
          is-horizontal-resize
          :fixed-header="true"
          :columns="columsData"
          :table-data="online == 0 ? lst : lst.filter((item) => !item.goodsid)"
          border-x
          border-y
          :border-around="true"
          :footer-data="footerData"
          :fixed-footer="true"
          :columnHiddenOption="{ defaultHiddenColumnKeys: columnHidden }"
          :column-width-resize-option="{ enable: true, minWidth: 50 }"
          :virtual-scroll-option="virtualScrollOption"
          :checkbox-option="checkboxOption"
        />
      </div>
      <div slot="footer">
        <el-button type="primary" :disabled="!allowUpload" @click="submitExPort"
          >确 定</el-button
        >
        <el-button
          @click="
            importVisble = false;
            $emit('closeDialog');
          "
          >取 消</el-button
        >
      </div>
    </el-dialog>
    <el-dialog
      title="新增货品"
      width="600px"
      v-if="addGoodsVissible"
      :visible.sync="addGoodsVissible"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form
        ref="formdata"
        :model="goodsForm"
        :label-width="'100px'"
        class="custInfo"
        :rules="formRules"
      >
        <el-form-item label="分组名称" prop="uidgroupguid">
          <el-cascader
            ref="cascader"
            v-model="goodsForm.uidgroupguid"
            :options="groupnameOptions"
            :props="defaultProps"
            :show-all-levels="false"
            placeholder="请选择分组名称"
            style="width: 100%"
            @change="handleChangeGroupid"
          >
          </el-cascader>
        </el-form-item>
        <el-form-item label="相关厂商" prop="groupname">
          <autoComplete
            :size="'default'"
            :value="goodsForm.statname"
            :baseurl="'/sale/D01M01B2/getOnlinePageList'"
            :params="{ name: 'groupname', other: 'groupcode' }"
            @setRow="
              goodsForm.groupname = $event.groupname;
              goodsForm.groupid = $event.id;
            "
            @autoClear="
              goodsForm.groupname = '';
              goodsForm.groupid = '';
            "
          ></autoComplete>
        </el-form-item>
        <el-form-item label="默认仓库" prop="storename">
          <autoComplete
            :size="'default'"
            :value="goodsForm.storename"
            :baseurl="'/store/D04M21S1/getPageList'"
            :params="{ name: 'storename' }"
            @setRow="
              goodsForm.storename = $event.storename;
              goodsForm.storeid = $event.id;
              goodsForm.storelistguid = $event.id;
              goodsForm.storelistname = $event.storename;
              $refs.formdata.clearValidate('storename');
            "
            @autoClear="
              goodsForm.storename = '';
              goodsForm.storeid = '';
              goodsForm.storelistguid = '';
              goodsForm.storelistname = '';
            "
          ></autoComplete>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button type="primary" @click="submitAddGoods">确 定</el-button>
        <el-button @click="addGoodsVissible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import autoComplete from "@/components/autoComplete/index";
export default {
  components: {
    autoComplete,
  },
  props: {
    code: {
      type: String,
      default: "",
    },
    filesList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      limitUpload: 1,
      fileTemp: {},
      importVisble: false,
      lst: [],
      listLoading: false,
      allowUpload: false,
      nohasgoods: "",
      //
      exlmodel: "",
      exlmodeldata: [],
      exlmodelrow: {},
      online: false,
      addGoodsVissible: false,
      goodsForm: {
        uidgroupguid: "",
        groupname: "",
        groupid: "",
        storeid: "",
        storename: "",
        storelistguid: "",
        storelistname: "",
      },
      defaultProps: {
        children: "children",
        label: "groupname",
        value: "id",
      },
      //授权仓库
      storeOptions: [],
      // 分组名称
      groupnameOptions: [],
      formRules: {
        storename: [
          { required: true, trigger: "blur", message: "默认仓库为必填项" },
        ],
        uidgroupguid: [
          { required: true, trigger: "blur", message: "分组名称为必填项" },
        ],
      },
      //=====================================
      tableForm: {},
      customList: [],
      editmarkfiles: [],
      keynum: 0,
      columsData: [],
      columnHidden: [], //列隐藏
      footerData: [], // 底部合计行
      copyText: "", //复制的内容
      // 虚拟滚动
      rowScroll: 0,
      virtualScrollOption: {
        enable: true,
        scrolling: ({ startRowIndex }) => {
          this.rowScroll = startRowIndex;
        },
      },
      selectList: [],
      checkboxOption: {
        selectedRowKeys: [],
        selectedRowChange: ({ row, isSelected, selectedRowKeys }) => {
          this.checkboxOption.selectedRowKeys = selectedRowKeys;
          if (selectedRowKeys.includes(row.rowKeys)) {
            this.selectList.push(row);
          } else {
            var index = this.selectList.findIndex(
              (a) => a.rowKeys == row.rowKeys
            );
            if (index != -1) {
              this.selectList.splice(index, 1);
            }
          }
        },
        // 全选
        selectedAllChange: ({ isSelected, selectedRowKeys }) => {
          if (isSelected) {
            this.checkboxOption.selectedRowKeys = selectedRowKeys;
            this.selectList = this.online
              ? JSON.parse(
                  JSON.stringify(this.lst.filter((item) => !item.goodsid))
                )
              : JSON.parse(JSON.stringify(this.lst));
          } else {
            this.selectList = [];
            this.checkboxOption.selectedRowKeys = [];
          }
        },
      },
    };
  },
  computed: {
    tableMinWidth() {
      var tableWidth = "100%";
      if (this.tableForm.item.length != 0) {
        tableWidth = 0;
        for (var i = 0; i < this.tableForm.item.length; i++) {
          var Item = this.tableForm.item[i];
          if (Item.displaymark) {
            tableWidth += Number(Item.minwidth);
          }
        }
      }
      return tableWidth;
    },
  },
  mounted() {
    this.bindDataByUidGroupName();
  },
  watch: {
    lst: function (val, oldVal) {
      for (var i = 0; i < val.length; i++) {
        val[i].rownum = i;
        // 自定义rowKeys
        if (!val[i].rowKeys) {
          val[i].rowKeys = new Date().getTime() + "-" + i;
        }
      }
      this.getSummary();
    },
  },
  methods: {
    getExlModelData() {
      this.allowUpload = false;
      this.$request
        .get(`/system/SYSM06B5/getListByModuleCode?code=${this.code}`)
        .then((res) => {
          if (res.data.code == 200) {
            this.exlmodeldata = res.data.data;
            if (this.exlmodeldata.length) {
              this.exlmodel = this.exlmodeldata[0].id;
              this.exlmodelrow = this.exlmodeldata[0];
              console.log(this.exlmodeldata);
              this.initColumn();
            }
          }
        });
    },
    initColumn() {
      this.tableForm = {
        formcode: this.code,
        item: [],
      };
      var titlejson = this.exlmodelrow.titlejson
        ? JSON.parse(this.exlmodelrow.titlejson)
        : [];
      for (var i = 0; i < titlejson.length; i++) {
        var Item = titlejson[i];
        var obj = {
          itemcode: Item.fieldname,
          itemname: Item.xlsheader,
          minwidth: "100",
          displaymark: 1,
          overflow: 1,
        };
        this.tableForm.item.push(obj);
      }
    },
    async getColumn() {
      var colunmItem = this.tableForm;
      this.$getColumn(colunmItem.formcode, colunmItem).then((data) => {
        this.customList = data.customList;
        this.tableForm = Object.assign({}, data.colList); //表头
        this.initTable(this.tableForm);
        // 获取 允许编辑的 字段
        this.editmarkfiles = [];
        for (var i = 0; i < this.tableForm.item.length; i++) {
          var Item = this.tableForm.item[i];
          if (!!Item.editmark) {
            this.editmarkfiles.push(Item.itemcode);
          }
        }
        this.$forceUpdate();
      });
    },
    initTable(data) {
      var customData = [];
      this.columnHidden = [];
      data.item.forEach((item, index) => {
        var obj = {
          field: item.itemcode,
          key: item.itemcode,
          title: item.itemname,
          width: !isNaN(item.minwidth) ? Number(item.minwidth) : item.minwidth,
          displaymark: item.displaymark,
          fixed: item.fixed ? (item.fixed == 1 ? "left" : "right") : false,
          ellipsis: item.overflow ? { showTitle: true } : false,
          align: item.aligntype ? item.aligntype : "center",
          sortBy: item.sortable ? "" : false,
          edit: false,
          resize: true,
          // operationColumn: item.operationmark ? true : false,
          renderBodyCell: ({ row, column, rowIndex }, h) => {
            return row[item.itemcode];
          },
        };
        // 判断显示隐藏
        if (!item.displaymark) {
          this.columnHidden.push(item.itemcode);
        }
        customData.push(obj);
      });
      customData.unshift({
        field: "",
        key: "checkbox",
        type: "checkbox",
        title: "",
        width: 50,
        align: "center",
        fixed: "left",
      });
      customData.unshift({
        field: "index",
        key: "index",
        title: "ID",
        width: 50,
        align: "center",
        fixed: "left",
        renderBodyCell: ({ row, column, rowIndex }, h) => {
          return rowIndex + this.rowScroll + 1;
        },
      });
      this.columsData = customData;
      this.keynum += 1;
      this.$forceUpdate();
    },
    bindDataByUidGroupName() {
      let queryParams = {
        PageNum: 1,
        PageSize: 10000,
        OrderType: 0,
        SearchType: 1,
        OrderBy: "rownum",
      };
      this.$request
        .post("/goods/D91M01S1/getPageList", JSON.stringify(queryParams))
        .then((res) => {
          if (res.data.code == 200) {
            this.groupnameOptions = this.changeFormat(res.data.data.list);
          }
        })
        .catch((error) => {});
    },
    handleChangeGroupid(val) {
      var uid = val[val.length - 1];
      this.goodsForm.uidgroupguid = val[val.length - 1];
      //   this.changeByGroup(uid);
    },
    //选择的分组发生变化
    async changeByGroup(val) {
      await this.$request
        .get("/goods/D91M01B1/getNewUidByGroup?key=" + val)
        .then((res) => {
          if (res.data.code == 200) {
            var resdata = res.data.data;
            this.$set(this.goodsForm, "goodsuid", resdata.goodsuid);
            this.$set(this.goodsForm, "uidgroupcode", resdata.uidgroupcode);
            this.$set(this.goodsForm, "uidgroupnum", resdata.uidgroupnum);
            this.$set(this.goodsForm, "uidgroupname", resdata.uidgroupname);
            this.$set(this.goodsForm, "batchmg", resdata.batchmg);
            this.$set(this.goodsForm, "batchonly", resdata.batchonly);
            this.$set(this.goodsForm, "packsnmark", resdata.packsnmark);
            this.$set(this.goodsForm, "skumark", resdata.skumark);
          } else {
            this.$message.error(res.data.msg || "请稍后重试");
          }
        });
    },
    getSummary() {
      this.$getSummary(this, ["goodsuid", "amount", "quantity", "taxamount"]);
    },
    // 识别编码
    async checkGoodsUid() {
      let that = this;
      let newGoodsCounter = 0;
      let promiseList = [];
      this.nohasgoods = "";
      for (var i = 0; i < this.lst.length; i++) {
        let promise = new Promise((resolve, reject) => {
          let _that = that;
          let obj = {
            goodsname: that.lst[i].goodsname,
            goodsspec: that.lst[i].goodsspec,
            partid: that.lst[i].partid || "",
            brandname: that.lst[i].brandname || "",
            material: that.lst[i].material || "",
          };
          let lstIndex = i;
          this.$request
            .post("/goods/D91M01B1/getEntityBynsp", JSON.stringify(obj))
            .then((res) => {
              if (res.data.code == 200) {
                if (res.data.data != null) {
                  this.$set(this.lst[lstIndex], "goodsid", res.data.data["id"]);
                  this.$set(
                    this.lst[lstIndex],
                    "goodsuid",
                    res.data.data["goodsuid"]
                  );
                  for (const key of this.filesList) {
                    this.$set(this.lst[lstIndex], key, res.data.data[key]);
                  }
                  resolve(res.data);
                } else {
                  reject(this.lst[lstIndex].goodsname);
                }
              } else {
                reject("查询错误");
              }
            })
            .catch((er) => {
              console.log(er);
              reject("查询失败");
            });
        });
        promiseList.push(promise);
      }
      await Promise.allSettled(promiseList)
        .then((reslst) => {
          for (var i = 0; i < reslst.length; i++) {
            var Item = reslst[i];
            if (Item.status == "fulfilled") {
            } else if (Item.status == "rejected") {
              newGoodsCounter++;
              if (!that.nohasgoods.includes("【" + Item.reason + "】")) {
                that.nohasgoods += "【" + Item.reason + "】";
              }
            }
          }
        })
        .catch((er) => {
          that.$message.warning(er);
        })
        .finally(() => {
          that.$message(
            `识别完毕,共${that.lst.length}条,需导入新货品${newGoodsCounter}条`
          );
          this.allowUpload = true;
          this.selectList = [];
          this.checkboxOption.selectedRowKeys = [];
        });
    },
    submitExPort() {
      try {
        if (this.allowUpload) {
          if (!!this.nohasgoods) {
            this.$alert(`系统中未查询到${this.nohasgoods}`, "提示", {
              type: "warning",
            });
          } else {
            this.importVisble = false;
            this.$emit("saveExport", this.lst);
          }
        } else {
          this.$message.warning("请先识别编码");
        }
      } catch (error) {
        console.log(error);
      }
    },
    //添加文件
    handleChange(file, fileList) {
      this.fileTemp = file.raw;
      const fileName = file.raw.name;
      const fileType = fileName.substring(fileName.lastIndexOf(".") + 1);
      // 判断上传文件格式
      if (this.fileTemp) {
        if (fileType == "xlsx" || fileType == "xls") {
          // this.importf(this.fileTemp);
        } else {
          this.$message.warning("文件格式错误，请删除后重新上传！");
        }
      } else {
        this.$message.warning("请上传文件！");
      }
    },
    //==========通用====================
    changeModelRow(val) {
      var index = this.exlmodeldata.findIndex((a) => a.id == val);
      if (index != -1) {
        this.exlmodel = val;
        this.exlmodelrow = this.exlmodeldata[index];
        this.initColumn();
      }
    },
    btnExport() {
      this.$btnExport([], this.tableForm, this.exlmodelrow.filename);
    },

    btnUpload() {
      const file = this.fileTemp;
      if (!file) {
        this.$message.warning("请上传文件!");
        return;
      }
      this.getColumn();
      const formData = new FormData();
      formData.append("file", file);

      // 发起文件上传请求
      this.$request
        .post(`/system/SYSM06B5/upload?id=${this.exlmodel}`, formData, {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        })
        .then((res) => {
          if (res.data.length) {
            this.lst = [];
            // 遍历处理导入数据
            res.data.forEach((item) => {
              // 移除字符串中去首尾空格
              for (const key in item) {
                if (
                  Object.prototype.hasOwnProperty.call(item, key) &&
                  typeof item[key] === "string"
                ) {
                  item[key] = item[key].trim();
                }
              }
              this.lst.push(item);
            });
            this.importVisble = true;
          } else {
            this.$message.warning("文件导入失败");
          }
        })
        .catch((error) => {
          this.$message.error(error || "请求错误");
        });
    },
    addGoods() {
      if (this.selectList.length) {
        this.goodsForm = {
          uidgroupguid: "",
          groupname: "",
          groupid: "",
          storeid: "",
          storename: "",
          storelistguid: "",
          storelistname: "",
          enabledmark: 1,
        };
        this.addGoodsVissible = true;
      } else {
        this.$message.warning("请选择要新增的货品");
      }
    },
    submitAddGoods() {
      this.$refs["formdata"].validate((valid) => {
        if (valid) {
          this.saveForm();
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    async saveForm() {
      let successCount = 0;
      try {
        for (const item of this.selectList) {
          try {
            await this.changeByGroup(this.goodsForm.uidgroupguid);
            const param = { ...item, ...this.goodsForm };
            const res = await this.$request.post(
              "/goods/D91M01B1/create",
              JSON.stringify(param)
            );

            if (res.data?.code === 200) {
              successCount++;
            } else {
              console.warn(`货品${item.goodsname}保存失败:`, res.data?.message);
            }
          } catch (itemError) {
            console.error(`货品${item.goodsname}保存出错:`, itemError);
          }
        }

        if (successCount > 0) {
          this.$message.success(
            `成功保存${successCount}/${this.selectList.length}条数据`
          );
          this.addGoodsVissible = false;
          this.selectList = [];
          this.checkboxOption.selectedRowKeys = [];
          this.checkGoodsUid();
        } else {
          this.$message.error("所有货品保存均失败");
        }
      } catch (error) {
        console.error("保存过程出错:", error);
        this.$message.error(error || "保存过程中发生错误");
      }
    },
    handlePreview(file) {
      console.log("handlePreview", file);
    },
    handleSuccess(file, fileList) {
      console.log("handleSuccess", file);
    },
    handleRemove(file, fileList) {
      console.log("handleRemove", file);
    },
    changeFormat(data) {
      const result = [];
      if (!Array.isArray(data)) {
        return result;
      }
      data.forEach((item) => {
        delete item.children;
      });
      const map = {};
      data.forEach((item) => {
        map[item.id] = item;
      });
      data.forEach((item) => {
        const parent = map[item.parentid];
        if (parent) {
          (parent.children || (parent.children = [])).push(item);
        } else {
          result.push(item);
        }
      });
      return result;
    },
  },
};
</script>
<style lang="scss" scoped>
.left {
  width: 150px;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  margin-right: 10px;
  button {
    margin: 0 auto;
  }
}
.center {
  border-left: 1px solid #ddd;
  margin: 0 10px;
}
.right {
  flex: 1;
  margin-left: 10px;
  ::v-deep .el-upload-dragger {
    height: 230px;
  }
}
</style>
