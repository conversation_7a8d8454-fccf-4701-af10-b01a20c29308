<template>
  <div class="refNo" style="top: 57px; right: 30px">
    <div class="refNo-item" v-if="showRefNo">
      <span style="margin-right: 10px">
        日期：
        <el-date-picker
          style="width: 150px"
          v-model="formdata.billdate"
          type="date"
          placeholder="选择日期"
          size="mini"
          :readonly="!!formdata.id ? writedate : false"
        ></el-date-picker>
      </span>
      <div>
        <span>NO：</span>
        <el-input
          v-model="formdata.refno"
          placeholder="请输入单号"
          size="mini"
          :readonly="writerefno"
          style="width: 150px"
        />
      </div>
    </div>
    <div style="display: flex; justify-content: flex-end">
      <div style="margin-right: 10px; width: 100px">
        <!-- 清款 -->
        <div v-if="amtstatusfinish" class="amtstatusfinish">
          <svg-icon icon-class="jieqing" style="width: 80px; height: 80px" />
        </div>
        <!-- 收款中 -->
        <div v-if="amtstatusing" class="amtstatusing">
          <svg-icon icon-class="shoukuan" style="width: 90px; height: 90px" />
        </div>
      </div>
      <div style="margin-right: 10px; width: 100px">
        <!-- 已完成 -->
        <svg-icon
          v-if="finished"
          icon-class="finish1"
          style="width: 80px; height: 80px"
        />
        <!-- 已撤销 -->
        <svg-icon
          v-else-if="revoke"
          icon-class="revoke"
          style="width: 80px; height: 80px"
        />
        <!-- 已中止 -->
        <svg-icon
          v-else-if="yizhongzhi"
          icon-class="yizhongzhi"
          style="width: 80px; height: 80px"
        />
        <!-- 已逾期 -->
        <svg-icon
          v-else-if="beoverdue"
          icon-class="beoverdue"
          style="width: 80px; height: 80px"
        />
      </div>
      <div style="margin-right: 10px; width: 90px">
        <!-- 已审核 -->
        <svg-icon
          v-show="approve"
          icon-class="approve"
          style="width: 80px; height: 80px"
        />
        <!-- 审核中 -->
        <svg-icon
          v-show="approving"
          icon-class="approving"
          style="width: 90px; height: 90px"
        />
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    formdata: {
      type: Object,
      required: true,
    },
    showRefNo: {
      type: Boolean,
      default: true,
    },
    writerefno: {
      type: Boolean,
      default: true,
    },
    writedate: {
      type: Boolean,
      default: true,
    },
    // 已完成
    finished: {
      type: Boolean,
      default: false,
    },
    // 已撤销
    revoke: {
      type: Boolean,
      default: false,
    },
    // 已审核
    approve: {
      type: Boolean,
      default: false,
    },
    // 审核中
    approving: {
      type: Boolean,
      default: false,
    },
    // 已中止
    yizhongzhi: {
      type: Boolean,
      default: false,
    },
    // 逾期
    beoverdue: {
      type: Boolean,
      default: false,
    },
    amtstatusfinish: {
      type: Boolean,
      default: false,
    },
    amtstatusing: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {};
  },
};
</script>
<style lang="scss" scoped>
.refNo {
  position: absolute;
  top: 56px;
  right: 30px;
  color: #595959;
  font-size: 14px;
  word-break: break-all;
  line-height: 24px;
  .refNo-item {
    display: flex;
  }
  .amtstatusfinish {
    width: 80px;
    height: 80px;
    font-size: 20px;
    font-weight: 700;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    transform: rotate(30deg);
  }
  .amtstatusing {
    width: 80px;
    height: 80px;
    font-size: 20px;
    font-weight: 700;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    background-color: #fff;
    transform: rotate(30deg);
    color: #409eff;
  }
}
</style>