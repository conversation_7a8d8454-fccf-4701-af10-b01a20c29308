<template>
    <div>
          <el-popover
                placement="right"
                trigger="click"
                :ref="'popover-' + scopeIndex"
                width="200"
                title="货品信息"
              >
                <div style="position: relative; min-height: 100px">
                  <div v-if="!!goodsInfo">
                    <div class="ellipsis">
                      <i class="el-icon-discount text-info"></i> 货品编码：{{
                        goodsInfo.goodsuid
                      }}
                    </div>
                    <div class="ellipsis">
                      <i class="el-icon-coin text-info"></i> 货品名称：{{
                        goodsInfo.goodsname
                      }}
                    </div>
                    <div class="ellipsis">
                          <i class="el-icon-coin text-info"></i> 货品规格：{{
                            goodsInfo.goodsspec
                          }}
                        </div>
                    <div>
                      <i class="el-icon-s-order text-blue"></i> 账面库存：{{
                        goodsInfo.quantity
                      }}
                    </div>
                    <div>
                      <i class="el-icon-s-data text-blue"></i> 可用数量：{{
                        goodsInfo.stoqty
                      }}
                    </div>
                    <div>
                      <i class="el-icon-circle-plus text-green"></i>
                      采购待入：{{ goodsInfo.buyremqty }}
                    </div>
                    <div>
                      <i class="el-icon-remove text-red"></i> 订单待用：{{
                        goodsInfo.busremqty
                      }}
                    </div>
                    <div>
                      <i class="el-icon-remove text-red"></i> 领料待出：{{
                        goodsInfo.requremqty
                      }}
                    </div>
                  </div>
                  <div v-else class="noData" style="font-size:16px">暂无内容</div>
                </div>
                <span
                  slot="reference"
                  class="textunderline"
                  @click="getStockInfo(scopeVal)"
                  >{{showVal?showVal:scopeVal }}</span
                >
              </el-popover>
    </div>
</template>
<script>
import request from "@/utils/request";
export default {
    props:['scopeIndex','scopeVal','showVal'],
    data () {
        return {
            goodsInfo:{}
        }
    },
    methods: {
         getStockInfo(val) {
      if (val == "") {
        return;
      }
      var queryParams = {
        PageNum: 1,
        PageSize: 1,
        OrderType: 1,
        SearchType: 0,
      };
      queryParams.SearchPojo = {
        goodsuid: val,
      };
      request
        .post(
          "/store/D04M04B1/getMachMatQtyPageListByGoods?itemid=" + this.idx,
          JSON.stringify(queryParams)
        )
        .then((res) => {
          console.log(res);
          if (res.data.code == 200) {
            this.goodsInfo = res.data.data.list[0];
          }
        });
    },
    },
}
</script>
<style lang="scss" scoped>
   @import "@/styles/mycustom.scss"; 
</style>