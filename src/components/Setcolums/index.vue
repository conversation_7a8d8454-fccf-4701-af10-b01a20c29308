<template>
  <div>
    <div class="dialog-body customDialog">
      <div class="right">
        <el-button-group>
          <el-button
            type="primary"
            size="mini"
            @click="getMoveUp()"
            :disabled="ActiveIndex == -1"
            icon="el-icon-top"
            >上 移</el-button
          >
          <el-button
            type="primary"
            size="mini"
            @click="getMoveDown()"
            :disabled="ActiveIndex == -1"
            icon="el-icon-bottom"
            >下 移</el-button
          >
        </el-button-group>
        <el-button-group style="margin-right: 12px">
          <!-- <el-button
            type="primary"
            size="mini"
            @click="refreshLeft()"
            v-if="!!formdata.id"
            icon="el-icon-refresh-left"
            v-preventReClick
            >重置</el-button
          > -->
        </el-button-group>
      </div>
      <div class="left">
        <div>
          <table class="productlTable" cellspacing="0" cellpadding="0">
            <thead>
              <tr>
                <th class="tabTh" style="width: 50px">序号</th>
                <th class="tabTh" v-if="showcode">编码</th>
                <th class="tabTh" @dblclick="showcode = !showcode">名称</th>
                <th class="tabTh" style="width: 150px">最小宽度</th>
                <th class="tabTh" style="width: 100px">是否固定</th>
                <th class="tabTh" style="width: 140px">显示/隐藏</th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="(i, index) in lst"
                :key="index"
                @click="ActiveIndex = index"
                :class="ActiveIndex == index ? 'isActive' : ''"
              >
                <td style="width: 50px">{{ index + 1 }}</td>
                <td v-if="showcode">{{ i.itemcode }}</td>
                <td>{{ i.itemname }}</td>
                <td style="width: 150px">
                  <el-input
                    v-if="ActiveIndex == index"
                    v-model="i.minwidth"
                    placeholder="最小宽度"
                    size="small"
                  ></el-input>
                  <span v-else>{{ i.minwidth }}</span>
                </td>
                <td style="width: 100px">
                  <el-checkbox
                    v-model="i.fixed"
                    :true-label="1"
                    :false-label="0"
                  ></el-checkbox>
                </td>
                <td style="width: 140px">
                  <el-switch
                    v-model="i.displaymark"
                    style="margin-right: 10px"
                    inactive-color="#ff4949"
                    :active-value="1"
                    :inactive-value="0"
                  ></el-switch>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div
        style="
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-top: 20px;
          margin-right: 12px;
        "
      >
        <div>
          <el-button
            size="small"
            @click.native="submitUpdateTen()"
            v-if="this.$store.state.user.userinfo.isadmin ? true : false"
            >保存默认格式</el-button
          >
          <el-button
            size="mini"
            icon="el-icon-view"
            @click.native="getDataTen(true)"
            >预览默认列</el-button
          >
        </div>
        <div>
          <!-- id存在并且默认defmark=0 -->
          <el-button
            type="danger"
            size="mini"
            @click="refreshLeft()"
            icon="el-icon-refresh-left"
            v-preventReClick
            >重置</el-button
          >
          <el-button type="primary" size="small" @click.native="submitUpdate()"
            >保 存</el-button
          >
          <el-button size="small" @click="$emit('closeDialog')"
            >关 闭</el-button
          >
        </div>
      </div>
    </div>
    <el-dialog
      title="预览默认列"
      :append-to-body="true"
      width="60vw"
      :visible.sync="showcolumnvisible"
      :close-on-click-modal="false"
    >
      <div>
        <table class="productlTable" cellspacing="0" cellpadding="0">
          <thead>
            <tr>
              <th class="tabTh" style="width: 50px">序号</th>
              <th class="tabTh" v-if="showcode">编码</th>
              <th class="tabTh" @dblclick="showcode = !showcode">名称</th>
              <th class="tabTh" style="width: 150px">最小宽度</th>

              <th class="tabTh" style="width: 100px">是否固定</th>
              <th class="tabTh" style="width: 150px">显示/隐藏</th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="(i, index) in defformdata.item"
              :key="index"
              @click="ActiveIndex = index"
              :class="ActiveIndex == index ? 'isActive' : ''"
            >
              <td style="width: 50px">{{ index + 1 }}</td>
              <td v-if="showcode">{{ i.itemcode }}</td>
              <td>{{ i.itemname }}</td>
              <td style="width: 150px">
                <span>{{ i.minwidth }}</span>
              </td>
              <td style="width: 100px">
                <el-checkbox
                  v-model="i.fixed"
                  :true-label="1"
                  :false-label="0"
                ></el-checkbox>
              </td>
              <td style="width: 150px">
                <el-switch
                  v-model="i.displaymark"
                  style="margin-right: 10px"
                  inactive-color="#ff4949"
                  :active-value="1"
                  :inactive-value="0"
                ></el-switch>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div
        style="
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-top: 20px;
          margin-right: 12px;
        "
      >
        <div>
          <el-button
            size="small"
            @click.native="submitUpdateTen('defformdata')"
            v-if="this.$store.state.user.userinfo.isadmin ? true : false"
            >保存默认列</el-button
          >
        </div>
        <div>
          <el-button type="primary" size="small" @click.native="imptDefData()"
            >导入默认列</el-button
          >
          <el-button size="small" @click="showcolumnvisible = false"
            >关 闭</el-button
          >
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import request from "@/utils/request";
export default {
  inject: ["reload"],
  props: ["code", "tableForm"],
  data() {
    return {
      title: "列设置",
      lst: [],
      formdata: {
        item: [],
      },
      showcolumnvisible: false,
      defformdata: {
        item: [],
      },
      showcode: false,
      ActiveIndex: -1,
      btnType: "Add",
      PwProcessFormVisible: false,
      itemFormRules: {
        itemcode: [
          { required: true, trigger: "blur", message: "编码为必填项" },
        ],
        itemname: [
          { required: true, trigger: "blur", message: "名称为必填项" },
        ],
      },
      itemFormdata: {
        aligntype: "center",
        classname: "",
        defwidth: "",
        displaymark: 1,
        eventname: "",
        fixed: 0,
        formatter: "",
        itemcode: "",
        itemname: "",
        minwidth: 100,
        overflow: 1,
        pid: this.idx,
        remark: "",
        rownum: 0,
        sortable: 0,
      },
    };
  },
  watch: {
    lst: function (val, oldVal) {
      if (val == undefined) {
        this.lst = [];
      }
      for (var i = 0; i < val.length; i++) {
        val[i].rownum = i;
      }
    },
  },
  created() {},
  mounted() {
    this.$nextTick(() => {
      this.bindData();
    });
  },
  methods: {
    // 加载列表
    bindData() {
      // this.formdata =Object.assign({}, this.tableForm); //浅拷贝:原数据中包含子对象改变会使数据一同改变
      this.formdata = JSON.parse(JSON.stringify(this.tableForm)); // 深拷贝
      // 如果获取到的时默认列，删除默认列id
      if (this.formdata.defmark == 1) {
        this.$delete(this.formdata, "id");
      }
      this.lst = [].concat(this.formdata.item);
    },
    // 重置表单
    restForm() {
      this.$refs.itemFormdata.resetFields();
    },
    refreshLeft() {
      var that = this;
      this.$confirm("此操作将初始化表格内容, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          request
            .get("/system/SYSM07B9/deleteByCode?code=" + that.formdata.formcode)
            .then((res) => {
              if (res.data.code == 200) {
                that.$message.success(res.data.msg || "重置内容成功");
                that.getColumn(that.formdata.formcode);
                that.$emit("bindData");
                that.$emit("closeDialog");
              } else {
                that.$message.warning(res.data.msg || "重置内容失败");
              }
            });
        })
        .catch(() => {});
    },
    // ===============================================
    getMoveUp() {
      if (this.ActiveIndex == 0) {
        this.$message.warning("已经是第一行了！");
        return;
      }
      var row = this.lst[this.ActiveIndex];
      var index = this.lst[this.ActiveIndex].rownum;
      this.lst.splice(index, 1);
      this.lst.splice(index - 1, 0, row);
      this.ActiveIndex -= 1;
      for (var i = 0; i < this.lst.length; i++) {
        this.lst[i].rownum = i;
      }
    },
    getMoveDown() {
      if (this.ActiveIndex == this.lst.length - 1) {
        this.$message.warning("已经是最后一行了！");
        return;
      }
      var row = this.lst[this.ActiveIndex];
      var index = this.lst[this.ActiveIndex].rownum;
      this.lst.splice(index, 1);
      this.lst.splice(index + 1, 0, row);
      this.ActiveIndex += 1;
      for (var i = 0; i < this.lst.length; i++) {
        this.lst[i].rownum = i;
      }
    },
    getDataTen(isTrue) {
      this.defformdata = {
        item: [],
      };
      var BaseUrl =
        "/system/SYSM07B9/getTenBillEntityByCode?code=" +
        this.tableForm.formcode;
      request
        .get(BaseUrl)
        .then((res) => {
          if (res.data.code == 200) {
            // 返回0条数据时
            if (res.data.data == null || !res.data.data) {
              this.$message.warning(
                res.data.msg || "未查询到默认列，请联系管理员设置默认列"
              );
              return;
            } else {
              this.defformdata = res.data.data;
              if (isTrue) {
                this.showcolumnvisible = true;
              }
            }
          } else {
            this.$message.warning(res.data.msg || "获取默认列失败");
          }
        })
        .catch((er) => {
          this.$message.err(er || "请求错误");
        });
    },
    imptDefData() {
      this.lst = [];
      //将默认列的内容赋值给this.lst
      for (var i = 0; i < this.defformdata.item.length; i++) {
        var Item = this.defformdata.item[i];
        this.$delete(Item, "id");
        this.$delete(Item, "pid");
        this.lst.push(Item);
      }
      this.showcolumnvisible = false;
      this.$forceUpdate();
    },
    // 租户-默认-保存
    submitUpdateTen(lstType) {
      var obj = Object.assign({}, this.formdata);
      obj.item = this.lst;
      if (lstType == "defformdata") {
        obj = Object.assign({}, this.defformdata);
        obj.item = this.defformdata.item;
      }
      obj.enabledmark = 1;
      obj.defmark = 1; // 租户默认
      var BaseUrl = "/system/SYSM07B9/updateTen";
      request
        .post(BaseUrl, JSON.stringify(obj))
        .then((res) => {
          if (res.data.code == 200) {
            this.$message.success(res.data.msg || "保存成功");
          } else {
            this.$message.warning(res.data.msg || "保存失败");
          }
        })
        .catch((err) => {
          this.$message.error(err || "请求错误");
        });
    },
    // 个人-保存
    submitUpdate() {
      this.formdata.item = this.lst;
      this.formdata.enabledmark = 1;
      this.formdata.defmark = 0; // 个人默认为0
      // return
      // if (!this.formdata.id) {
      //   var BaseUrl = "/system/SYSM07B9/create";
      // } else {
      //   var BaseUrl = "/system/SYSM07B9/update";
      // }
      var BaseUrl = "/system/SYSM07B9/saveByCode";
      request
        .post(BaseUrl, JSON.stringify(this.formdata))
        .then((res) => {
          if (res.data.code == 200) {
            this.$message.success(res.data.msg || "保存成功");
            this.formdata.id = res.data.data.id;
            this.$forceUpdate();
            this.$emit("bindData");
            this.$emit("closeDialog");
          } else {
            this.$message.warning(res.data.msg || "保存失败");
          }
        })
        .catch((err) => {
          this.$message.error(err || "请求错误");
        });
    },
    //
    getColumn(val) {
      request
        .get("/system/SYSM07B9/getBillEntityByCode?code=" + val)
        .then((res) => {
          if (res.data.code == 200) {
            if (res.data.data == null) {
              this.formdata.id = "";
              this.$forceUpdate();
              return;
            }
            this.formdata = res.data.data;
            this.lst = res.data.data.item;
            this.$forceUpdate();
          }
        })
        .catch((error) => {
          this.$message.error("请求出错");
        });
    },
    //=================================================
    cleValidate(val) {
      this.$refs.itemFormdata.clearValidate(val);
    },
  },
};
</script>
<style lang="scss" scoped>
.productlTable {
  border-left: 1px solid #ddd;
  text-align: center;
  width: 100%;

  td {
    border: 1px solid #ddd;
    border-top: 0px solid #ddd;
    border-left: 0px solid #ddd;
    padding: 8px 10px;
    // min-width: 100px;
    // max-width: 300px;
  }
  .tabTh {
    background: #f3f4f7;
    padding: 8px 10px;
    border: 1px solid #ddd;
    border-left: 0px solid #ddd;
  }
  tbody {
    display: block;
    max-height: 520px;
    overflow-y: scroll;
    /*滚动条样式*/
    &::-webkit-scrollbar {
      width: 8px;
      // height: 8px;
    }
    &::-webkit-scrollbar-thumb {
      cursor: pointer;
      border-radius: 4px;
      -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.4);
      background: rgba(0, 0, 0, 0.4);
    }
    &::-webkit-scrollbar-track {
      -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0);
      border-radius: 4px;
      background: rgba(0, 0, 0, 0);
    }
  }
  thead,
  tbody tr {
    display: table;
    width: 100%;
    table-layout: fixed;
  }
  thead {
    width: calc(100% - 8px);
  }
}
.customDialog {
  .left {
    // border: 1px solid #c5c2c2;
    width: 100%;
    overflow: auto;
    p {
      text-align: center;
      cursor: pointer;
      padding: 8px 0;
      margin: 0;
      margin-bottom: 4px;
      border-radius: 4px;
    }
    .isActive {
      background: #c1dcf7;
    }
  }
  .right {
    display: flex;
    justify-content: space-between;
  }
}
</style>
