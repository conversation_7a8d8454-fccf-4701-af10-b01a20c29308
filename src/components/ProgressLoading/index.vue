<template>
  <div class="progress-loading-container">
    <!-- 全屏Loading层 -->
    <div 
      v-show="visible" 
      class="progress-loading-mask" 
      :style="{ backgroundColor: background }"
    >
      <div class="progress-loading-content">
        <!-- ElementUI Spinner -->
        <div v-if="showSpinner" class="progress-loading-spinner">
          <i :class="spinner"></i>
          <!-- 自定义文本 -->
            <p v-if="text" class="progress-loading-text">{{ text }}</p>
        </div>
        
        <!-- ElementUI Progress   :status="status"-->
        <el-progress
          :percentage="percentage"
         
          :stroke-width="strokeWidth"
          :color="customColor"
          :show-text="showProgressText"
          :text-inside="textInside"
          :text-color="customColor"
          class="progress-loading-bar"
        ></el-progress>
        
        
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ProgressLoading',
  props: {
    // 控制显示/隐藏
    visible: {
      type: Boolean,
      default: false
    },
    // 当前进度 (0-100)
    percentage: {
      type: Number,
      default: 0,
      validator: val => val >= 0 && val <= 100
    },
    // 进度条状态
    status: {
      type: String,
      default: '',
      validator: val => ['','success', 'exception', 'warning'].includes(val)
    },
    // 是否显示spinner图标
    showSpinner: {
      type: Boolean,
      default: true
    },
    // spinner图标类名
    spinner: {
      type: String,
      default: 'el-icon-loading'
    },
    // 进度条高度
    strokeWidth: {
      type: Number,
      default: 6
    },
    // 自定义进度条颜色
    color: {
      type: [String, Function, Array],
      default: ''
    },
    // 是否显示进度文本
    showProgressText: {
      type: Boolean,
      default: true
    },
    // 文本是否显示在进度条内部
    textInside: {
      type: Boolean,
      default: false
    },
    // 自定义文本内容
    text: {
      type: String,
      default: '加载中...'
    },
    // 背景色
    background: {
      type: String,
      default: 'rgba(0, 0, 0, 0.8)'
    },
    // 是否锁定屏幕(禁止滚动)
    lock: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    customColor() {
      if (this.color) return this.color
      return this.status === 'exception' ? '#f56c6c' 
           : this.status === 'success' ? '#67c23a' 
           : this.status === 'warning' ? '#e6a23c' 
           : '#409eff'
    }
  },
  watch: {
    lock(newVal) {
      if (newVal) {
        document.body.style.overflow = 'hidden'
      } else {
        document.body.style.overflow = ''
      }
    }
  },
  mounted() {
    if (this.lock) {
      document.body.style.overflow = 'hidden'
    }
  },
  beforeDestroy() {
    document.body.style.overflow = ''
  }
}
</script>

<style scoped lang="scss">
.progress-loading-container {
  position: relative;
}

.progress-loading-mask {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: opacity 0.3s;
}

.progress-loading-content {
  width: 50%;
  max-width: 400px;
  min-width: 200px;
  text-align: center;
}

.progress-loading-spinner {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.progress-loading-spinner i {
  font-size: 42px;
  color: #409eff;
  animation: rotating 2s linear infinite;
}

.progress-loading-text {
  margin-top: 16px;
  color: #409eff;
  font-size: 16px;
  margin-left:10px;
}
::v-deep .el-progress__text{
    color: #409eff;
}
@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>