<template>
  <el-dialog
    :close-on-click-modal="false"
    :visible.sync="shows"
    width="46vw"
    append-to-body
  >
    <div slot="title" @dblclick="showCode = !showCode" style="cursor: default">
      {{ title + (showCode ? "-" + code : "") }}
    </div>
    <el-form
      ref="formdata"
      :model="formdata"
      label-width="100px"
      class="custInfo"
      auto-complete="on"
      :rules="formRules"
    >
      <el-row>
        <el-col :span="12">
          <div @click="cleValidate('counttype')">
            <el-form-item
              label="类型(年月日)"
              prop="counttype"
              label-width="120px"
            >
              <el-select
                v-model="formdata.counttype"
                placeholder="请选择"
                style="width: 100%"
              >
                <el-option label="日" value="day" />
                <el-option label="月" value="month" />
                <el-option label="年" value="year" />
              </el-select>
            </el-form-item>
          </div>
        </el-col>
        <el-col :span="10">
          <el-form-item label="跳步">
            <el-input-number
              v-model="formdata.step"
              controls-position="right"
              :min="1"
              :max="10"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-divider />
      <el-row v-for="(item, index) in 5" :key="index">
        <el-col :span="10">
          <el-form-item :label="'前缀' + (index + 1)">
            <el-select
              v-model="formdata['prefix' + (index + 1)]"
              filterable
              allow-create
              clearable
              :placeholder="'前缀' + (index + 1)"
              size="small"
              style="width: 100%"
              :disabled="disabledForm['prefix' + (index + 1)]"
            >
              <el-option label="yyyy" value="yyyy" />
              <el-option label="yy" value="yy" />
              <el-option label="MM" value="MM" />
              <el-option label="DD" value="DD" />
              <el-option label="dd" value="dd" />
              <el-option label="[00]" value="[00]" />
              <el-option label="[000]" value="[000]" />
              <el-option label="[0000]" value="[0000]" />
              <el-option label="[00000]" value="[00000]" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item :label="'后缀' + (index + 1)">
            <el-select
              v-model="formdata['suffix' + (index + 1)]"
              filterable
              allow-create
              clearable
              :placeholder="'后缀' + (index + 1)"
              size="small"
              style="width: 100%"
              :disabled="disabledForm['suffix' + (index + 1)]"
            >
              <el-option label="-" value="-" />
              <el-option label="/" value="/" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-divider />
      <el-row style="margin-top: 15px">
        <el-col :span="24">
          <el-form-item
            label="摘  要"
            label-position="right"
            label-width="100px"
          >
            <el-input
              v-model="formdata.remark"
              type="input"
              placeholder="请输入摘要"
              clearable
              size="small"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="创建人">
            <span v-show="formdata.createby">{{ formdata.createby }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="创建日期">
            <span v-show="formdata.createdate">{{
              formdata.createdate | dateFormat
            }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="制表">
            <span v-show="formdata.lister">{{ formdata.lister }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="修改日期">
            <span v-show="formdata.createdate">{{
              formdata.modifydate | dateFormat
            }}</span>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click.native="submitForm()">保 存</el-button>
      <el-button @click="shows = false">关 闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  props: {
    show: Boolean,
    code: {
      type: String,
    },
    title: {
      type: String,
      default: "单据编码",
    },
  },
  data() {
    return {
      // 表单参数
      formdata: {
        modulecode: "", // 功能编码
        billname: "", // 功能名称
        prefix1: "",
        prefix2: "",
        prefix3: "",
        prefix4: "",
        prefix5: "",
        suffix1: "",
        suffix2: "",
        suffix3: "",
        suffix4: "",
        suffix5: "",
        counttype: "", // 年月日
        step: 1, // 跳步
        currentnum: 0, // 当前序号
        tablename: "", // 数据表
        datecolumn: "CreateDate", // 时间字段
        columnname: "RefNo", // 列名
        dbfilter: "", // 数据过滤
        allowedit: 0, // 编号允许修改
        allowdelete: 0, // 允许删除复用
        lister: JSON.parse(window.localStorage.getItem("getInfo")).realname,
        createby: JSON.parse(window.localStorage.getItem("getInfo")).realname,
      },
      formRules: {
        counttype: [
          { required: true, trigger: "blur", message: "类型(年月日)为必填项" },
        ],
      },
      disabledForm: {
        prefix1: false,
        prefix2: false,
        prefix3: false,
        prefix4: false,
        prefix5: false,
        suffix1: false,
        suffix2: false,
        suffix3: false,
        suffix4: false,
        suffix5: false,
      },
      lst: [],
      showCode: false,
    };
  },
  computed: {
    shows: {
      get() {
        return this.show;
      },
      set(newValue) {
        this.$emit("update:show", newValue);
      },
    },
  },
  watch: {
    idx: function (val, oldVal) {
      this.bindData();
    },
    "formdata.prefix1": function (val, oldVal) {
      this.isdisable(val, 1);
    },
    "formdata.prefix2": function (val, oldVal) {
      this.isdisable(val, 2);
    },
    "formdata.prefix3": function (val, oldVal) {
      this.isdisable(val, 3);
    },
    "formdata.prefix4": function (val, oldVal) {
      this.isdisable(val, 4);
    },
    "formdata.prefix5": function (val, oldVal) {
      this.isdisable(val, 5);
    },
  },
  async mounted() {
    await this.bindData();
  },
  methods: {
    bindData() {
      this.$request
        .get("/system/SYSM07B2/getEntityByCode?modulecode=" + this.code)
        .then((res) => {
          if (res.data.code === 200) {
            this.formdata = res.data.data;
          } else {
            // this.$message.error(res.data.msg || "获取数据失败");
            this.$alert(res.data.msg || "获取数据失败", "错误", {
              confirmButtonText: "确定",
              type: "warning",
              callback: (action) => {
                this.shows = false;
              },
            });
          }
        })
        .catch((error) => {
            this.$alert(error || "请求失败", "错误", {
              confirmButtonText: "确定",
              type: "error",
              callback: (action) => {
                this.shows = false;
              },
            });
        //   this.$message.error(error || "请求失败");
        });
    },
    submitForm() {
      this.$refs.formdata.validate((valid) => {
        if (valid) {
          this.formsave();
        } else {
          return false;
        }
      });
    },
    formsave() {
      if (!this.formdata.id) {
        this.$message.warning("未查询到数据，请联系管理员添加！");
        return;
      }
      this.$request
        .post("/system/SYSM07B2/update", JSON.stringify(this.formdata))
        .then((res) => {
          if (res.data.code === 200) {
            this.$message.success("保存成功");
            this.formdata=res.data.data;
          } else {
            this.$message.warning(res.message || "保存失败");
          }
        })
        .catch((err) => {
          this.$message.error(err || "保存失败");
          console.error("保存出错:", err);
        });
    },
    isdisable(val, num) {
      if (
        val == "[00]" ||
        val == "[000]" ||
        val == "[0000]" ||
        val == "[00000]"
      ) {
        for (var i = num; i < 6; i++) {
          if (i != num) {
            this.formdata["prefix" + i] = "";
            this.disabledForm["prefix" + i] = true;
          }
          this.formdata["suffix" + i] = "";
          this.disabledForm["suffix" + i] = true;
        }
      } else {
        for (var i = num; i < 6; i++) {
          if (i != num) {
            this.disabledForm["prefix" + i] = false;
          }
          this.disabledForm["suffix" + i] = false;
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-form-item {
  margin-bottom: 4px !important;
}
</style>
