<template>
  <div>
    <el-dropdown
      trigger="click"
      :hide-on-click="false"
      placement="bottom"
      style="margin: 0 10px"
    >
      <el-button
        size="small"
        style="padding: 7px 15px"
        icon="el-icon-edit-outline"
        >快选<i class="el-icon-caret-bottom el-icon--right"></i
      ></el-button>
      <el-dropdown-menu slot="dropdown" class="quickTick myscrollbar">
        <div class="fixed">
          <div class="searchInput">
            <el-input
              v-model.trim="searchVal"
              placeholder="搜索"
              size="small"
              clearable
              @focus="$event.currentTarget.select()"
              @input="$emit('quickTickSearch', searchVal)"
            ></el-input>
          </div>
          <el-dropdown-item style="border-bottom: 1px solid #edecec;">
            <el-checkbox
              :indeterminate="isIndeterminate"
              v-model="checkAll"
              @change="changeAll"
              >全选</el-checkbox
            ></el-dropdown-item
          >
        </div>
        <!-- <div style="margin-top:72px;"> -->
        <el-dropdown-item v-for="(item, index) in droupList" :key="index">
            <el-tooltip effect="dark" :content="item.label" placement="left">
          <el-checkbox
            :label="item.label"
            v-model="item.show"
            @change="$emit('quickTickBtn', item)"
          >
              <span class="quickTickLabel">{{ item.label }}</span>
          </el-checkbox>
            </el-tooltip>
        </el-dropdown-item>
        <!-- </div> -->
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>
<script>
export default {
  props: ["droupList"],
  //   droupList——下拉列表内容
  data() {
    return {
      checkAll: false,
      isIndeterminate: false,
      searchVal: "",
    };
  },
  created() {
    // this.init();
  },
  watch: {
    droupList: function (val, oldVal) {
      this.checkAll = true;
      for (var i = 0; i < this.droupList.length; i++) {
        if (!this.droupList[i].show) {
          this.checkAll = false;
          break;
        }
      }
    },
  },
  methods: {
    changeAll(val) {
      for (var i = 0; i < this.droupList.length; i++) {
        var Item = this.droupList[i];
        Item.show = val;
        this.$emit("quickTickBtn", Item);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-checkbox__label {
  line-height: 12px;
}
.quickTick {
  overflow-y: auto; // 添加滚动条
  max-height: 360px; // 最大高度
  overflow-x: hidden; // 隐藏x轴上的滚动条
  width: 180px;
  position: relative;
  padding: 0 !important;
}
.fixed {
    position: sticky;
    top: 0;
    z-index: 9999;
    background: #FFF;
}
.quickTickLabel{
     width: 120px;
    display: inline-block;
    word-wrap: break-word;
    text-overflow: ellipsis;
    overflow: hidden;
}
.searchInput {
  padding:10px 10px 0 10px;
  margin-bottom: 4px;
  // border-bottom: #f3f4f7 1px solid;
}
.myscrollbar::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 6px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 8px;
}

.myscrollbar::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 10px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.5);
  background: #f3f4f7;
}

.myscrollbar::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 3px;
  background: rgba(255, 255, 255, 1);
}

</style>