<template>
  <div>
    <el-dropdown
      trigger="click"
      :hide-on-click="false"
      placement="bottom"
      style="margin: 0 10px"
    >
      <el-button
        size="small"
        style="padding: 7px 15px"
        icon="el-icon-edit-outline"
        >快选<i class="el-icon-caret-bottom el-icon--right"></i
      ></el-button>
      <el-dropdown-menu slot="dropdown" class="quickTick myscrollbar">
        <div class="fixed">
          <div
            style="
              display: flex;
              justify-content: space-between;
              margin: 10px 10px 0 10px;
            "
          ></div>
          <div class="searchInput">
            <el-input
              v-model.trim="searchVal"
              placeholder="搜索"
              size="small"
              clearable
              @focus="$event.currentTarget.select()"
              @input="$emit('quickTickSearch', searchVal)"
              style="width: 64%; margin-right: 6px"
            ></el-input>
            <!--  -->
            <div style="display: flex; justify-content: space-between">
              <el-button
                type="primary"
                size="mini"
                @click="addFlowcode()"
                icon="el-icon-plus"
                style="padding: 6px"
              ></el-button>
              <el-button
                type="primary"
                size="mini"
                @click="$emit('getflowcodeData')"
                icon="el-icon-refresh-right"
                style="padding: 6px"
              ></el-button>
            </div>
          </div>

          <el-dropdown-item style="border-bottom: 1px solid #edecec">
            <el-checkbox
              :indeterminate="isIndeterminate"
              v-model="checkAll"
              @change="changeAll"
              >全选</el-checkbox
            ></el-dropdown-item
          >
        </div>
        <!-- <div style="margin-top:72px;"> -->
        <el-dropdown-item v-for="(item, index) in droupList" :key="index">
          <el-tooltip effect="dark" :content="item.dictvalue" placement="left">
            <el-checkbox
              :label="item.dictvalue"
              v-model="item.show"
              @change="$emit('quickTickBtn', item)"
            >
              <span class="quickTickLabel">{{ item.dictvalue }}</span>
            </el-checkbox>
          </el-tooltip>
        </el-dropdown-item>
        <!-- </div> -->
      </el-dropdown-menu>
    </el-dropdown>
    <div v-show="false">
      <selDictionaries
        ref="flowcodeRef"
        :multi="0"
        :billcode="'mat_bomitem.flowcode'"
        style="width: 200px"
      />
    </div>
  </div>
</template>
<script>
import selDictionaries from "@/views/modules/SYS/SYSM07B1/components/select.vue";
export default {
  props: ["droupList"],
  components: {
    selDictionaries,
  },
  //   droupList——下拉列表内容
  data() {
    return {
      checkAll: false,
      isIndeterminate: false,
      searchVal: "",
      //
      flowcodeRefVisible: false,
    };
  },
  created() {
    // this.init();
  },
  mounted() {
    // console.log(this.droupList, "droupList");
  },
  watch: {
    droupList: function (val, oldVal) {
      this.checkAll = true;
      for (var i = 0; i < this.droupList.length; i++) {
        if (!this.droupList[i].show) {
          this.checkAll = false;
          break;
        }
      }
    },
  },
  methods: {
    changeAll(val) {
      for (var i = 0; i < this.droupList.length; i++) {
        var Item = this.droupList[i];
        Item.show = val;
        this.$forceUpdate();
        this.$emit("quickTickBtn", Item);
      }
    },
    addFlowcode() {
      setTimeout(() => {
        this.$refs.flowcodeRef.bindData();
        this.$refs.flowcodeRef.SYSM07B1Visible = true;
      });
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-checkbox__label {
  line-height: 12px;
}
.quickTick {
  overflow-y: auto; // 添加滚动条
  max-height: 300px; // 最大高度
  overflow-x: hidden; // 隐藏x轴上的滚动条
  width: 220px;
  position: relative;
  padding: 0 !important;
}
.fixed {
  position: sticky;
  top: 0;
  z-index: 9999;
  background: #fff;
}
.quickTickLabel {
  width: 120px;
  display: inline-block;
  word-wrap: break-word;
  text-overflow: ellipsis;
  overflow: hidden;
}
.searchInput {
  padding: 10px 10px 0 10px;
  margin-bottom: 4px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  // border-bottom: #f3f4f7 1px solid;
}
.myscrollbar::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 6px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 8px;
}

.myscrollbar::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 10px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.5);
  background: #f3f4f7;
}

.myscrollbar::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 3px;
  background: rgba(255, 255, 255, 1);
}
</style>