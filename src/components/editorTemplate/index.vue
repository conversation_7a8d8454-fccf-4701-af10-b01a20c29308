<template>
  <div style="border: 1px solid #ccc;z-index: 999;">
    <Toolbar
      style="border-bottom: 1px solid #ccc"
      :editor="editor"
      :defaultConfig="toolbarConfig"
      :mode="mode"
    />
    <Editor
      style="overflow-y: hidden;"
      :style="{ height: height + 'px' }"
      v-model="docHtml"
      :defaultConfig="editorConfig"
      :mode="mode"
      @onCreated="onCreated"
      @onChange="onChange"
    />
  </div>
</template>
<script>
import Vue from "vue";
import { Editor, Toolbar } from "@wangeditor/editor-for-vue";
import { DomEditor } from "@wangeditor/editor";
import request from "@/utils/request";
import lrz from "lrz";
export default Vue.extend({
  name: "MyEditor",
  components: { Editor, Toolbar },
  props: {
    html: {
      type: String,
      required: false,
      default: "",
    },
    height: {
      type: [Number, String],
      required: false,
      default: 500,
    },
     excludeKeys: {
      type: Array,
      default: () => []
     },
     toolbarKeys:{
       type: Array,
      default: () => [
          "headerSelect",
          "|",
          "bold",
          "underline",
          "italic",
          {
            iconSvg:"<svg viewBox=\"0 0 1024 1024\"><path d=\"M204.8 505.6m-76.8 0a76.8 76.8 0 1 0 153.6 0 76.8 76.8 0 1 0-153.6 0Z\"></path><path d=\"M505.6 505.6m-76.8 0a76.8 76.8 0 1 0 153.6 0 76.8 76.8 0 1 0-153.6 0Z\"></path><path d=\"M806.4 505.6m-76.8 0a76.8 76.8 0 1 0 153.6 0 76.8 76.8 0 1 0-153.6 0Z\"></path></svg>",
            key: "group-more-style",
            menuKeys: ['through', 'code', 'sup', 'sub', 'clearStyle'],
            title: "更多"
          },
          "color",
          "bgColor",
          "|",
          "fontSize",
          "fontFamily",
          "lineHeight",
          "|",
          "bulletedList",
          "numberedList",
          "todo",
          {
            iconSvg: "<svg viewBox=\"0 0 1024 1024\"><path d=\"M768 793.6v102.4H51.2v-102.4h716.8z m204.8-230.4v102.4H51.2v-102.4h921.6z m-204.8-230.4v102.4H51.2v-102.4h716.8zM972.8 102.4v102.4H51.2V102.4h921.6z\"></path></svg>",
            key: "group-justify",
             menuKeys: ['justifyLeft', 'justifyRight', 'justifyCenter', 'justifyJustify'],
             title: "对齐"
          },
          {
          iconSvg: "<svg viewBox=\"0 0 1024 1024\"><path d=\"M0 64h1024v128H0z m384 192h640v128H384z m0 192h640v128H384z m0 192h640v128H384zM0 832h1024v128H0z m0-128V320l256 192z\"></path></svg>",
          key: "group-indent",
          menuKeys:['indent', 'delIndent'],
          title: "缩进"
          },
          "|",
          "emotion",
          "insertLink",
          {
            iconSvg: "<svg viewBox=\"0 0 1024 1024\"><path d=\"M959.877 128l0.123 0.123v767.775l-0.123 0.122H64.102l-0.122-0.122V128.123l0.122-0.123h895.775zM960 64H64C28.795 64 0 92.795 0 128v768c0 35.205 28.795 64 64 64h896c35.205 0 64-28.795 64-64V128c0-35.205-28.795-64-64-64zM832 288.01c0 53.023-42.988 96.01-96.01 96.01s-96.01-42.987-96.01-96.01S682.967 192 735.99 192 832 234.988 832 288.01zM896 832H128V704l224.01-384 256 320h64l224.01-192z\"></path></svg>",
            key: "group-image",
            menuKeys:['insertImage', 'uploadImage'],
            title: "图片"
          },
          {
            iconSvg: "<svg viewBox=\"0 0 1024 1024\"><path d=\"M981.184 160.096C837.568 139.456 678.848 128 512 128S186.432 139.456 42.816 160.096C15.296 267.808 0 386.848 0 512s15.264 244.16 42.816 351.904C186.464 884.544 345.152 896 512 896s325.568-11.456 469.184-32.096C1008.704 756.192 1024 637.152 1024 512s-15.264-244.16-42.816-351.904zM384 704V320l320 192-320 192z\"></path></svg>",
            key: "group-video",
            menuKeys:['insertVideo', 'uploadVideo'],
            title: "视频"
          },
          "insertTable",
          "codeBlock",
          "divider",
          "|",
          "undo",
          "redo",
          "|",
          "uploadImage",
          "fullScreen",
        ],
     }
  },
  data() {
    return {
      editor: null,
      // html: '<p>hello</p>',
      docHtml: this.html,
      toolbarConfig: {
        /* 显示哪些菜单，如何排序、分组 */
        toolbarKeys: this.toolbarKeys,
        excludeKeys: this.excludeKeys /* 隐藏哪些菜单 */,
      },
      editorConfig: {
        placeholder: "请输入内容...",
        MENU_CONF: {
          uploadImage: {
            customUpload: this.updateImg,
          },
          uploadVideo:{
            customUpload:this.updataVideo
          }
        },
      },
      mode: "default", // or 'simple'
    };
  },
  watch: {
    html: function (val, oldVal) {
      this.docHtml = val;
    },
  },
  methods: {
    onCreated(editor) {
      this.editor = Object.seal(editor); // 一定要用 Object.seal() ，否则会报错
    },
    onChange(editor) {
      // console.log("onChange", editor.getHtml() ); // onChange 时获取编辑器最新内容
      this.$emit("changeHtml", editor.getHtml())
          // const toolbar = DomEditor.getToolbar(editor)
          // console.log("工具栏配置", toolbar.getConfig().toolbarKeys ); // 工具栏配置
        // console.log("editor.getAllMenuKeys()", editor.getAllMenuKeys());
    },
    getEditorText() {
      const editor = this.editor;
      if (editor == null) return;
      // console.log(editor.getText()); // 执行 editor API
    },
    printEditorHtml() {
      const editor = this.editor;
      if (editor == null) return;
      // console.log(editor.getHtml()); // 执行 editor API
    },
    updateImg(file, insertFn) {
      lrz(file).then((rst) => {
         insertFn(rst.base64, '', '')
      });
      //   url": "xxx", // 图片 src ，必须
      // "alt": "yyy", // 图片描述文字，非必须
      // "href": "zzz" // 图片的链接，非必须
      //    insertFn(url, alt, href);
    },
    updataVideo(file, insertFn){
        // insertFn(file.lastModifiedDate)
    }
  },
  mounted() {},
  beforeDestroy() {
    const editor = this.editor;
    if (editor == null) return;
    editor.destroy(); // 组件销毁时，及时销毁编辑器
  },
});
</script>
<style src="@wangeditor/editor/dist/css/style.css"></style>