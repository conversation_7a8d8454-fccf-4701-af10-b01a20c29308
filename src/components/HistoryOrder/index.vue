<template>
  <div class="orderBox">
    <div id="histoyrOrder" style="height: 500px; width: 100%"></div>
  </div>
</template>

<script>
export default {
  props: ["historyData"],
  data() {
    return {};
  },
  mounted() {},
  methods: {
    initEchart(data) {
      var that = this;
      document
        .getElementById("histoyrOrder")
        .removeAttribute("_echarts_instance_");
      const container = document.getElementById("histoyrOrder");
      const myChart = this.$echarts.init(container);
      var xdata = [];
      var ydata = [];
      var zdata = [];
      for (var i = 0; i < data.length; i++) {
        xdata.push(data[i].refno);
        ydata.push(!!data[i].taxprice ? data[i].taxprice : 0);
        zdata.push(!!data[i].price ? data[i].price : 0);
      }
      var option = {
        title: {
          text: "历史订单价格",
        },
        tooltip: {
          trigger: "axis",
          formatter: function (params) {
            const data = that.historyData.filter(
              (i) => i.refno == params[0].name
            );
            if (data.length > 0) {
              return (
                "<div>" +
                "订单编号: " +
                data[0].refno +
                "<br>" +
                "订单日期: " +
                that.dateFormats(data[0].billdate) +
                "<br>" +
                "未税单价: " +
                data[0].price +
                '<span style="color: #00B83F;">' +
                "</span>元" +
                "<br>" +
                "含税单价: " +
                data[0].taxprice +
                '<span style="color: #00B83F;">' +
                "</span>元" +
                "</div>"
              );
            }
          },
        },
        legend: {
          data: ["含税单价", "未税单价"],
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: xdata,
        },
        yAxis: {
          type: "value",
        },
        grid: [
          {
            left: "5%",
            right: "5%",
          },
        ],
        series: [
          {
            name: "含税单价",
            type: "line",
            data: ydata,
          },
          {
            name: "未税单价",
            type: "line",
            data: zdata,
          },
        ],
      };
      myChart.setOption(option);
      window.addEventListener("resize", function () {
        myChart.resize();
      });
      //获取点击节点的值
      myChart.on("click", (params) => {
        //含税价格
        if (params.seriesIndex === 0) {
          that.$emit("getHistoryPrice", params.value, "含税");
        }
        //未税价
        if (params.seriesIndex === 1) {
          that.$emit("getHistoryPrice", params.value, "未税");
        }
      });
    },
    dateFormats(dataStr) {
      if (!dataStr) {
        return;
      }
      var dt = new Date(dataStr);
      var y = dt.getFullYear();
      var m = (dt.getMonth() + 1).toString().padStart(2, "0");
      var d = dt.getDate().toString().padStart(2, "0");
      var hh = dt.getHours().toString().padStart(2, "0");
      var mm = dt.getMinutes().toString().padStart(2, "0");
      var ss = dt.getSeconds().toString().padStart(2, "0");
      return `${y}-${m}-${d} `;
    },
  },
};
</script>

<style lang="scss" scoped>
.orderBox {
  width: 70vw;
  height: 50vh;
}
</style>>

