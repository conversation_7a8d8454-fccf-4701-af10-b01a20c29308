<template>
  <div class="help-warp">
    <div class="help-header">
      <span>{{formdata.introname?formdata.introname:'功能简介'}}</span>
    </div>
    <div class="help-content">
      <div v-if="formdata.introcontent">
        <div v-html="formdata.introcontent"></div>
      </div>
      <div v-else class="noData" style="font-size: 22px">暂无内容</div>
    </div>
  </div>
</template>
<script>
import request from "@/utils/request";
export default {
    props:['code'],
  data() {
    return {
      formdata: {},
    };
  },
  created() {
      this.bindData()
  },
  methods: {
    bindData() {
        request.get('/system/SYSM06B7/getEntityByCode?key='+this.code).then(res=>{
            if(res.data.code==200){
              if(res.data.data==null){
                this.formdata={
                  introname:"",
                  introcontent:""
                }
                return
              }
                this.formdata=res.data.data
            }else{
                this.$message.warning(res.data.msg||"获取功能简介失败")
            }
        }).catch(er=>{
            this.$message.error(er||"请求错误")
        })
    },
  },
};
</script>
<style lang="scss" scoped>
@import "@/styles/mycustom.scss";
.help-warp {
  //  border-left: #60b8ff 1px solid;
  //  min-width: 260px;
  border-top: 1px solid #ebeef5;
}
.help-header {
  //    background: #60b8ff;
  height: 34px;
  line-height: 34px;
  text-align: center;
  color: #72767b;
  border-bottom: 1px solid #ebeef5;
  span {
    font-size: 17px;
    font-weight: 700;
  }
}
.help-content {
  min-height: 50vh;
  padding: 20px;
  box-sizing: border-box;
  position: relative;
}
</style>