// sidebar
$menuText:#bfcbd9;
$menuActiveText:#bfcbd9;
$subMenuActiveText:#f4f4f5; //https://github.com/ElemeFE/element/issues/12951

//$menuBg:#409EFF;//原本的颜色
$menuBg:#366CB3;
$menuHover:#175385;

//$subMenuBg:#1f2d3d;//原本的颜色
$subMenuBg:#366CB3;
//$subMenuHover:#001528;//原本的颜色
$subMenuHover:#366CB3;

$sideBarWidth: 100px;

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
}
