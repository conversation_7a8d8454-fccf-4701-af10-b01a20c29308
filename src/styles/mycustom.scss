.circular {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin: 0 auto;
}

.fontWeight {
  font-weight: bold
}

::v-deep .wpselect {
  z-index: -1;
  .el-input--suffix .el-input__inner {
    padding-right: 0 !important;
    padding: 0;
  }
}
::v-deep .writePacksn {
  font-size: 15px;
  color: #409eff;
  cursor: pointer;
  height: 100%;
  line-height: 2;
  padding: 0 4px;
}

::v-deep .textunderline {
  text-decoration: underline;
  cursor: pointer;
}
::v-deep .textlinethrough {
  text-decoration: line-through
}

::v-deep .textborder {
  border: 1px solid;
  color: #969696;
  padding: 2px 10px;
  background: #fcfcfc;
  font-size: 14px;
  border-radius: 3px;
  display: inline-block;
  line-height: 20px;
}

::v-deep .textborder-normal {
  border: 1px solid;
  color: #969696;
  padding: 2px 10px;
  background: #fcfcfc;
  font-size: 14px;
  border-radius: 2px;
  display: inline-block;
  line-height: 20px;
}

::v-deep .textborder-red {
  border: 1px solid;
  color: #F56C6C;
  padding: 2px 10px;
  background: #fcf0f0;
  font-size: 14px;
  border-radius: 2px;
  display: inline-block;
  line-height: 20px;
}

::v-deep .textborder-green {
  border: 1px solid;
  color: #6dc76a;
  padding: 2px 10px;
  background: #f4f8f4;
  font-size: 14px;
  border-radius: 2px;
  display: inline-block;
  line-height: 20px;
}

::v-deep .textborder-grey {
  border: 1px solid #686868;
  color: #686868;
  padding: 2px 10px;
  background: #f0eeee;
  font-size: 14px;
  border-radius: 2px;
  display: inline-block;
  line-height: 20px;
}

::v-deep .textborder-blue {
  border: 1px solid #409eff;
  color: #409eff;
  padding: 2px 10px;
  background: #ebf3fa;
  font-size: 14px;
  border-radius: 2px;
  display: inline-block;
  line-height: 20px;
}

::v-deep .textborder-black {
  border: 1px solid #272727;
  color: #272727;
  padding: 2px 10px;
  font-size: 14px;
  border-radius: 2px;
  display: inline-block;
  line-height: 20px;
}

::v-deep .textborder-yellow {
  border: 1px solid #e6a23c;
  color: #e6a23c;
  padding: 2px 10px;
  background: #faf4ee;
  font-size: 14px;
  border-radius: 2px;
  display: inline-block;
  line-height: 20px;
}

::v-deep .text-red {
  color: #F56C6C;
}

::v-deep .text-green {
  color: #6dc76a;
}

::v-deep .text-grey {
  color: #9e9e9e;
}

::v-deep .text-yellow {
  color: #ffeb3b;
}

::v-deep .text-orange {
  color: #ff9800;
}

::v-deep .text-blue {
  color: #409eff;
}

::v-deep .text-purple {
  color: purple;
}

::v-deep .text-peru {
  color: peru;
}

::v-deep .text-info {
  color: #909399;
}

::v-deep .info {
  background: #909399;
}

::v-deep .red {
  background: #F56C6C;
}

::v-deep .green {
  background: #6dc76a;
}

::v-deep .grey {
  background: #9e9e9e;
}

::v-deep .yellow {
  background: #ffeb3b;
}

::v-deep .orange {
  background: #ff9800;
}

::v-deep .blue {
  background: #409eff;
}

::v-deep .orange_red {
  background: #fa7753;
}

::v-deep .sky_blue {
  background: #65a6ff;
}

::v-deep .shen_green {
  background: #9cd159;
}

::v-deep .bg-yellow {
  background: #fc0
}

::v-deep .bg-qing {
  background: #36c6d3;
}

::v-deep .bg-purple {
  background: #9049f3;
}
.num_tip{
  color: #fff;
    padding: 0 4px;
    border-radius: 2px;
    font-size: 12px;
}
::v-deep .noData {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 28px;
  color: #c0c4cc;
}

::v-deep .ellipsis {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-wrap: break-word;
}

::v-deep .ellipsis-content {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

// 自动换行
::v-deep .breakword {
  white-space: pre-wrap;
  word-break: break-all;
  word-wrap: break-word;
}
::v-deep .writePacksn {
  font-size: 15px;
  color: #409eff;
  cursor: pointer;
  // border-left: 1px solid #dcdfe6;
  height: 100%;
  line-height: 2;
  padding: 0 4px;
}

// 不可选择-拷贝
::v-deep .breakword {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
}

.myscrollbar::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 6px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 8px;
}

.myscrollbar::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 10px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.3);
  background: #ccc;
}

.myscrollbar::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 3px;
  background: rgba(255, 255, 255, 1);
}

.el_scrollbar {
  ::v-deep .el-table__body-wrapper::-webkit-scrollbar {
    width: 6px;
    /*高宽分别对应横竖滚动条的尺寸*/
    height: 8px;
    background-color: #ebeef5;
  }

  ::v-deep .el-table__body-wrapper::-webkit-scrollbar-thumb {
    border-radius: 10px;
    box-shadow: inset 0 0 6px rgba(0, 0, 0, .3);
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, .3);
    background-color: #ccc;
  }

  ::v-deep .el-table__body-wrapper::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 3px;
    background: rgba(255, 255, 255, 1);
  }
}