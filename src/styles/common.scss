.autocompleteInput {
  min-width: 100px !important;
}

span.el-form-item__label {
  min-width: 140px;
  text-align: left;
}

.el-dialog__body {
  padding: 25px 20px !important;
  padding-top: 15px !important;
}

.formTitle {
  color: #555;
  font-size: 20px;
  margin-bottom: 10px;
  margin-top: 0px;
  margin-left: 20px;
}

/* .listheader .iShowBtn .el-button>i{
    font-weight: bold;
  } */
.footFormContent {
  .el-form-item {
    margin-bottom: 0 !important;
  }
}

.el-table__footer-wrapper tbody td {
  padding: 8px 0;
}

.el-table td,
.el-table th {
  min-width: 80px;
  height: 34px;

  .el-button--small,
  .el-button--small.is-round {
    padding: 5px 8px;
  }
}

.pdfDialog .el-dialog__body {
  padding: 15px 20px;
}

.el-table .caret-wrapper {
  width: 22px;
}

.el-button--text {
  user-select: unset;
}

.monthPickerStyle {
  .el-date-picker__header--bordered {
    display: none !important;
  }
}

.ve-contextmenu-popper {
  // spu 选择内容过多 显示不全
  max-height: 340px !important;
  overflow: auto !important;
}

.curssor {
  cursor: pointer;
}

/**flex css  */
.flex {
  display: flex;
  flex-wrap: wrap;
}

.f-1 {
  flex: 1;
}

.f-d-c {
  flex-direction: column;
}

.j-c {
  justify-content: center;
}

.a-c {
  align-items: center;
}

.j-s {
  justify-content: space-between;
}

.j-start {
  justify-content: flex-start;
}

.j-end {
  justify-content: flex-end;
}

.p-r {
  position: relative;
}

.p-a {
  position: absolute;
}

// ve-table
.ve-table td {
  padding: 0 !important;
}

.ve-table-body-tr,
.ve-table-footer-tr {
  height: 32px !important;
}

.el-input--small .el-input__inner {
  height: 30px !important;
  line-height: 30px !important;
}

.ve-table-content-wrapper,
.ve-table-content {
  height: 100% !important;
}

.ve-table-body::after {
  content: "" !important;
}

.ve-table-content .ve-table-body tr:nth-last-child(1) td {
  border-bottom: 1px solid #eee !important;
}
.ve-table.ve-table-border-around{
  height: 100% !important;
}
.my-dialogDragZoom{
  display: flex;
  flex-direction: column;
  height: 650px;
   .el-dialog__body{
    flex:1;
    overflow: auto;
   
  }
}
/* 全局禁用文本选择 */  
.no-select {
  user-select: none !important;
  -webkit-user-select: none  !important; /* Safari */
  -moz-user-select: none  !important; /* Firefox */
  -ms-user-select: none  !important; /* IE/Edge */
}