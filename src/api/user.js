import request from '@/utils/request'

export function login(data) {
  return request({
    url: '/auth/login', // /auth
    method: 'post',
    data: { UserName: data.UserName, Password: data.Password }
  })
}

export function getInfo(token) {
  // var params = new URLSearchParams();
  // params.append("token", token);
  return request({
    url: '/auth/Login/getUserInfo',
    method: 'post'
  })
}

export function logout() {
  return request({
    url: '/auth/logout',
    method: 'DELETE'
  })
}
