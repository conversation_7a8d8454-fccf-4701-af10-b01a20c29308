import InitApp from '@/micro' // 放在main首行
import Vue from 'vue'
import 'normalize.css/normalize.css'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
Vue.use(ElementUI)

import '@/styles/index.scss'
import '@/utils/filter'
import '@/utils/commonLog.js'
import omsRoutes from './router/omsRoutes'
import './utils/directives.js'
import '@/icons'
import axios from 'axios'
import VueAxios from 'vue-axios'
Vue.use(VueAxios, axios)
import request from '@/utils/request'
Vue.prototype.$request = request
import dataV from '@jiaminghi/data-view'
Vue.use(dataV)
import VCharts from 'v-charts'
Vue.use(VCharts)
import echarts from 'echarts'
Vue.prototype.$echarts = echarts
import wsConnection from './utils/socket'
Vue.prototype.$setWs = wsConnection
import inksRefnoView from '@/components/setRefno/index.vue'
Vue.component('inksRefnoView', inksRefnoView)
import inksMachuidView from '@/components/common/mchuidView.vue'
Vue.component('inksMachuidView', inksMachuidView)
import inksAutoComplete from '@/components/autoComplete/index.vue'
Vue.component('inksAutoComplete', inksAutoComplete)
import BillState from '@/components/BillState/index.vue'
Vue.component('BillState', BillState)
import rangePicker from '@/components/rangePicker/index.vue'
Vue.component('rangePicker', rangePicker)
import '@inksyun/webcomp/webcomp.css'
import inksutils from '@inksyun/utils'
import { getColumn, initColumns } from '@/utils/getcolumn.js'
Vue.prototype.$inksutils = inksutils
Vue.prototype.$tableSort = inksutils.tools.tableSort
Vue.prototype.$countInput = inksutils.count.countInput
Vue.prototype.$fomatFloat = inksutils.count.fomatFloat
Vue.prototype.$amountFloat = inksutils.count.amountFloat
Vue.prototype.$getParam = inksutils.count.getParam
Vue.prototype.$getPageInfo = inksutils.getpageinfo.getPageInfo
Vue.prototype.$getColumn = getColumn
Vue.prototype.$initColumns = initColumns
Vue.prototype.$apiCache = inksutils.apiCache.default
Vue.use(inksutils.fun.default)
import { install } from '@inksyun/webcomp'
Vue.use(install)
import 'vue-easytable/libs/theme-default/index.css'
import VueEasytable from 'vue-easytable'
import { VeLocale } from 'vue-easytable'
import zhCN from 'vue-easytable/libs/locale/lang/zh-CN.js'
VeLocale.use(zhCN)
Vue.use(VueEasytable)
import VueRouter from 'vue-router'
Vue.use(VueRouter)

Vue.directive('preventReClick', {
  inserted(el, binding) {
    el.addEventListener('click', () => {
      if (!el.disabled) {
        el.disabled = true
        setTimeout(() => {
          el.disabled = false
        }, binding.value || 3000)
      }
    })
  }
})

new InitApp('/E08/', omsRoutes)
