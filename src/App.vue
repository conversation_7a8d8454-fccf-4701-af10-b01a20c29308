<template>
  <div id="app">
    <keep-alive> <router-view v-if="isRouterAlive"></router-view></keep-alive>
  </div>
</template>

<script>
import request from "@/utils/request";
import Cookies from "js-cookie";
export default {
  name: "App",
  provide() {
    //父组件中通过provide来提供变量，在子组件中通过inject来注入变量。
    return {
      reload: this.reload,
    };
  },
  data() {
    return {
      isRouterAlive: true, //控制视图是否显示的变量
      refreshTokenTime: null,
    };
  },
  created() {
    // 在页面加载时读取sessionStorage里的状态信息
    if (sessionStorage.getItem("store")) {
      this.$store.replaceState(
        Object.assign(
          {},
          this.$store.state,
          JSON.parse(sessionStorage.getItem("store"))
        )
      );
    }
    // 在页面刷新时将vuex里的信息保存到sessionStorage里
    // beforeunload事件在页面刷新时先触发
    // var baseapi=Cookie.get('baseApi')
    window.addEventListener("beforeunload", () => {
      sessionStorage.setItem("store", JSON.stringify(this.$store.state));
    });
  },
  mounted() {
    request.defaults.baseURL = Cookies.get("baseApi", {
      domain: window.location.hostname,
    });
    this.startTime();
  },
  methods: {
    reload() {
      this.isRouterAlive = false; //先关闭，
      this.$nextTick(function () {
        this.isRouterAlive = true; //再打开
      });
    },
    startTime() {
      this.refreshTokenTime = setInterval(this.timeChange, 2 * 60 * 60 * 1000);
    },
    timeChange() {
      request.get("/auth/renewal");
    },
  },
  beforeDestroy() {
    clearInterval(this.refreshTokenTime);
  },
};
</script>
<style lang="scss">
@import "@/styles/common.scss";
</style>
