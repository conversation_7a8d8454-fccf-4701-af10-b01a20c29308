# 基础镜像
FROM nginx
# author
MAINTAINER omsweb

# 挂载目录
VOLUME /home/<USER>/projects/ui-omsweb
# 创建目录
RUN mkdir -p /home/<USER>/projects/ui-omsweb
# 指定路径
WORKDIR /home/<USER>/projects/ui-omsweb
# 复制conf文件到路径
COPY ./conf/nginx.conf /etc/nginx/nginx.conf
# 复制html文件到路径
COPY ./html/dist /home/<USER>/projects/ui-omsweb
# 复制ssl文件到路径
COPY ./ssl/dev.inksyun.com_ssl.crt /usr/local/nginx/cert/server.crt
COPY ./ssl/dev.inksyun.com_ssl.key /usr/local/nginx/cert/server.key