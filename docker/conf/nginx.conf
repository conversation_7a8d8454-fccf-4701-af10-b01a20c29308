worker_processes  1;

events {
    worker_connections  1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;
    sendfile        on;
    keepalive_timeout  65;

    server {
        listen       80;
        server_name  localhost;

		location / {
            root   /home/<USER>/projects/ui-omsweb;
			try_files $uri $uri/ /index.html;
            index  index.html index.htm;
            add_header Access-Control-Allow-Origin  "*"                   always;
        }

        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
    }
}
