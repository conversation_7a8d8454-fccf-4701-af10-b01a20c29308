apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: mf-cusweb-k1039-cost
  name: mf-cusweb-k1039-cost
  namespace: inksdev-mf   #一定要写名称空间
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  selector:
    matchLabels:
      app: mf-cusweb-k1039-cost
  strategy:
    rollingUpdate:
      maxSurge: 50%
      maxUnavailable: 50%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: mf-cusweb-k1039-cost
    spec:
      imagePullSecrets:
        - name: inks-docker-auth  #提前在项目下配置访问阿里云的账号密码
      containers:
        - image: $REGISTRY/$DOCKERHUB_NAMESPACE/mf-cusweb-k1039-cost :SNAPSHOT-$BUILD_NUMBER
          imagePullPolicy: Always
          name: mf-cusweb-k1039-cost
          ports:
            - containerPort: 80
              protocol: TCP
          env: #appconfig模板，网址和数字需加引号转字符
            - name: BASE_URL
              value: 'http://dev.inksyun.com:31080'
            - name: SYSTEM_TITLE
              value: mf-sale销售模块
            - name: SYSTEM_CODE
              value: inksoms
            - name: DEPLOY_MODEL
              value: standalone
            - name: DEPLOY_ENV
              value: dev
          resources:
            limits:
              cpu: 100m
              memory: 100Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: mf-cusweb-k1039-cost
  name: mf-cusweb-k1039-cost
  namespace: inksdev-mf
spec:
  ports:
    - name: http
      port: 80
      protocol: TCP
      targetPort: 80
      nodePort: 32111
  selector:
    app: mf-cusweb-k1039-cost
  sessionAffinity: None
  type: NodePort
